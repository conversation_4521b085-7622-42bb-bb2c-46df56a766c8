{"华安证券": {"customerName": "华安证券", "customerId": 1, "departments": [{"id": "dept_001", "name": "总部", "type": "总部", "manager": "张总", "employeeCount": 50, "description": "华安证券总部", "parentId": null, "children": [{"id": "dept_002", "name": "信息技术部", "type": "部门", "manager": "李部长", "employeeCount": 15, "description": "负责公司IT系统建设和维护", "parentId": "dept_001", "children": [{"id": "dept_003", "name": "系统开发组", "type": "小组", "manager": "王组长", "employeeCount": 4, "description": "负责系统开发工作", "parentId": "dept_002", "children": [], "employees": [{"id": "emp_001", "name": "王组长", "gender": "男", "position": "开发组长", "description": "<p>负责系统开发组的日常管理和技术指导工作。</p><p><strong>主要职责：</strong></p><ul><li>制定开发计划和技术方案</li><li>代码审查和质量控制</li><li>团队技术培训</li></ul>", "createdAt": "2025-06-12T04:30:00.000Z", "updatedAt": "2025-06-12T04:30:00.000Z"}, {"id": "emp_002", "name": "张三", "gender": "男", "position": "高级开发工程师", "description": "<p>专注于后端系统开发，具有丰富的Java和Python开发经验。</p><p><strong>技能专长：</strong></p><ul><li>Spring Boot框架</li><li>微服务架构</li><li>数据库设计</li></ul>", "createdAt": "2025-06-12T04:30:00.000Z", "updatedAt": "2025-06-12T04:30:00.000Z"}, {"id": "emp_003", "name": "李四", "gender": "女", "position": "前端开发工程师", "description": "<p>负责前端界面开发和用户体验优化。</p><p><strong>技能专长：</strong></p><ul><li>React/Vue.js</li><li>UI/UX设计</li><li>移动端开发</li></ul>", "createdAt": "2025-06-12T04:30:00.000Z", "updatedAt": "2025-06-12T04:30:00.000Z"}, {"id": "emp_1749713274839_t7we8qc", "name": "测试员工", "gender": "男", "position": "测试工程师", "description": "<p>这是一个测试员工</p>", "createdAt": "2025-06-12T07:27:54.839Z", "updatedAt": "2025-06-12T07:27:54.839Z"}], "updatedAt": "2025-06-12T07:27:54.839Z"}, {"id": "dept_004", "name": "运维组", "type": "小组", "manager": "赵组长", "employeeCount": 7, "description": "负责系统运维工作", "parentId": "dept_002", "children": [], "employees": [{"id": "emp_004", "name": "赵组长", "gender": "男", "position": "运维组长", "description": "<p>负责系统运维组的管理和运维体系建设。</p><p><strong>主要职责：</strong></p><ul><li>系统监控和故障处理</li><li>运维流程制定</li><li>安全管理</li></ul>", "createdAt": "2025-06-12T04:30:00.000Z", "updatedAt": "2025-06-12T04:30:00.000Z"}, {"id": "emp_005", "name": "王五", "gender": "男", "position": "系统运维工程师", "description": "<p>专注于Linux系统运维和自动化部署。</p><p><strong>技能专长：</strong></p><ul><li>Linux系统管理</li><li>Docker容器化</li><li>CI/CD流水线</li></ul>", "createdAt": "2025-06-12T04:30:00.000Z", "updatedAt": "2025-06-12T04:30:00.000Z"}]}]}, {"id": "dept_005", "name": "业务部", "type": "部门", "manager": "陈部长", "employeeCount": 25, "description": "负责公司核心业务", "parentId": "dept_001", "children": [{"id": "dept_006", "name": "投资银行组", "type": "小组", "manager": "刘组长", "employeeCount": 12, "description": "负责投资银行业务", "parentId": "dept_005", "children": [], "employees": []}, {"id": "dept_007", "name": "资产管理组", "type": "小组", "manager": "孙组长", "employeeCount": 13, "description": "负责资产管理业务", "parentId": "dept_005", "children": [], "employees": []}]}, {"id": "dept_008", "name": "财务部", "type": "部门", "manager": "周部长", "employeeCount": 10, "description": "负责公司财务管理", "parentId": "dept_001", "children": []}, {"id": "dept_1749712011810_uhmbsv1rn", "name": "测试部门", "type": "部门", "manager": "测试经理", "employeeCount": 5, "description": "这是一个测试部门", "parentId": "dept_001", "children": [], "createdAt": "2025-06-12T07:06:51.810Z", "updatedAt": "2025-06-12T07:06:51.810Z"}]}], "createdAt": "2025-06-12T04:30:00.000Z", "updatedAt": "2025-06-29T05:28:45.866Z"}, "国元证券": {"customerName": "国元证券", "customerId": 2, "departments": [{"id": "dept_101", "name": "总部", "type": "总部", "manager": "马总", "employeeCount": 40, "description": "国元证券总部", "parentId": null, "children": [{"id": "dept_102", "name": "技术部", "type": "部门", "manager": "吴部长", "employeeCount": 12, "description": "负责技术支持和系统维护", "parentId": "dept_101", "children": []}, {"id": "dept_103", "name": "市场部", "type": "部门", "manager": "郑部长", "employeeCount": 18, "description": "负责市场拓展和客户关系", "parentId": "dept_101", "children": []}, {"id": "dept_104", "name": "风控部", "type": "部门", "manager": "钱部长", "employeeCount": 10, "description": "负责风险控制和合规管理", "parentId": "dept_101", "children": []}]}], "createdAt": "2025-06-12T04:30:00.000Z", "updatedAt": "2025-06-12T04:30:00.000Z"}, "华福证券": {"customerName": "华福证券", "customerId": 4, "departments": [{"id": "dept_1749712628565_qavuvkqs2", "name": "客群发展部", "type": "事业部", "manager": "林喆", "employeeCount": 0, "description": "", "parentId": null, "children": [{"id": "dept_1749712648413_k6cx4h30a", "name": "基础客群部", "type": "部门", "manager": "郑相君", "employeeCount": 0, "description": "", "parentId": "dept_1749712628565_qavuvkqs2", "children": [], "createdAt": "2025-06-12T07:17:28.413Z", "updatedAt": "2025-06-12T07:17:28.413Z"}], "createdAt": "2025-06-12T07:17:08.565Z", "updatedAt": "2025-06-12T07:17:08.565Z"}], "createdAt": "2025-06-12T07:17:08.564Z", "updatedAt": "2025-06-12T07:17:28.413Z"}}