const express = require('express');
const path = require('path');
const config = require('./config/database');
const mongoManager = require('./utils/mongoManager');

// 创建Express应用实例
const app = express();

// 从配置中获取端口
const PORT = config.port;

// 中间件配置
app.use(express.json()); // 解析JSON请求体
app.use(express.urlencoded({ extended: true })); // 解析URL编码的请求体
app.use(express.static(path.join(__dirname, 'public'))); // 静态文件服务

// 根路径重定向到登录页面
app.get('/', (req, res) => {
  res.redirect('/login.html');
});

// 导入路由
const indexRouter = require('./routes/index');
const apiRouter = require('./routes/api');

// 使用路由
app.use('/', indexRouter);
app.use('/api', apiRouter);

// 404错误处理
app.use((req, res, next) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  });
});

// 全局错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production' ? 'Something went wrong!' : err.message
  });
});

// 启动服务器
async function startServer() {
  try {
    console.log('🔄 正在启动服务器...');
    console.log('');

    // 初始化MongoDB连接
    await mongoManager.connect();
    console.log('🔗 MongoDB连接已建立');

    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log('');
      console.log(`🚀 Server is running on http://localhost:${PORT}`);
      console.log(`📁 Static files served from: ${path.join(__dirname, 'public')}`);
      console.log(`🔧 Environment: ${config.environment}`);

      const { mongodb } = config;
      const connectionInfo = mongodb.username ?
        `${mongodb.host}:${mongodb.port}/${mongodb.database} (认证)` :
        `${mongodb.host}:${mongodb.port}/${mongodb.database}`;
      console.log(`💾 数据存储: MongoDB (${connectionInfo})`);
    });
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error.message);

    // 根据环境决定是否继续启动
    if (config.mongodb.isProduction()) {
      console.error('🚫 生产环境必须连接到MongoDB，服务器启动失败');
      process.exit(1);
    } else {
      console.log('🔄 开发环境：尝试使用JSON文件存储启动服务器...');

      // 开发环境如果MongoDB连接失败，仍然启动服务器（使用JSON文件存储）
      app.listen(PORT, () => {
        console.log('');
        console.log(`🚀 Server is running on http://localhost:${PORT} (JSON文件存储模式)`);
        console.log(`📁 Static files served from: ${path.join(__dirname, 'public')}`);
        console.log(`🔧 Environment: ${config.environment}`);
        console.log(`💾 数据存储: JSON文件 (回退模式)`);
        console.log('⚠️  注意：当前使用JSON文件存储，数据不会持久化到数据库');
      });
    }
  }
}

startServer();

module.exports = app;
