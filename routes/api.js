const express = require('express');
const mongoManager = require('../utils/mongoManager');
const auth = require('../middleware/auth');
const router = express.Router();

// 数据存储变量
let salesFunnelData = [];
let customerDetailsData = {};
let organizationData = {};
let businessManagersData = [];
let paymentRecordsData = [];
let qaData = [];
let executionPlansData = [];

// 初始化数据加载函数 - 只使用MongoDB
async function initializeData() {
    try {
        console.log('🔄 正在从MongoDB加载数据...');

        // 确保MongoDB连接
        await mongoManager.connect();

        salesFunnelData = await mongoManager.loadSalesData();
        customerDetailsData = await mongoManager.loadCustomerData();
        organizationData = await mongoManager.loadOrganizationData();
        businessManagersData = await mongoManager.loadBusinessManagersData();
        paymentRecordsData = await mongoManager.loadPaymentRecordsData();
        qaData = await mongoManager.loadQAData();
        executionPlansData = await mongoManager.loadExecutionPlansData();

        console.log('✅ 数据从MongoDB加载完成');
        console.log(`📊 已加载 ${salesFunnelData.length} 条销售数据`);
        console.log(`👥 已加载 ${Object.keys(customerDetailsData).length} 个客户的详细数据`);
        console.log(`🏢 已加载 ${Object.keys(organizationData).length} 个客户的组织架构数据`);
        console.log(`💰 已加载 ${paymentRecordsData.length} 条回款记录`);
        console.log(`📚 已加载 ${qaData.length} 条Q&A数据`);
        console.log(`📋 已加载 ${executionPlansData.length} 条执行计划数据`);
    } catch (error) {
        console.error('❌ MongoDB数据加载失败:', error.message);
        console.error('💡 请确保MongoDB服务正在运行并且连接配置正确');

        // 不再回退到JSON文件，而是初始化为空数据
        salesFunnelData = [];
        customerDetailsData = {};
        organizationData = {};
        businessManagersData = [];
        paymentRecordsData = [];
        qaData = [];
        executionPlansData = [];

        console.log('⚠️  已初始化为空数据，请检查MongoDB连接');
    }
}

// 立即初始化数据
initializeData();

// ==================== 认证 API ====================

// 用户登录
router.post('/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    const user = await auth.authenticateUser(username, password);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    const token = auth.generateToken(user);

    res.json({
      success: true,
      message: '登录成功',
      token: token,
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        permission: user.permission,
        customers: user.customers || []
      }
    });
  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 验证token
router.get('/auth/verify', auth.authenticateToken, (req, res) => {
  res.json({
    success: true,
    user: {
      id: req.user.id,
      username: req.user.username,
      name: req.user.name,
      permission: req.user.permission,
      customers: req.user.customers || []
    }
  });
});

// 用户登出
router.post('/auth/logout', (req, res) => {
  // 由于使用JWT，登出主要在前端处理（删除token）
  res.json({
    success: true,
    message: '登出成功'
  });
});

// 获取当前用户信息
router.get('/auth/profile', auth.authenticateToken, async (req, res) => {
  try {
    const accessibleCustomers = await auth.getUserAccessibleCustomers(req.user);

    res.json({
      success: true,
      user: {
        id: req.user.id,
        username: req.user.username,
        name: req.user.name,
        permission: req.user.permission,
        accessibleCustomers: accessibleCustomers
      }
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

// 重新加载数据的函数 - 只使用MongoDB
async function reloadData() {
  try {
    salesFunnelData = await mongoManager.loadSalesData();
    customerDetailsData = await mongoManager.loadCustomerData();
    organizationData = await mongoManager.loadOrganizationData();
    businessManagersData = await mongoManager.loadBusinessManagersData();
    paymentRecordsData = await mongoManager.loadPaymentRecordsData();
    qaData = await mongoManager.loadQAData();
    executionPlansData = await mongoManager.loadExecutionPlansData();
    console.log('✅ 数据已从MongoDB重新加载');
    console.log(`📊 重新加载: ${salesFunnelData.length} 条销售数据, ${Object.keys(customerDetailsData).length} 个客户, ${executionPlansData.length} 条执行计划`);
  } catch (error) {
    console.error('❌ MongoDB重新加载失败:', error.message);
    throw error; // 抛出错误，不再回退到JSON
  }
}

// 手动重新加载数据API
router.post('/reload-data', auth.authenticateToken, auth.requireAdmin, async (req, res) => {
  try {
    await reloadData();
    res.json({
      success: true,
      message: '数据重新加载成功',
      data: {
        salesCount: salesFunnelData.length,
        customerCount: Object.keys(customerDetailsData).length,
        organizationCount: Object.keys(organizationData).length,
        businessManagerCount: businessManagersData.length,
        paymentRecordCount: paymentRecordsData.length,
        qaCount: qaData.length,
        executionPlanCount: executionPlansData.length
      }
    });
  } catch (error) {
    console.error('重新加载数据失败:', error);
    res.status(500).json({
      success: false,
      message: '数据重新加载失败: ' + error.message
    });
  }
});

// 获取所有销售数据（基于客户详情数据动态计算，支持序号编辑）
router.get('/sales', auth.authenticateToken, (req, res) => {
  const { region, businessManager: rawBusinessManager, sortBy = 'id', order = 'asc', page = 1, limit = 50, month, quarter } = req.query;
  const businessManager = rawBusinessManager ? decodeURIComponent(rawBusinessManager) : rawBusinessManager;

  // 基于客户详情数据动态生成销售记录，但保持序号映射
  const aggregatedSalesData = [];

  // 创建客户名称到序号的映射（从salesFunnelData获取）
  const customerIdMap = {};
  console.log(salesFunnelData);
  salesFunnelData.forEach(record => {
    customerIdMap[record.customerName] = record.id;
  });

  // 遍历所有客户详细数据，动态计算销售数据
  Object.values(customerDetailsData).forEach(customer => {
    // 计算月度数据（基于项目的预计签约月份）
    const monthlyData = {
      jan: 0, feb: 0, mar: 0, apr: 0, may: 0, jun: 0,
      jul: 0, aug: 0, sep: 0, oct: 0, nov: 0, dec: 0
    };

    const monthlyPayment = {
      jan: 0, feb: 0, mar: 0, apr: 0, may: 0, jun: 0,
      jul: 0, aug: 0, sep: 0, oct: 0, nov: 0, dec: 0
    };

    const monthlySales = {
      jan: 0, feb: 0, mar: 0, apr: 0, may: 0, jun: 0,
      jul: 0, aug: 0, sep: 0, oct: 0, nov: 0, dec: 0
    };

    // 根据项目的预计签约月份分配数据
    customer.projects.forEach(project => {
      const projectMonth = getProjectMonth(project);

      if (!monthlyData[projectMonth]) {
        monthlyData[projectMonth] = 0;
        monthlyPayment[projectMonth] = 0;
        monthlySales[projectMonth] = 0;
      }

      monthlyData[projectMonth] += project.expectedRevenue;
      monthlyPayment[projectMonth] += project.actualPayment || 0;
      monthlySales[projectMonth] += project.actualSales || 0;
    });

    // 计算季度数据
    const quarterlyData = {
      Q1: monthlyData.jan + monthlyData.feb + monthlyData.mar,
      Q2: monthlyData.apr + monthlyData.may + monthlyData.jun,
      Q3: monthlyData.jul + monthlyData.aug + monthlyData.sep,
      Q4: monthlyData.oct + monthlyData.nov + monthlyData.dec
    };

    const quarterlyPayment = {
      Q1: monthlyPayment.jan + monthlyPayment.feb + monthlyPayment.mar,
      Q2: monthlyPayment.apr + monthlyPayment.may + monthlyPayment.jun,
      Q3: monthlyPayment.jul + monthlyPayment.aug + monthlyPayment.sep,
      Q4: monthlyPayment.oct + monthlyPayment.nov + monthlyPayment.dec
    };

    const quarterlySales = {
      Q1: monthlySales.jan + monthlySales.feb + monthlySales.mar,
      Q2: monthlySales.apr + monthlySales.may + monthlySales.jun,
      Q3: monthlySales.jul + monthlySales.aug + monthlySales.sep,
      Q4: monthlySales.oct + monthlySales.nov + monthlySales.dec
    };

    // 获取客户的序号，如果没有则分配新序号
    let customerId = customerIdMap[customer.customerName];
    if (!customerId) {
      // 为新客户分配序号
      const maxId = Math.max(...Object.values(customerIdMap), 0);
      customerId = maxId + 1;
      customerIdMap[customer.customerName] = customerId;
    }

    // 根据筛选条件计算显示的数据
    let displayTotalPlan = customer.summary.totalExpectedRevenue;
    let displayActualPayment = customer.summary.totalActualPayment;
    let displayActualSales = customer.summary.totalActualSales;

    // 如果有月份筛选，只显示该月份的数据
    if (month && month !== 'all') {
      displayTotalPlan = monthlyData[month] || 0;
      displayActualPayment = monthlyPayment[month] || 0;
      displayActualSales = monthlySales[month] || 0;
    }
    // 如果有季度筛选，只显示该季度的数据
    else if (quarter && quarter !== 'all') {
      displayTotalPlan = quarterlyData[quarter] || 0;
      displayActualPayment = quarterlyPayment[quarter] || 0;
      displayActualSales = quarterlySales[quarter] || 0;
    }

    // 获取商务负责人信息
    const salesRecord = salesFunnelData.find(record => record.customerName === customer.customerName);
    const businessManager = salesRecord ? salesRecord.businessManager : '未分配';

    aggregatedSalesData.push({
      id: customerId,
      region: customer.region,
      customerName: customer.customerName,
      businessManager: businessManager,
      totalPlan: displayTotalPlan,
      actualPayment: displayActualPayment,
      actualSales: displayActualSales,
      totalFunnel: displayTotalPlan,
      customerRatio: displayTotalPlan > 0 ? (displayActualPayment / displayTotalPlan) : 0,
      provinceSales: 0, // 暂时设为0，可以后续计算
      provinceRatio: 0, // 暂时设为0，可以后续计算
      monthlyPlan: monthlyData,
      monthlyPayment: monthlyPayment,
      monthlySales: monthlySales,
      quarterlyPlan: quarterlyData,
      quarterlyPayment: quarterlyPayment,
      quarterlySales: quarterlySales,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      // 添加筛选信息，用于前端显示
      filterInfo: {
        type: month && month !== 'all' ? 'month' : (quarter && quarter !== 'all' ? 'quarter' : 'all'),
        value: month && month !== 'all' ? month : (quarter && quarter !== 'all' ? quarter : 'all')
      }
    });
  });

  // 根据用户权限过滤数据
  let filteredData = aggregatedSalesData;
  if (req.user.permission !== 'admin') {
    const accessibleCustomers = req.user.customers || [];
    filteredData = aggregatedSalesData.filter(item =>
      accessibleCustomers.includes(item.customerName)
    );
  }

  // 按区域过滤
  if (region && region !== 'all') {
    filteredData = filteredData.filter(item => item.region === region);
  }

  // 按商务负责人过滤
  if (businessManager && businessManager !== 'all') {
    filteredData = filteredData.filter(item => item.businessManager === businessManager);
  }

  // 按月份过滤（只显示该月份有数据的客户）
  if (month && month !== 'all') {
    filteredData = filteredData.filter(item => {
      return item.monthlyPlan && item.monthlyPlan[month] > 0;
    });
  }

  // 按季度过滤（只显示该季度有数据的客户）
  if (quarter && quarter !== 'all') {
    filteredData = filteredData.filter(item => {
      return item.quarterlyPlan && item.quarterlyPlan[quarter] > 0;
    });
  }

  // 排序
  filteredData.sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];

    // 处理数字类型的排序
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return order === 'desc' ? bValue - aValue : aValue - bValue;
    }

    // 处理字符串类型的排序
    if (order === 'desc') {
      return bValue > aValue ? 1 : -1;
    }
    return aValue > bValue ? 1 : -1;
  });

  // 分页
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedData = filteredData.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: paginatedData,
    total: filteredData.length,
    page: parseInt(page),
    limit: parseInt(limit),
    totalPages: Math.ceil(filteredData.length / limit)
  });
});

// 获取单个销售记录
router.get('/sales/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const salesRecord = salesFunnelData.find(item => item.id === id);

  if (!salesRecord) {
    return res.status(404).json({
      success: false,
      message: '销售记录不存在'
    });
  }

  res.json({
    success: true,
    data: salesRecord
  });
});

// 创建新的销售记录（仅管理序号映射，数据来源于客户详情）
router.post('/sales', auth.authenticateToken, auth.requireAdmin, async (req, res) => {
  const { region, customerName, businessManager } = req.body;

  // 简单验证
  if (!customerName || !region) {
    return res.status(400).json({
      success: false,
      message: '缺少必要字段：customerName, region'
    });
  }

  // 处理序号分配
  let newId;
  if (req.body.id) {
    const requestedId = parseInt(req.body.id);

    // 检查序号是否已被使用
    const existingRecord = salesFunnelData.find(item => item.id === requestedId);
    if (existingRecord) {
      return res.status(400).json({
        success: false,
        message: `序号 ${requestedId} 已被客户"${existingRecord.customerName}"使用，请选择其他序号`
      });
    }

    newId = requestedId;
  } else {
    // 如果没有指定序号，自动分配下一个可用序号
    newId = Math.max(...salesFunnelData.map(item => item.id), 0) + 1;
  }

  // 只存储序号映射信息，不存储销售数据
  const newSalesRecord = {
    id: newId,
    region,
    customerName,
    businessManager: businessManager || '未分配',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  salesFunnelData.push(newSalesRecord);

  // 检查客户详细信息是否存在，如果不存在则创建
  if (!customerDetailsData[customerName]) {
    console.log(`创建新客户详细信息: ${customerName}`);

    // 创建新的客户详细信息
    const newCustomerDetails = {
      customerName,
      region,
      sheetName: `${Object.keys(customerDetailsData).length + 1}${customerName}`,
      summary: {
        totalProjects: 0,
        totalExpectedRevenue: 0,
        totalExpectedSales: 0,
        totalActualPayment: 0,
        totalActualSales: 0,
        paymentRate: 0,
        salesRate: 0
      },
      projects: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 新客户创建时不创建默认项目，用户需要在客户详情页面添加具体项目

    // 添加到客户详细数据中
    customerDetailsData[customerName] = newCustomerDetails;

    // 保存客户数据到MongoDB
    try {
      await mongoManager.saveCustomerData(customerDetailsData);
      console.log('✅ 客户数据已保存到MongoDB');
    } catch (err) {
      console.error('❌ 保存客户数据到MongoDB失败:', err);
    }
  } else {
    console.log(`客户详细信息已存在: ${customerName}`);
  }

  // 保存销售数据到MongoDB
  try {
    await mongoManager.saveSalesData(salesFunnelData);
    console.log('✅ 销售数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存销售数据到MongoDB失败:', err);
  }

  const customerCreated = customerDetailsData[customerName] &&
    customerDetailsData[customerName].createdAt === customerDetailsData[customerName].updatedAt;

  res.status(201).json({
    success: true,
    data: newSalesRecord,
    customerDetails: customerDetailsData[customerName] ? {
      created: customerCreated,
      summary: customerDetailsData[customerName].summary
    } : null,
    message: '销售记录创建成功' + (customerCreated ? '，客户详细信息已同步创建' : '')
  });
});

// 更新销售记录（支持客户名称同步更新）
router.put('/sales/:id', auth.authenticateToken, auth.requireAdmin, async (req, res) => {
  const id = parseInt(req.params.id);
  const salesIndex = salesFunnelData.findIndex(item => item.id === id);

  if (salesIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '销售记录不存在'
    });
  }

  const originalRecord = salesFunnelData[salesIndex];
  const originalCustomerName = originalRecord.customerName;
  const newCustomerName = req.body.customerName || originalCustomerName;

  // 处理序号更新
  let newId = id; // 默认保持原有ID
  if (req.body.id && req.body.id !== id) {
    const requestedId = parseInt(req.body.id);

    // 检查新序号是否已被其他记录使用
    const existingRecord = salesFunnelData.find(item => item.id === requestedId && item.id !== id);
    if (existingRecord) {
      return res.status(400).json({
        success: false,
        message: `序号 ${requestedId} 已被客户"${existingRecord.customerName}"使用，请选择其他序号`
      });
    }

    newId = requestedId;
  }

  // 检查客户名称是否发生变化
  const customerNameChanged = originalCustomerName !== newCustomerName;
  let customerDataUpdated = false;

  // 如果客户名称发生变化，需要同步更新客户详细信息
  if (customerNameChanged) {
    console.log(`客户名称变化: "${originalCustomerName}" -> "${newCustomerName}"`);

    // 检查新客户名称是否已存在
    if (customerDetailsData[newCustomerName]) {
      return res.status(400).json({
        success: false,
        message: `客户名称"${newCustomerName}"已存在，无法重命名`
      });
    }

    // 如果原客户详细信息存在，进行重命名
    if (customerDetailsData[originalCustomerName]) {
      // 复制原客户数据到新名称
      const originalCustomerData = customerDetailsData[originalCustomerName];
      customerDetailsData[newCustomerName] = {
        ...originalCustomerData,
        customerName: newCustomerName,
        region: req.body.region || originalRecord.region,
        updatedAt: new Date().toISOString()
      };

      // 删除原客户数据
      delete customerDetailsData[originalCustomerName];
      customerDataUpdated = true;
      console.log(`✅ 客户详细信息已重命名: "${originalCustomerName}" -> "${newCustomerName}"`);
    }

    // 更新组织架构数据中的客户名称
    if (organizationData[originalCustomerName]) {
      organizationData[newCustomerName] = {
        ...organizationData[originalCustomerName],
        customerName: newCustomerName,
        updatedAt: new Date().toISOString()
      };
      delete organizationData[originalCustomerName];
      console.log(`✅ 组织架构数据已重命名: "${originalCustomerName}" -> "${newCustomerName}"`);
    }

    // 更新回款记录中的客户名称
    paymentRecordsData.forEach(record => {
      if (record.customerName === originalCustomerName) {
        record.customerName = newCustomerName;
      }
    });

    // 更新执行计划中的客户名称
    executionPlansData.forEach(plan => {
      if (plan.customerName === originalCustomerName) {
        plan.customerName = newCustomerName;
      }
    });

    // 更新商务经理分配中的客户名称
    businessManagersData.forEach(manager => {
      if (manager.customers && manager.customers.includes(originalCustomerName)) {
        const index = manager.customers.indexOf(originalCustomerName);
        manager.customers[index] = newCustomerName;
      }
    });
  }

  // 更新销售记录
  const updatedSalesRecord = {
    id: newId,
    region: req.body.region || originalRecord.region,
    customerName: newCustomerName,
    businessManager: req.body.businessManager || originalRecord.businessManager || '未分配',
    createdAt: originalRecord.createdAt,
    updatedAt: new Date().toISOString()
  };

  salesFunnelData[salesIndex] = updatedSalesRecord;

  // 保存所有相关数据到MongoDB
  try {
    await mongoManager.saveSalesData(salesFunnelData);
    console.log('✅ 销售数据已保存到MongoDB');

    if (customerDataUpdated) {
      await mongoManager.saveCustomerData(customerDetailsData);
      console.log('✅ 客户数据已保存到MongoDB');

      await mongoManager.saveOrganizationData(organizationData);
      console.log('✅ 组织架构数据已保存到MongoDB');

      await mongoManager.savePaymentRecordsData(paymentRecordsData);
      console.log('✅ 回款记录数据已保存到MongoDB');

      await mongoManager.saveExecutionPlansData(executionPlansData);
      console.log('✅ 执行计划数据已保存到MongoDB');

      await mongoManager.saveBusinessManagersData(businessManagersData);
      console.log('✅ 商务经理数据已保存到MongoDB');
    }
  } catch (err) {
    console.error('❌ 保存数据到MongoDB失败:', err);
    return res.status(500).json({
      success: false,
      message: '数据保存失败: ' + err.message
    });
  }

  const message = customerNameChanged ?
    `销售记录更新成功，客户名称已从"${originalCustomerName}"更改为"${newCustomerName}"，所有相关数据已同步更新` :
    '销售记录更新成功';

  res.json({
    success: true,
    data: updatedSalesRecord,
    customerNameChanged,
    originalCustomerName: customerNameChanged ? originalCustomerName : undefined,
    message
  });
});

// 删除销售记录
router.delete('/sales/:id', auth.authenticateToken, auth.requireAdmin, async (req, res) => {
  const id = parseInt(req.params.id);

  // 直接在salesFunnelData中查找要删除的记录
  const salesIndex = salesFunnelData.findIndex(item => item.id === id);

  if (salesIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '销售记录不存在'
    });
  }

  const recordToDelete = salesFunnelData[salesIndex];
  const customerName = recordToDelete.customerName;

  // 备份原始数据，以便在失败时回滚
  const originalSalesRecord = { ...recordToDelete };
  const originalCustomerData = customerDetailsData[customerName] ?
    JSON.parse(JSON.stringify(customerDetailsData[customerName])) : null;

  // 删除销售记录
  salesFunnelData.splice(salesIndex, 1);
  console.log(`已删除销售记录: ID=${id}, 客户=${customerName}`);

  // 删除对应的客户详细数据（如果存在）
  const hadCustomerData = !!customerDetailsData[customerName];
  if (customerDetailsData[customerName]) {
    delete customerDetailsData[customerName];
    console.log(`已删除客户详细数据: ${customerName}`);
  }

  // 保存数据到MongoDB（使用事务性操作）
  let saveSuccess = true;
  let errorMessage = '';

  try {
    // 先保存客户数据
    await mongoManager.saveCustomerData(customerDetailsData);
    console.log('✅ 客户数据已保存到MongoDB');

    // 再保存销售数据
    await mongoManager.saveSalesData(salesFunnelData);
    console.log('✅ 销售数据已保存到MongoDB');

    // 同步保存到JSON文件（用于数据备份和一致性）
    const dataManager = require('../utils/dataManager');
    dataManager.saveCustomerData(customerDetailsData);
    dataManager.saveSalesData(salesFunnelData);
    console.log('✅ 数据已同步到JSON文件');

  } catch (err) {
    console.error('❌ 保存数据失败:', err);
    saveSuccess = false;
    errorMessage = err.message;

    // 回滚内存中的数据
    console.log('🔄 开始回滚数据...');

    // 恢复销售记录
    salesFunnelData.splice(salesIndex, 0, originalSalesRecord);
    console.log('✅ 销售记录已回滚');

    // 恢复客户数据
    if (hadCustomerData && originalCustomerData) {
      customerDetailsData[customerName] = originalCustomerData;
      console.log('✅ 客户数据已回滚');
    }
  }

  if (saveSuccess) {
    res.json({
      success: true,
      message: `销售记录删除成功，已删除序号 ${id} 的客户 "${customerName}" 的所有数据`
    });
  } else {
    res.status(500).json({
      success: false,
      message: `删除失败: ${errorMessage}，数据已回滚`
    });
  }
});

// 获取销售统计数据（基于客户详细数据汇总计算）
router.get('/stats', auth.authenticateToken, async (req, res) => {
  try {
    const { region, businessManager: rawBusinessManager, month, quarter } = req.query;
    const businessManager = rawBusinessManager ? decodeURIComponent(rawBusinessManager) : rawBusinessManager;

    // 从客户详细数据计算统计，根据用户权限过滤
    const allCustomers = Object.values(customerDetailsData);
    const customers = await auth.filterDataByPermission(allCustomers, req.user, 'customerName');

  if (customers.length === 0) {
    // 如果没有客户详细数据，回退到原始统计逻辑
    const totalRecords = salesFunnelData.length;
    const totalPlan = salesFunnelData.reduce((sum, item) => sum + item.totalPlan, 0);
    const totalPayment = salesFunnelData.reduce((sum, item) => sum + item.actualPayment, 0);
    const totalSales = salesFunnelData.reduce((sum, item) => sum + item.actualSales, 0);

    return res.json({
      success: true,
      data: {
        regions: [],
        monthly: [],
        summary: {
          totalRecords,
          totalPlan: Math.round(totalPlan * 100) / 100,
          totalPayment: Math.round(totalPayment * 100) / 100,
          totalSales: Math.round(totalSales * 100) / 100,
          totalFunnel: Math.round(totalPlan * 100) / 100,
          salesRate: totalPlan > 0 ? Math.round((totalSales / totalPlan) * 10000) / 100 : 0
        }
      }
    });
  }

  // 根据筛选条件计算统计数据
  let totalRecords = 0;
  let totalPlan = 0;
  let totalPayment = 0;
  let totalSales = 0;

  customers.forEach(customer => {
    // 区域筛选
    if (region && customer.region !== region) {
      return;
    }

    // 商务负责人筛选
    if (businessManager && businessManager !== 'all') {
      const salesRecord = salesFunnelData.find(record => record.customerName === customer.customerName);
      const customerBusinessManager = salesRecord ? salesRecord.businessManager : '未分配';
      if (customerBusinessManager !== businessManager) {
        return;
      }
    }

    // 计算月度数据
    const monthlyData = {};
    const monthlyPayment = {};
    const monthlySales = {};

    customer.projects.forEach(project => {
      const projectMonth = getProjectMonth(project);

      if (!monthlyData[projectMonth]) {
        monthlyData[projectMonth] = 0;
        monthlyPayment[projectMonth] = 0;
        monthlySales[projectMonth] = 0;
      }

      monthlyData[projectMonth] += project.expectedRevenue;
      monthlyPayment[projectMonth] += project.actualPayment || 0;
      monthlySales[projectMonth] += project.actualSales || 0;
    });

    // 计算季度数据
    const quarterlyData = {
      Q1: (monthlyData.jan || 0) + (monthlyData.feb || 0) + (monthlyData.mar || 0),
      Q2: (monthlyData.apr || 0) + (monthlyData.may || 0) + (monthlyData.jun || 0),
      Q3: (monthlyData.jul || 0) + (monthlyData.aug || 0) + (monthlyData.sep || 0),
      Q4: (monthlyData.oct || 0) + (monthlyData.nov || 0) + (monthlyData.dec || 0)
    };

    const quarterlyPayment = {
      Q1: (monthlyPayment.jan || 0) + (monthlyPayment.feb || 0) + (monthlyPayment.mar || 0),
      Q2: (monthlyPayment.apr || 0) + (monthlyPayment.may || 0) + (monthlyPayment.jun || 0),
      Q3: (monthlyPayment.jul || 0) + (monthlyPayment.aug || 0) + (monthlyPayment.sep || 0),
      Q4: (monthlyPayment.oct || 0) + (monthlyPayment.nov || 0) + (monthlyPayment.dec || 0)
    };

    const quarterlySales = {
      Q1: (monthlySales.jan || 0) + (monthlySales.feb || 0) + (monthlySales.mar || 0),
      Q2: (monthlySales.apr || 0) + (monthlySales.may || 0) + (monthlySales.jun || 0),
      Q3: (monthlySales.jul || 0) + (monthlySales.aug || 0) + (monthlySales.sep || 0),
      Q4: (monthlySales.oct || 0) + (monthlySales.nov || 0) + (monthlySales.dec || 0)
    };

    // 根据筛选条件计算显示的数据
    let customerPlan = customer.summary.totalExpectedRevenue;
    let customerPayment = customer.summary.totalActualPayment;
    let customerSales = customer.summary.totalActualSales;

    // 如果有月份筛选，只计算该月份的数据
    if (month && month !== 'all') {
      customerPlan = monthlyData[month] || 0;
      customerPayment = monthlyPayment[month] || 0;
      customerSales = monthlySales[month] || 0;
    }
    // 如果有季度筛选，只计算该季度的数据
    else if (quarter && quarter !== 'all') {
      customerPlan = quarterlyData[quarter] || 0;
      customerPayment = quarterlyPayment[quarter] || 0;
      customerSales = quarterlySales[quarter] || 0;
    }

    // 只有当客户有数据时才计入统计
    if (customerPlan > 0 || customerPayment > 0 || customerSales > 0) {
      totalRecords++;
      totalPlan += customerPlan;
      totalPayment += customerPayment;
      totalSales += customerSales;
    }
  });

  res.json({
    success: true,
    data: {
      summary: {
        totalRecords,
        totalPlan: Math.round(totalPlan * 100) / 100,
        totalPayment: Math.round(totalPayment * 100) / 100,
        totalSales: Math.round(totalSales * 100) / 100
      }
    }
  });
  } catch (error) {
    console.error('获取统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败'
    });
  }
});

// 获取详细统计数据
router.get('/stats/detailed', auth.authenticateToken, async (req, res) => {
  try {
    const { region, businessManager: rawBusinessManager, month, quarter } = req.query;
    const businessManager = rawBusinessManager ? decodeURIComponent(rawBusinessManager) : rawBusinessManager;

    // 根据用户权限过滤数据
    const allCustomers = Object.values(customerDetailsData);
    let customers = await auth.filterDataByPermission(allCustomers, req.user, 'customerName');

    // 应用筛选条件
    if (region && region !== 'all') {
      customers = customers.filter(customer => customer.region === region);
    }

    // 商务负责人筛选
    if (businessManager && businessManager !== 'all') {
      customers = customers.filter(customer => {
        const salesRecord = salesFunnelData.find(record => record.customerName === customer.customerName);
        const customerBusinessManager = salesRecord ? salesRecord.businessManager : '未分配';
        return customerBusinessManager === businessManager;
      });
    }

    // 获取当前时间信息
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    const endOfWeek = new Date(startOfWeek.getTime() + 6 * 24 * 60 * 60 * 1000);

    // 初始化统计数据
    const detailedStats = {
      weeklyPayment: { amount: 0, count: 0 }, // 近一周项目回款
      invoicedPending: { amount: 0, count: 0 }, // 已开票待回款项目
      expectedInvoicing: { amount: 0, count: 0 }, // 近期预计待开票金额
      weeklySignedCompleted: { amount: 0, count: 0 }, // 本周已签约和完成采购项目
      weeklyNegotiationPending: { amount: 0, count: 0 } // 本周商谈及招标结束待签合同项目
    };

    // 辅助函数：获取项目所属月份
    function getProjectMonth(project) {
      const monthMapping = {
        '2025年1月': 'jan', '2025年2月': 'feb', '2025年3月': 'mar', '2025年4月': 'apr',
        '2025年5月': 'may', '2025年6月': 'jun', '2025年7月': 'jul', '2025年8月': 'aug',
        '2025年9月': 'sep', '2025年10月': 'oct', '2025年11月': 'nov', '2025年12月': 'dec'
      };
      return monthMapping[project.expectedSignMonth] || 'unknown';
    }

    // 遍历所有客户数据进行统计
    customers.forEach(customer => {
      if (!customer.projects) return;

      customer.projects.forEach(project => {
        // 应用月份和季度筛选
        if (month && month !== 'all') {
          const projectMonth = getProjectMonth(project);
          if (projectMonth !== month) return;
        }

        if (quarter && quarter !== 'all') {
          if (project.quarter !== quarter) return;
        }

        const projectDate = new Date(project.createdAt || project.updatedAt);
        const paymentDate = project.paymentTime ? new Date(project.paymentTime) : null;
        const contractDate = project.actualSignTime ? new Date(project.actualSignTime) : null;
        const acceptanceDate = project.acceptanceTime ? new Date(project.acceptanceTime) : null;

        // 获取项目相关时间
        const invoiceDate = project.invoiceDate ? new Date(project.invoiceDate) : null;
        const negotiationEndDate = project.actualNegotiationTime ? new Date(project.actualNegotiationTime) : null;

        // 1. 近一周项目回款：统计回款时间在近一周的回款金额
        if (paymentDate && paymentDate >= oneWeekAgo && project.paymentAmount) {
          detailedStats.weeklyPayment.amount += parseFloat(project.paymentAmount) || 0;
          detailedStats.weeklyPayment.count++;
        }

        // 2. 已开票待回款：统计存在开票时间但回款时间和回款金额为空的数据
        if (invoiceDate && !paymentDate && !project.paymentAmount && project.expectedRevenue) {
          detailedStats.invoicedPending.amount += parseFloat(project.expectedRevenue) || 0;
          detailedStats.invoicedPending.count++;
        }

        // 3. 近期预计待开票：统计存在近期签约时间，但是没有开票时间的数据
        if (contractDate && contractDate >= oneWeekAgo && !invoiceDate && project.expectedRevenue) {
          detailedStats.expectedInvoicing.amount += parseFloat(project.expectedRevenue) || 0;
          detailedStats.expectedInvoicing.count++;
        }

        // 4. 本周已签约完成：统计签约时间为近一周的数据
        if (contractDate && contractDate >= oneWeekAgo && project.expectedRevenue) {
          detailedStats.weeklySignedCompleted.amount += parseFloat(project.expectedRevenue) || 0;
          detailedStats.weeklySignedCompleted.count++;
        }

        // 5. 本周商谈招标待签：统计商谈招标结束时间为近一周的数据
        if (negotiationEndDate && negotiationEndDate >= oneWeekAgo && project.expectedRevenue) {
          detailedStats.weeklyNegotiationPending.amount += parseFloat(project.expectedRevenue) || 0;
          detailedStats.weeklyNegotiationPending.count++;
        }
      });
    });

    // 格式化金额（保留两位小数）
    Object.keys(detailedStats).forEach(key => {
      detailedStats[key].amount = Math.round(detailedStats[key].amount * 100) / 100;
    });

    res.json({
      success: true,
      data: detailedStats
    });
  } catch (error) {
    console.error('获取详细统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取详细统计数据失败'
    });
  }
});

// 获取详细统计的具体项目信息
router.get('/project-details/:type', auth.authenticateToken, async (req, res) => {
  try {
    console.log('项目详情API被调用，类型:', req.params.type);
    const { type } = req.params;
    const { region, businessManager: rawBusinessManager, month, quarter } = req.query;
    const businessManager = rawBusinessManager ? decodeURIComponent(rawBusinessManager) : rawBusinessManager;

    // 根据用户权限过滤数据
    const allCustomers = Object.values(customerDetailsData);
    let customers = await auth.filterDataByPermission(allCustomers, req.user, 'customerName');

    // 应用筛选条件
    if (region && region !== 'all') {
      customers = customers.filter(customer => customer.region === region);
    }

    if (businessManager && businessManager !== 'all') {
      customers = customers.filter(customer => {
        const salesRecord = salesFunnelData.find(record => record.customerName === customer.customerName);
        const customerBusinessManager = salesRecord ? salesRecord.businessManager : '未分配';
        return customerBusinessManager === businessManager;
      });
    }

    // 获取当前时间信息
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    const endOfWeek = new Date(startOfWeek.getTime() + 6 * 24 * 60 * 60 * 1000);

    // 辅助函数：获取项目所属月份
    function getProjectMonth(project) {
      const monthMapping = {
        '2025年1月': 'jan', '2025年2月': 'feb', '2025年3月': 'mar', '2025年4月': 'apr',
        '2025年5月': 'may', '2025年6月': 'jun', '2025年7月': 'jul', '2025年8月': 'aug',
        '2025年9月': 'sep', '2025年10月': 'oct', '2025年11月': 'nov', '2025年12月': 'dec'
      };
      return monthMapping[project.expectedSignMonth] || 'unknown';
    }

    const projects = [];

    // 遍历所有客户数据收集项目
    customers.forEach(customer => {
      if (!customer.projects) return;

      customer.projects.forEach(project => {
        // 应用月份和季度筛选
        if (month && month !== 'all') {
          const projectMonth = getProjectMonth(project);
          if (projectMonth !== month) return;
        }

        if (quarter && quarter !== 'all') {
          if (project.quarter !== quarter) return;
        }

        const paymentDate = project.paymentTime ? new Date(project.paymentTime) : null;
        const contractDate = project.actualSignTime ? new Date(project.actualSignTime) : null;
        const invoiceDate = project.invoiceDate ? new Date(project.invoiceDate) : null;
        const negotiationEndDate = project.actualNegotiationTime ? new Date(project.actualNegotiationTime) : null;

        let shouldInclude = false;

        // 根据类型筛选项目
        switch (type) {
          case 'weeklyPayment':
            // 近一周项目回款：统计回款时间在近一周的回款金额
            if (paymentDate && paymentDate >= oneWeekAgo && project.paymentAmount) {
              shouldInclude = true;
            }
            break;

          case 'invoicedPending':
            // 已开票待回款：统计存在开票时间但回款时间和回款金额为空的数据
            if (invoiceDate && !paymentDate && !project.paymentAmount && project.expectedRevenue) {
              shouldInclude = true;
            }
            break;

          case 'expectedInvoicing':
            // 近期预计待开票：统计存在近期签约时间，但是没有开票时间的数据
            if (contractDate && contractDate >= oneWeekAgo && !invoiceDate && project.expectedRevenue) {
              shouldInclude = true;
            }
            break;

          case 'weeklySignedCompleted':
            // 本周已签约完成：统计签约时间为近一周的数据
            if (contractDate && contractDate >= oneWeekAgo && project.expectedRevenue) {
              shouldInclude = true;
            }
            break;

          case 'weeklyNegotiationPending':
            // 本周商谈招标待签：统计商谈招标结束时间为近一周的数据
            if (negotiationEndDate && negotiationEndDate >= oneWeekAgo && project.expectedRevenue) {
              shouldInclude = true;
            }
            break;
        }

        if (shouldInclude) {
          // 获取商务负责人信息
          const salesRecord = salesFunnelData.find(record => record.customerName === customer.customerName);
          const projectBusinessManager = salesRecord ? salesRecord.businessManager : '未分配';

          projects.push({
            ...project,
            customerName: customer.customerName,
            customerRegion: customer.region,
            businessManager: projectBusinessManager,
            pendingAmount: type === 'invoicedPending' ? parseFloat(project.expectedRevenue) || 0 : null
          });
        }
      });
    });

    // 按金额排序
    projects.sort((a, b) => {
      const amountA = type === 'invoicedPending' ? a.pendingAmount :
                     type === 'weeklyPayment' ? a.paymentAmount : a.expectedRevenue;
      const amountB = type === 'invoicedPending' ? b.pendingAmount :
                     type === 'weeklyPayment' ? b.paymentAmount : b.expectedRevenue;
      return (parseFloat(amountB) || 0) - (parseFloat(amountA) || 0);
    });

    res.json({
      success: true,
      data: projects,
      type: type
    });
  } catch (error) {
    console.error('获取项目详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取项目详情失败'
    });
  }
});

// 获取区域列表
router.get('/regions', auth.authenticateToken, (req, res) => {
  const regions = [...new Set(salesFunnelData.map(item => item.region))].sort();
  res.json({
    success: true,
    data: regions
  });
});

// 获取所有客户列表
router.get('/customers', auth.authenticateToken, async (req, res) => {
  try {
    const allCustomers = Object.keys(customerDetailsData).map(customerName => {
      const customer = customerDetailsData[customerName];
      return {
        customerName: customer.customerName,
        region: customer.region,
        sheetName: customer.sheetName,
        summary: customer.summary
      };
    });

    // 根据用户权限过滤客户数据
    const filteredCustomers = await auth.filterDataByPermission(allCustomers, req.user, 'customerName');

    res.json({
      success: true,
      data: filteredCustomers
    });
  } catch (error) {
    console.error('获取客户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取客户列表失败'
    });
  }
});

// 获取特定客户的详细信息
router.get('/customers/:customerName', auth.authenticateToken, auth.checkCustomerAccess, (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const customer = customerDetailsData[customerName];

  if (!customer) {
    return res.status(404).json({
      success: false,
      message: '客户不存在'
    });
  }

  res.json({
    success: true,
    data: customer
  });
});

// 获取特定客户的项目列表
router.get('/customers/:customerName/projects', auth.authenticateToken, auth.checkCustomerAccess, (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const customer = customerDetailsData[customerName];

  if (!customer) {
    return res.status(404).json({
      success: false,
      message: '客户不存在'
    });
  }

  const { sortBy = 'id', order = 'asc', stage, productType } = req.query;
  let projects = [...customer.projects];

  // 按项目阶段过滤
  if (stage) {
    projects = projects.filter(project => project.projectStage === stage);
  }

  // 按产品类型过滤
  if (productType) {
    projects = projects.filter(project => project.productType === productType);
  }

  // 排序
  projects.sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return order === 'desc' ? bValue - aValue : aValue - bValue;
    }

    if (order === 'desc') {
      return bValue > aValue ? 1 : -1;
    }
    return aValue > bValue ? 1 : -1;
  });

  res.json({
    success: true,
    data: projects,
    total: projects.length,
    summary: customer.summary
  });
});

// 获取特定客户的特定项目
router.get('/customers/:customerName/projects/:projectId', auth.authenticateToken, auth.checkCustomerAccess, (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const projectId = parseInt(req.params.projectId);
  const customer = customerDetailsData[customerName];

  if (!customer) {
    return res.status(404).json({
      success: false,
      message: '客户不存在'
    });
  }

  const project = customer.projects.find(p => p.id === projectId);

  if (!project) {
    return res.status(404).json({
      success: false,
      message: '项目不存在'
    });
  }

  res.json({
    success: true,
    data: project
  });
});

// 更新特定客户的特定项目
router.put('/customers/:customerName/projects/:projectId', auth.authenticateToken, auth.checkCustomerAccess, async (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const projectId = parseInt(req.params.projectId);
  const customer = customerDetailsData[customerName];

  if (!customer) {
    return res.status(404).json({
      success: false,
      message: '客户不存在'
    });
  }

  const projectIndex = customer.projects.findIndex(p => p.id === projectId);

  if (projectIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '项目不存在'
    });
  }

  // 更新项目数据
  const updatedProject = {
    ...customer.projects[projectIndex],
    ...req.body,
    id: projectId, // 确保ID不被修改
    updatedAt: new Date().toISOString()
  };

  // 自动计算预计销售额 = 预计收入 * 完成度
  if (updatedProject.expectedRevenue && updatedProject.percentage !== undefined) {
    updatedProject.expectedSales = Math.round(updatedProject.expectedRevenue * updatedProject.percentage * 100) / 100;
    console.log(`自动计算预计销售额: ${updatedProject.expectedRevenue} * ${updatedProject.percentage} = ${updatedProject.expectedSales}`);
  }

  customer.projects[projectIndex] = updatedProject;

  // 重新计算汇总数据
  const totalExpectedRevenue = customer.projects.reduce((sum, p) => sum + p.expectedRevenue, 0);
  const totalExpectedSales = customer.projects.reduce((sum, p) => sum + p.expectedSales, 0);
  const totalActualPayment = customer.projects.reduce((sum, p) => sum + p.actualPayment, 0);
  const totalActualSales = customer.projects.reduce((sum, p) => sum + p.actualSales, 0);

  customer.summary = {
    totalProjects: customer.projects.length,
    totalExpectedRevenue: Math.round(totalExpectedRevenue * 100) / 100,
    totalExpectedSales: Math.round(totalExpectedSales * 100) / 100,
    totalActualPayment: Math.round(totalActualPayment * 100) / 100,
    totalActualSales: Math.round(totalActualSales * 100) / 100,
    salesRate: totalExpectedRevenue > 0 ?
      Math.round((totalActualSales / totalExpectedRevenue) * 10000) / 100 : 0
  };

  customer.updatedAt = new Date().toISOString();

  // 保存客户数据到MongoDB
  try {
    await mongoManager.saveCustomerData(customerDetailsData);
    console.log('✅ 客户数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存客户数据到MongoDB失败:', err);
  }

  res.json({
    success: true,
    data: updatedProject,
    summary: customer.summary,
    message: '项目更新成功'
  });
});

// 添加新项目到特定客户
router.post('/customers/:customerName/projects', auth.authenticateToken, auth.checkCustomerAccess, async (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const customer = customerDetailsData[customerName];

  if (!customer) {
    return res.status(404).json({
      success: false,
      message: '客户不存在'
    });
  }

  const { projectName, productType, expectedRevenue } = req.body;

  if (!projectName || !expectedRevenue) {
    return res.status(400).json({
      success: false,
      message: '缺少必要字段：projectName, expectedRevenue'
    });
  }

  const newProject = {
    id: Math.max(...customer.projects.map(p => p.id), 0) + 1,
    projectName,
    productType: productType || '',
    expectedRevenue: parseFloat(expectedRevenue),
    projectStage: req.body.projectStage || '呈现价值',
    percentage: parseFloat(req.body.percentage) || 0,
    expectedSales: 0, // 将由自动计算设置
    actualPayment: parseFloat(req.body.actualPayment) || 0,
    actualSales: parseFloat(req.body.actualSales) || 0,
    expectedSignMonth: req.body.expectedSignMonth || '',
    quarter: req.body.quarter || '',
    contractTime: req.body.contractTime || '',

    // 新增字段：时间相关
    actualNegotiationTime: req.body.actualNegotiationTime || '', // 实际商务谈判/中标时间
    actualSignTime: req.body.actualSignTime || '', // 实际签约时间
    invoiceDate: req.body.invoiceDate || '', // 开票日期
    acceptanceTime: req.body.acceptanceTime || '', // 验收时间

    // 新增字段：回款相关
    paymentTime: req.body.paymentTime || '', // 回款时间
    paymentAmount: parseFloat(req.body.paymentAmount) || 0, // 回款金额
    paymentRemarks: req.body.paymentRemarks || '', // 回款备注
    realtimePaymentData: req.body.realtimePaymentData || '', // 实时收款数据

    // 新增字段：合作与竞争
    partners: req.body.partners || '', // 合作伙伴
    competitors: req.body.competitors || '', // 竞争对手

    // 原有字段
    projectDepartment: req.body.projectDepartment || '',
    departmentHead: req.body.departmentHead || '',
    departmentLeader: req.body.departmentLeader || '',
    companyLeader: req.body.companyLeader || '',
    relatedDepartment: req.body.relatedDepartment || '',
    relatedDepartmentHead: req.body.relatedDepartmentHead || '',
    relatedCompanyLeader: req.body.relatedCompanyLeader || '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // 自动计算预计销售额 = 预计收入 * 完成度
  newProject.expectedSales = Math.round(newProject.expectedRevenue * newProject.percentage * 100) / 100;
  console.log(`新项目自动计算预计销售额: ${newProject.expectedRevenue} * ${newProject.percentage} = ${newProject.expectedSales}`);

  customer.projects.push(newProject);

  // 重新计算汇总数据
  const totalExpectedRevenue = customer.projects.reduce((sum, p) => sum + p.expectedRevenue, 0);
  const totalExpectedSales = customer.projects.reduce((sum, p) => sum + p.expectedSales, 0);
  const totalActualPayment = customer.projects.reduce((sum, p) => sum + p.actualPayment, 0);
  const totalActualSales = customer.projects.reduce((sum, p) => sum + p.actualSales, 0);

  customer.summary = {
    totalProjects: customer.projects.length,
    totalExpectedRevenue: Math.round(totalExpectedRevenue * 100) / 100,
    totalExpectedSales: Math.round(totalExpectedSales * 100) / 100,
    totalActualPayment: Math.round(totalActualPayment * 100) / 100,
    totalActualSales: Math.round(totalActualSales * 100) / 100,
    paymentRate: totalExpectedRevenue > 0 ?
      Math.round((totalActualPayment / totalExpectedRevenue) * 10000) / 100 : 0,
    salesRate: totalExpectedRevenue > 0 ?
      Math.round((totalActualSales / totalExpectedRevenue) * 10000) / 100 : 0
  };

  customer.updatedAt = new Date().toISOString();

  // 保存客户数据到MongoDB
  try {
    await mongoManager.saveCustomerData(customerDetailsData);
    console.log('✅ 客户数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存客户数据到MongoDB失败:', err);
  }

  res.status(201).json({
    success: true,
    data: newProject,
    summary: customer.summary,
    message: '项目创建成功'
  });
});

// 删除特定客户的特定项目
router.delete('/customers/:customerName/projects/:projectId', auth.authenticateToken, auth.checkCustomerAccess, async (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const projectId = parseInt(req.params.projectId);
  const customer = customerDetailsData[customerName];

  if (!customer) {
    return res.status(404).json({
      success: false,
      message: '客户不存在'
    });
  }

  const projectIndex = customer.projects.findIndex(p => p.id === projectId);

  if (projectIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '项目不存在'
    });
  }

  // 删除项目
  const deletedProject = customer.projects.splice(projectIndex, 1)[0];

  // 重新计算汇总数据
  const totalExpectedRevenue = customer.projects.reduce((sum, p) => sum + p.expectedRevenue, 0);
  const totalExpectedSales = customer.projects.reduce((sum, p) => sum + p.expectedSales, 0);
  const totalActualPayment = customer.projects.reduce((sum, p) => sum + p.actualPayment, 0);
  const totalActualSales = customer.projects.reduce((sum, p) => sum + p.actualSales, 0);

  customer.summary = {
    totalProjects: customer.projects.length,
    totalExpectedRevenue: Math.round(totalExpectedRevenue * 100) / 100,
    totalExpectedSales: Math.round(totalExpectedSales * 100) / 100,
    totalActualPayment: Math.round(totalActualPayment * 100) / 100,
    totalActualSales: Math.round(totalActualSales * 100) / 100,
    paymentRate: totalExpectedRevenue > 0 ?
      Math.round((totalActualPayment / totalExpectedRevenue) * 10000) / 100 : 0,
    salesRate: totalExpectedRevenue > 0 ?
      Math.round((totalActualSales / totalExpectedRevenue) * 10000) / 100 : 0
  };

  customer.updatedAt = new Date().toISOString();

  // 保存客户数据到MongoDB
  try {
    await mongoManager.saveCustomerData(customerDetailsData);
    console.log('✅ 客户数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存客户数据到MongoDB失败:', err);
  }

  res.json({
    success: true,
    data: deletedProject,
    summary: customer.summary,
    message: '项目删除成功'
  });
});



// ==================== 组织架构管理API ====================

// 获取客户组织架构
router.get('/organizations/:customerName', auth.authenticateToken, auth.checkCustomerAccess, (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const organization = organizationData[customerName];

  if (!organization) {
    return res.status(404).json({
      success: false,
      message: '客户组织架构不存在'
    });
  }

  res.json({
    success: true,
    data: organization
  });
});

// 创建或更新客户组织架构
router.post('/organizations/:customerName', auth.authenticateToken, auth.checkCustomerAccess, async (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);

  // 验证客户是否存在
  if (!customerDetailsData[customerName]) {
    return res.status(404).json({
      success: false,
      message: '客户不存在'
    });
  }

  // 获取客户ID
  const salesRecord = salesFunnelData.find(record => record.customerName === customerName);
  const customerId = salesRecord ? salesRecord.id : null;

  const organizationInfo = {
    customerName,
    customerId,
    departments: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  organizationData[customerName] = organizationInfo;
  try {
    await mongoManager.saveOrganizationData(organizationData);
    console.log('✅ 组织架构数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存组织架构数据到MongoDB失败:', err);
  }

  res.status(201).json({
    success: true,
    data: organizationInfo,
    message: '组织架构创建成功'
  });
});

// 添加部门
router.post('/organizations/:customerName/departments', auth.authenticateToken, auth.checkCustomerAccess, async (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const { name, type, manager, employeeCount, description, parentId } = req.body;

  if (!name || !type) {
    return res.status(400).json({
      success: false,
      message: '部门名称和类型为必填项'
    });
  }

  // 确保组织架构存在
  if (!organizationData[customerName]) {
    // 自动创建组织架构
    const salesRecord = salesFunnelData.find(record => record.customerName === customerName);
    const customerId = salesRecord ? salesRecord.id : null;

    organizationData[customerName] = {
      customerName,
      customerId,
      departments: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  }

  const organization = organizationData[customerName];

  // 生成新的部门ID
  const newDeptId = `dept_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const newDepartment = {
    id: newDeptId,
    name,
    type,
    manager: manager || '',
    employeeCount: parseInt(employeeCount) || 0,
    description: description || '',
    parentId: parentId || null,
    children: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // 如果是根部门，直接添加到departments数组
  if (!parentId) {
    organization.departments.push(newDepartment);
  } else {
    // 查找父部门并添加为子部门
    const parentDept = findDepartmentById(organization.departments, parentId);
    if (!parentDept) {
      return res.status(404).json({
        success: false,
        message: '父部门不存在'
      });
    }
    parentDept.children.push(newDepartment);
  }

  organization.updatedAt = new Date().toISOString();
  try {
    await mongoManager.saveOrganizationData(organizationData);
    console.log('✅ 组织架构数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存组织架构数据到MongoDB失败:', err);
  }

  res.status(201).json({
    success: true,
    data: newDepartment,
    message: '部门添加成功'
  });
});

// 更新部门
router.put('/organizations/:customerName/departments/:deptId', auth.authenticateToken, auth.checkCustomerAccess, async (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const deptId = req.params.deptId;
  const { name, type, manager, employeeCount, description } = req.body;

  const organization = organizationData[customerName];
  if (!organization) {
    return res.status(404).json({
      success: false,
      message: '客户组织架构不存在'
    });
  }

  const department = findDepartmentById(organization.departments, deptId);
  if (!department) {
    return res.status(404).json({
      success: false,
      message: '部门不存在'
    });
  }

  // 更新部门信息
  if (name) department.name = name;
  if (type) department.type = type;
  if (manager !== undefined) department.manager = manager;
  if (employeeCount !== undefined) department.employeeCount = parseInt(employeeCount) || 0;
  if (description !== undefined) department.description = description;
  department.updatedAt = new Date().toISOString();

  organization.updatedAt = new Date().toISOString();
  try {
    await mongoManager.saveOrganizationData(organizationData);
    console.log('✅ 组织架构数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存组织架构数据到MongoDB失败:', err);
  }

  res.json({
    success: true,
    data: department,
    message: '部门更新成功'
  });
});

// 删除部门
router.delete('/organizations/:customerName/departments/:deptId', auth.authenticateToken, auth.checkCustomerAccess, async (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const deptId = req.params.deptId;

  const organization = organizationData[customerName];
  if (!organization) {
    return res.status(404).json({
      success: false,
      message: '客户组织架构不存在'
    });
  }

  const deleted = deleteDepartmentById(organization.departments, deptId);
  if (!deleted) {
    return res.status(404).json({
      success: false,
      message: '部门不存在'
    });
  }

  organization.updatedAt = new Date().toISOString();
  try {
    await mongoManager.saveOrganizationData(organizationData);
    console.log('✅ 组织架构数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存组织架构数据到MongoDB失败:', err);
  }

  res.json({
    success: true,
    message: '部门删除成功'
  });
});

// 辅助函数：根据项目获取月份
function getProjectMonth(project) {
  // 优先使用expectedSignMonth，如果没有则使用quarter推算
  if (project.expectedSignMonth) {
    const monthMap = {
      '1月': 'jan', '2月': 'feb', '3月': 'mar', '4月': 'apr',
      '5月': 'may', '6月': 'jun', '7月': 'jul', '8月': 'aug',
      '9月': 'sep', '10月': 'oct', '11月': 'nov', '12月': 'dec'
    };

    // 处理"2025年2月"格式
    const monthMatch = project.expectedSignMonth.match(/(\d+)月/);
    if (monthMatch) {
      const monthNum = parseInt(monthMatch[1]);
      const monthKeys = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
      if (monthNum >= 1 && monthNum <= 12) {
        return monthKeys[monthNum - 1];
      }
    }

    // 处理直接的月份格式如"2月"
    return monthMap[project.expectedSignMonth] || 'jan';
  }

  // 如果有quarter信息，使用季度的第一个月
  if (project.quarter) {
    const quarterMap = {
      'Q1': 'jan', '第一季度': 'jan',
      'Q2': 'apr', '第二季度': 'apr',
      'Q3': 'jul', '第三季度': 'jul',
      'Q4': 'oct', '第四季度': 'oct'
    };
    return quarterMap[project.quarter] || 'jan';
  }

  // 默认返回1月
  return 'jan';
}

// 辅助函数：根据ID查找部门
function findDepartmentById(departments, id) {
  for (const dept of departments) {
    if (dept.id === id) {
      return dept;
    }
    if (dept.children) {
      const found = findDepartmentById(dept.children, id);
      if (found) return found;
    }
  }
  return null;
}

// 辅助函数：根据ID删除部门
function deleteDepartmentById(departments, id) {
  for (let i = 0; i < departments.length; i++) {
    if (departments[i].id === id) {
      departments.splice(i, 1);
      return true;
    }
    if (departments[i].children) {
      const deleted = deleteDepartmentById(departments[i].children, id);
      if (deleted) return true;
    }
  }
  return false;
}

// ==================== 人员管理API ====================

// 添加人员到部门
router.post('/organizations/:customerName/departments/:deptId/employees', auth.authenticateToken, auth.checkCustomerAccess, async (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const deptId = req.params.deptId;
  const { name, gender, position, description } = req.body;

  if (!name || !gender || !position) {
    return res.status(400).json({
      success: false,
      message: '姓名、性别和职位为必填项'
    });
  }

  const organization = organizationData[customerName];
  if (!organization) {
    return res.status(404).json({
      success: false,
      message: '客户组织架构不存在'
    });
  }

  const department = findDepartmentById(organization.departments, deptId);
  if (!department) {
    return res.status(404).json({
      success: false,
      message: '部门不存在'
    });
  }

  // 确保部门有employees数组
  if (!department.employees) {
    department.employees = [];
  }

  // 生成新的人员ID
  const newEmpId = `emp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

  const newEmployee = {
    id: newEmpId,
    name,
    gender,
    position,
    description: description || '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  department.employees.push(newEmployee);

  // 更新部门人员数量
  department.employeeCount = department.employees.length;
  department.updatedAt = new Date().toISOString();

  organization.updatedAt = new Date().toISOString();
  try {
    await mongoManager.saveOrganizationData(organizationData);
    console.log('✅ 组织架构数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存组织架构数据到MongoDB失败:', err);
  }

  res.status(201).json({
    success: true,
    data: newEmployee,
    message: '人员添加成功'
  });
});

// 更新人员信息
router.put('/organizations/:customerName/departments/:deptId/employees/:empId', auth.authenticateToken, auth.checkCustomerAccess, async (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const deptId = req.params.deptId;
  const empId = req.params.empId;
  const { name, gender, position, description } = req.body;

  const organization = organizationData[customerName];
  if (!organization) {
    return res.status(404).json({
      success: false,
      message: '客户组织架构不存在'
    });
  }

  const department = findDepartmentById(organization.departments, deptId);
  if (!department) {
    return res.status(404).json({
      success: false,
      message: '部门不存在'
    });
  }

  if (!department.employees) {
    return res.status(404).json({
      success: false,
      message: '人员不存在'
    });
  }

  const employee = department.employees.find(emp => emp.id === empId);
  if (!employee) {
    return res.status(404).json({
      success: false,
      message: '人员不存在'
    });
  }

  // 更新人员信息
  if (name) employee.name = name;
  if (gender) employee.gender = gender;
  if (position) employee.position = position;
  if (description !== undefined) employee.description = description;
  employee.updatedAt = new Date().toISOString();

  department.updatedAt = new Date().toISOString();
  organization.updatedAt = new Date().toISOString();
  try {
    await mongoManager.saveOrganizationData(organizationData);
    console.log('✅ 组织架构数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存组织架构数据到MongoDB失败:', err);
  }

  res.json({
    success: true,
    data: employee,
    message: '人员更新成功'
  });
});

// 删除人员
router.delete('/organizations/:customerName/departments/:deptId/employees/:empId', auth.authenticateToken, auth.checkCustomerAccess, async (req, res) => {
  const customerName = decodeURIComponent(req.params.customerName);
  const deptId = req.params.deptId;
  const empId = req.params.empId;

  const organization = organizationData[customerName];
  if (!organization) {
    return res.status(404).json({
      success: false,
      message: '客户组织架构不存在'
    });
  }

  const department = findDepartmentById(organization.departments, deptId);
  if (!department) {
    return res.status(404).json({
      success: false,
      message: '部门不存在'
    });
  }

  if (!department.employees) {
    return res.status(404).json({
      success: false,
      message: '人员不存在'
    });
  }

  const empIndex = department.employees.findIndex(emp => emp.id === empId);
  if (empIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '人员不存在'
    });
  }

  // 删除人员
  department.employees.splice(empIndex, 1);

  // 更新部门人员数量
  department.employeeCount = department.employees.length;
  department.updatedAt = new Date().toISOString();

  organization.updatedAt = new Date().toISOString();
  try {
    await mongoManager.saveOrganizationData(organizationData);
    console.log('✅ 组织架构数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存组织架构数据到MongoDB失败:', err);
  }

  res.json({
    success: true,
    message: '人员删除成功'
  });
});

// ==================== 商务负责人管理 API ====================

// 获取所有商务负责人
router.get('/business-managers', (req, res) => {
  try {
    const { permission, search } = req.query;
    let managers = [...businessManagersData];

    // 按权限筛选
    if (permission && permission !== 'all') {
      managers = managers.filter(manager => manager.permission === permission);
    }

    // 按搜索关键词筛选
    if (search) {
      const searchLower = search.toLowerCase();
      managers = managers.filter(manager =>
        manager.name.toLowerCase().includes(searchLower) ||
        manager.username.toLowerCase().includes(searchLower)
      );
    }

    res.json({
      success: true,
      data: managers,
      total: managers.length
    });
  } catch (error) {
    console.error('获取商务负责人列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取商务负责人列表失败: ' + error.message
    });
  }
});

// 获取单个商务负责人
router.get('/business-managers/:id', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const manager = businessManagersData.find(m => m.id === id);

    if (!manager) {
      return res.status(404).json({
        success: false,
        message: '商务负责人不存在'
      });
    }

    res.json({
      success: true,
      data: manager
    });
  } catch (error) {
    console.error('获取商务负责人详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取商务负责人详情失败: ' + error.message
    });
  }
});

// 创建新的商务负责人
router.post('/business-managers', async (req, res) => {
  try {
    const { name, username, password, permission, customers = [] } = req.body;

    // 验证必填字段
    if (!name || !username || !password || !permission) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 检查姓名是否重复
    const existingByName = businessManagersData.find(m => m.name === name);
    if (existingByName) {
      return res.status(400).json({
        success: false,
        message: `姓名"${name}"已存在`
      });
    }

    // 检查账户名是否重复
    const existingByUsername = businessManagersData.find(m => m.username === username);
    if (existingByUsername) {
      return res.status(400).json({
        success: false,
        message: `账户名"${username}"已存在`
      });
    }

    // 生成新ID
    const newId = Math.max(...businessManagersData.map(m => m.id), 0) + 1;

    const newManager = {
      id: newId,
      name,
      username,
      password,
      permission,
      customers: customers || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    businessManagersData.push(newManager);

    // 更新销售记录中的商务负责人关联
    updateSalesRecordsBusinessManager(name, customers);

    // 保存数据
    try {
    await mongoManager.saveBusinessManagersData(businessManagersData);
    console.log('✅ 商务经理数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存商务经理数据到MongoDB失败:', err);
  }
    try {
    await mongoManager.saveSalesData(salesFunnelData);
    console.log('✅ 销售数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存销售数据到MongoDB失败:', err);
  }

    res.status(201).json({
      success: true,
      data: newManager,
      message: '商务负责人创建成功'
    });
  } catch (error) {
    console.error('创建商务负责人失败:', error);
    res.status(500).json({
      success: false,
      message: '创建商务负责人失败: ' + error.message
    });
  }
});

// 更新商务负责人
router.put('/business-managers/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const managerIndex = businessManagersData.findIndex(m => m.id === id);

    if (managerIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '商务负责人不存在'
      });
    }

    const originalManager = businessManagersData[managerIndex];
    const { name, username, password, permission, customers = [] } = req.body;

    // 验证必填字段
    if (!name || !username || !password || !permission) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 检查姓名是否与其他记录重复
    const existingByName = businessManagersData.find(m => m.name === name && m.id !== id);
    if (existingByName) {
      return res.status(400).json({
        success: false,
        message: `姓名"${name}"已存在`
      });
    }

    // 检查账户名是否与其他记录重复
    const existingByUsername = businessManagersData.find(m => m.username === username && m.id !== id);
    if (existingByUsername) {
      return res.status(400).json({
        success: false,
        message: `账户名"${username}"已存在`
      });
    }

    const updatedManager = {
      ...originalManager,
      name,
      username,
      password,
      permission,
      customers: customers || [],
      updatedAt: new Date().toISOString()
    };

    businessManagersData[managerIndex] = updatedManager;

    // 如果姓名发生变化，需要更新销售记录中的关联
    if (originalManager.name !== name) {
      // 更新销售记录中的商务负责人姓名
      salesFunnelData.forEach(record => {
        if (record.businessManager === originalManager.name) {
          record.businessManager = name;
        }
      });
    }

    // 更新销售记录中的商务负责人关联
    updateSalesRecordsBusinessManager(name, customers, originalManager.customers);

    // 保存数据
    try {
    await mongoManager.saveBusinessManagersData(businessManagersData);
    console.log('✅ 商务经理数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存商务经理数据到MongoDB失败:', err);
  }
    try {
    await mongoManager.saveSalesData(salesFunnelData);
    console.log('✅ 销售数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存销售数据到MongoDB失败:', err);
  }

    res.json({
      success: true,
      data: updatedManager,
      message: '商务负责人更新成功'
    });
  } catch (error) {
    console.error('更新商务负责人失败:', error);
    res.status(500).json({
      success: false,
      message: '更新商务负责人失败: ' + error.message
    });
  }
});

// 删除商务负责人
router.delete('/business-managers/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const managerIndex = businessManagersData.findIndex(m => m.id === id);

    if (managerIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '商务负责人不存在'
      });
    }

    const managerToDelete = businessManagersData[managerIndex];

    // 将该负责人的客户设置为未分配
    salesFunnelData.forEach(record => {
      if (record.businessManager === managerToDelete.name) {
        record.businessManager = '未分配';
      }
    });

    // 删除商务负责人
    businessManagersData.splice(managerIndex, 1);

    // 保存数据
    try {
    await mongoManager.saveBusinessManagersData(businessManagersData);
    console.log('✅ 商务经理数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存商务经理数据到MongoDB失败:', err);
  }
    try {
    await mongoManager.saveSalesData(salesFunnelData);
    console.log('✅ 销售数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存销售数据到MongoDB失败:', err);
  }

    res.json({
      success: true,
      message: `商务负责人"${managerToDelete.name}"删除成功，相关客户已设置为未分配`
    });
  } catch (error) {
    console.error('删除商务负责人失败:', error);
    res.status(500).json({
      success: false,
      message: '删除商务负责人失败: ' + error.message
    });
  }
});

// 辅助函数：更新销售记录中的商务负责人关联
function updateSalesRecordsBusinessManager(managerName, newCustomers, oldCustomers = []) {
  // 将原来的客户设置为未分配
  oldCustomers.forEach(customerName => {
    if (!newCustomers.includes(customerName)) {
      const record = salesFunnelData.find(r => r.customerName === customerName);
      if (record) {
        record.businessManager = '未分配';
      }
    }
  });

  // 将新的客户分配给该负责人
  newCustomers.forEach(customerName => {
    const record = salesFunnelData.find(r => r.customerName === customerName);
    if (record) {
      record.businessManager = managerName;
    } else {
      // 如果销售记录不存在，可以选择创建或忽略
      console.log(`警告: 客户"${customerName}"在销售记录中不存在`);
    }
  });
}

// 获取商务负责人的客户分配统计
router.get('/business-managers/stats/assignments', (req, res) => {
  try {
    const stats = businessManagersData.map(manager => {
      const assignedCustomers = salesFunnelData.filter(record =>
        record.businessManager === manager.name
      );

      const totalRevenue = assignedCustomers.reduce((sum, record) => {
        const customer = customerDetailsData[record.customerName];
        return sum + (customer ? customer.summary.totalExpectedRevenue : 0);
      }, 0);

      const totalPayment = assignedCustomers.reduce((sum, record) => {
        const customer = customerDetailsData[record.customerName];
        return sum + (customer ? customer.summary.totalActualPayment : 0);
      }, 0);

      return {
        id: manager.id,
        name: manager.name,
        username: manager.username,
        permission: manager.permission,
        customerCount: assignedCustomers.length,
        totalRevenue: Math.round(totalRevenue * 100) / 100,
        totalPayment: Math.round(totalPayment * 100) / 100,
        customers: assignedCustomers.map(record => record.customerName)
      };
    });

    // 计算未分配的客户
    const unassignedCustomers = salesFunnelData.filter(record =>
      !record.businessManager || record.businessManager === '未分配'
    );

    const unassignedRevenue = unassignedCustomers.reduce((sum, record) => {
      const customer = customerDetailsData[record.customerName];
      return sum + (customer ? customer.summary.totalExpectedRevenue : 0);
    }, 0);

    const unassignedPayment = unassignedCustomers.reduce((sum, record) => {
      const customer = customerDetailsData[record.customerName];
      return sum + (customer ? customer.summary.totalActualPayment : 0);
    }, 0);

    const unassignedStats = {
      name: '未分配',
      customerCount: unassignedCustomers.length,
      totalRevenue: Math.round(unassignedRevenue * 100) / 100,
      totalPayment: Math.round(unassignedPayment * 100) / 100,
      paymentRate: unassignedRevenue > 0 ? Math.round((unassignedPayment / unassignedRevenue) * 10000) / 100 : 0,
      customers: unassignedCustomers.map(record => record.customerName)
    };

    res.json({
      success: true,
      data: {
        managers: stats,
        unassigned: unassignedStats,
        summary: {
          totalManagers: businessManagersData.length,
          activeManagers: businessManagersData.filter(m => m.status === 'active').length,
          totalCustomers: salesFunnelData.length,
          assignedCustomers: salesFunnelData.filter(r => r.businessManager && r.businessManager !== '未分配').length,
          unassignedCustomers: unassignedCustomers.length
        }
      }
    });
  } catch (error) {
    console.error('获取商务负责人统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取商务负责人统计失败: ' + error.message
    });
  }
});

// ==================== 回款数据 API ====================

// 获取回款记录
router.get('/payment-records', auth.authenticateToken, async (req, res) => {
  try {
    const { region, businessManager, customer, project, month } = req.query;

    // 获取所有回款记录（独立记录 + 同步记录）
    function getAllPaymentRecords() {
      let allRecords = [...paymentRecordsData];

      // 同步客户详细信息中的回款数据
      Object.keys(customerDetailsData).forEach(customerName => {
        const customerInfo = customerDetailsData[customerName];

        // 获取客户的区域和商务负责人信息
        const salesRecord = salesFunnelData.find(record => record.customerName === customerName);
        const customerRegion = salesRecord ? salesRecord.region : '未知';
        const customerBusinessManager = salesRecord ? salesRecord.businessManager : '未分配';

        // 遍历客户的所有项目
        if (customerInfo.projects) {
          customerInfo.projects.forEach(projectData => {
            // 检查项目是否有回款记录
            if (projectData.paymentAmount && projectData.paymentAmount > 0) {
              // 检查是否已经在独立回款记录中存在
              const existingRecord = paymentRecordsData.find(record =>
                record.customerName === customerName &&
                record.projectName === projectData.projectName &&
                record.paymentAmount === projectData.paymentAmount &&
                record.paymentDate === (projectData.paymentTime || projectData.actualSignTime)
              );

              // 如果不存在，则添加到回款记录中
              if (!existingRecord) {
                allRecords.push({
                  id: `sync_${customerName}_${projectData.id}`, // 使用特殊ID标识同步数据
                  customerName: customerName,
                  region: customerRegion,
                  businessManager: customerBusinessManager,
                  projectName: projectData.projectName,
                  contractNumber: null, // 客户详细信息中没有合同号
                  invoiceDate: projectData.invoiceDate || null, // 使用客户详细信息中的开票日期
                  contractDate: projectData.actualSignTime || projectData.expectedSignMonth,
                  paymentDate: projectData.paymentTime || projectData.actualSignTime,
                  paymentAmount: projectData.paymentAmount,
                  invoiceEntity: null, // 客户详细信息中没有开票主体
                  contractRevenue: 0, // 客户详细信息中没有这些字段
                  contractFinalPayment: 0,
                  invoiceRevenue: 0,
                  paymentNote: projectData.paymentRemarks || '来自客户详细信息',
                  source: 'customer_details', // 标识数据来源
                  createdAt: projectData.createdAt || new Date().toISOString(),
                  updatedAt: projectData.updatedAt || new Date().toISOString()
                });
              }
            }
          });
        }
      });

      return allRecords;
    }

    // 获取所有回款记录
    const allPaymentRecords = getAllPaymentRecords();

    // 根据用户权限过滤数据
    let filteredRecords = await auth.filterDataByPermission(allPaymentRecords, req.user, 'customerName');

    if (region && region !== 'all') {
      filteredRecords = filteredRecords.filter(record => record.region === region);
    }

    if (businessManager && businessManager !== 'all') {
      const decodedManager = decodeURIComponent(businessManager);
      filteredRecords = filteredRecords.filter(record => record.businessManager === decodedManager);
    }

    if (customer) {
      const decodedCustomer = decodeURIComponent(customer);
      const searchTerm = decodedCustomer.toLowerCase();
      filteredRecords = filteredRecords.filter(record =>
        record.customerName.toLowerCase().includes(searchTerm)
      );
    }

    if (project) {
      const searchTerm = project.toLowerCase();
      filteredRecords = filteredRecords.filter(record =>
        record.projectName.toLowerCase().includes(searchTerm)
      );
    }

    if (month) {
      filteredRecords = filteredRecords.filter(record => {
        if (!record.paymentDate) return false;
        const recordMonth = record.paymentDate.substring(0, 7); // YYYY-MM
        return recordMonth === month;
      });
    }

    // 按回款日期排序（最新的在前）
    filteredRecords.sort((a, b) => {
      if (!a.paymentDate && !b.paymentDate) return 0;
      if (!a.paymentDate) return 1;
      if (!b.paymentDate) return -1;
      return new Date(b.paymentDate) - new Date(a.paymentDate);
    });

    // 计算统计信息
    const totalAmount = filteredRecords.reduce((sum, record) => sum + (record.paymentAmount || 0), 0);
    const currentMonth = new Date().toISOString().substring(0, 7);
    const monthlyAmount = filteredRecords
      .filter(record => record.paymentDate && record.paymentDate.substring(0, 7) === currentMonth)
      .reduce((sum, record) => sum + (record.paymentAmount || 0), 0);

    res.json({
      success: true,
      data: filteredRecords,
      stats: {
        totalRecords: filteredRecords.length,
        totalAmount: Math.round(totalAmount * 100) / 100,
        monthlyAmount: Math.round(monthlyAmount * 100) / 100,
        averageAmount: filteredRecords.length > 0 ? Math.round((totalAmount / filteredRecords.length) * 100) / 100 : 0
      }
    });
  } catch (error) {
    console.error('获取回款记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取回款记录失败: ' + error.message
    });
  }
});

// 新增回款记录
router.post('/payment-records', auth.authenticateToken, auth.checkCustomerAccess, async (req, res) => {
  try {
    const {
      customerName,
      projectName,
      contractNumber,
      businessManager,
      invoiceDate,
      contractDate,
      paymentAmount,
      paymentDate,
      invoiceEntity,
      contractRevenue,
      contractFinalPayment,
      invoiceRevenue
    } = req.body;

    // 验证必填字段
    if (!customerName || !projectName || !paymentAmount || !paymentDate) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段：客户名称、项目名称、收款金额、收款日期'
      });
    }

    // 验证回款金额
    const amount = parseFloat(paymentAmount);
    if (amount <= 0) {
      return res.status(400).json({
        success: false,
        message: '收款金额必须大于0'
      });
    }

    // 验证日期格式
    const paymentDateObj = new Date(paymentDate);
    if (isNaN(paymentDateObj.getTime())) {
      return res.status(400).json({
        success: false,
        message: '收款日期格式不正确'
      });
    }

    // 验证签约日期格式（如果提供）
    if (contractDate) {
      const contractDateObj = new Date(contractDate);
      if (isNaN(contractDateObj.getTime())) {
        return res.status(400).json({
          success: false,
          message: '签约日期格式不正确'
        });
      }
    }

    // 获取客户的区域信息
    const salesRecord = salesFunnelData.find(record => record.customerName === customerName);
    const customerRegion = salesRecord ? salesRecord.region : '未知';

    // 使用传入的商务经理，如果没有则使用销售记录中的，最后默认为未分配
    const finalBusinessManager = businessManager || (salesRecord ? salesRecord.businessManager : '未分配');

    // 生成新的回款记录ID
    const newId = Math.max(...paymentRecordsData.map(record => record.id), 0) + 1;

    // 创建新的回款记录
    const newPaymentRecord = {
      id: newId,
      customerName: customerName,
      region: customerRegion,
      businessManager: finalBusinessManager,
      projectName: projectName,
      contractNumber: contractNumber || null,
      invoiceDate: invoiceDate || null,
      contractDate: contractDate || null,
      paymentDate: paymentDate,
      paymentAmount: amount,
      invoiceEntity: invoiceEntity || null,
      contractRevenue: parseFloat(contractRevenue) || 0,
      contractFinalPayment: parseFloat(contractFinalPayment) || 0,
      invoiceRevenue: parseFloat(invoiceRevenue) || 0,
      source: 'independent', // 标记为独立回款记录
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 添加到回款记录数据中
    paymentRecordsData.push(newPaymentRecord);

    // 保存数据
    try {
    await mongoManager.savePaymentRecordsData(paymentRecordsData);
    console.log('✅ 回款记录数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存回款记录数据到MongoDB失败:', err);
  }

    res.status(201).json({
      success: true,
      data: newPaymentRecord,
      message: '回款记录添加成功'
    });
  } catch (error) {
    console.error('新增回款记录失败:', error);
    res.status(500).json({
      success: false,
      message: '新增回款记录失败: ' + error.message
    });
  }
});

// 更新回款记录
router.put('/payment-records/:id', auth.authenticateToken, async (req, res) => {
  try {
    const recordId = parseInt(req.params.id);
    const {
      customerName,
      projectName,
      contractNumber,
      businessManager,
      invoiceDate,
      contractDate,
      paymentAmount,
      paymentDate,
      invoiceEntity,
      contractRevenue,
      contractFinalPayment,
      invoiceRevenue
    } = req.body;

    // 查找要更新的记录
    const recordIndex = paymentRecordsData.findIndex(record => record.id === recordId);
    if (recordIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '找不到要更新的回款记录'
      });
    }

    const existingRecord = paymentRecordsData[recordIndex];

    // 检查权限：只能编辑手动新增的记录
    if (existingRecord.source !== 'independent') {
      return res.status(403).json({
        success: false,
        message: '只能编辑手动新增的回款记录'
      });
    }

    // 检查用户权限：管理员或对应的商务负责人
    if (req.user.permission !== 'admin' &&
        (req.user.permission !== 'manager' || req.user.name !== existingRecord.businessManager)) {
      return res.status(403).json({
        success: false,
        message: '没有权限编辑此回款记录'
      });
    }

    // 验证必填字段
    if (!customerName || !projectName || !paymentAmount || !paymentDate) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段：客户名称、项目名称、收款金额、收款日期'
      });
    }

    // 验证回款金额
    const amount = parseFloat(paymentAmount);
    if (amount <= 0) {
      return res.status(400).json({
        success: false,
        message: '收款金额必须大于0'
      });
    }

    // 获取客户区域信息
    const salesRecord = salesFunnelData.find(record => record.customerName === customerName);
    const customerRegion = salesRecord ? salesRecord.region : '未知';

    // 更新记录
    const updatedRecord = {
      ...existingRecord,
      customerName: customerName,
      region: customerRegion,
      businessManager: businessManager || existingRecord.businessManager,
      projectName: projectName,
      contractNumber: contractNumber || null,
      invoiceDate: invoiceDate || null,
      contractDate: contractDate || null,
      paymentDate: paymentDate,
      paymentAmount: amount,
      invoiceEntity: invoiceEntity || null,
      contractRevenue: parseFloat(contractRevenue) || 0,
      contractFinalPayment: parseFloat(contractFinalPayment) || 0,
      invoiceRevenue: parseFloat(invoiceRevenue) || 0,
      updatedAt: new Date().toISOString()
    };

    // 更新数据
    paymentRecordsData[recordIndex] = updatedRecord;

    // 保存数据
    try {
      await mongoManager.savePaymentRecordsData(paymentRecordsData);
      console.log('✅ 回款记录数据已更新到MongoDB');
    } catch (err) {
      console.error('❌ 更新回款记录数据到MongoDB失败:', err);
    }

    res.json({
      success: true,
      data: updatedRecord,
      message: '回款记录更新成功'
    });
  } catch (error) {
    console.error('更新回款记录失败:', error);
    res.status(500).json({
      success: false,
      message: '更新回款记录失败: ' + error.message
    });
  }
});

// 删除回款记录
router.delete('/payment-records/:id', auth.authenticateToken, async (req, res) => {
  try {
    const recordId = parseInt(req.params.id);

    // 查找要删除的记录
    const recordIndex = paymentRecordsData.findIndex(record => record.id === recordId);
    if (recordIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '找不到要删除的回款记录'
      });
    }

    const existingRecord = paymentRecordsData[recordIndex];

    // 检查权限：只能删除手动新增的记录
    if (existingRecord.source !== 'independent') {
      return res.status(403).json({
        success: false,
        message: '只能删除手动新增的回款记录'
      });
    }

    // 检查用户权限：管理员或对应的商务负责人
    if (req.user.permission !== 'admin' &&
        (req.user.permission !== 'manager' || req.user.name !== existingRecord.businessManager)) {
      return res.status(403).json({
        success: false,
        message: '没有权限删除此回款记录'
      });
    }

    // 删除记录
    paymentRecordsData.splice(recordIndex, 1);

    // 保存数据
    try {
      await mongoManager.savePaymentRecordsData(paymentRecordsData);
      console.log('✅ 回款记录数据已删除并保存到MongoDB');
    } catch (err) {
      console.error('❌ 删除回款记录数据到MongoDB失败:', err);
    }

    res.json({
      success: true,
      message: '回款记录删除成功'
    });
  } catch (error) {
    console.error('删除回款记录失败:', error);
    res.status(500).json({
      success: false,
      message: '删除回款记录失败: ' + error.message
    });
  }
});

// 获取回款统计数据
router.get('/payment-stats', auth.authenticateToken, (req, res) => {
  try {
    const { period = 'month' } = req.query; // month, quarter, year
    let paymentStats = {};
    let allPaymentRecords = [...paymentRecordsData];

    // 同步客户详细信息中的回款数据到统计中
    Object.keys(customerDetailsData).forEach(customerName => {
      const customer = customerDetailsData[customerName];

      if (customer.projects) {
        customer.projects.forEach(project => {
          if (project.paymentAmount && project.paymentAmount > 0) {
            const paymentDate = project.paymentTime || project.actualSignTime;
            if (paymentDate) {
              // 检查是否已经在独立回款记录中存在
              const existingRecord = paymentRecordsData.find(record =>
                record.customerName === customerName &&
                record.projectName === project.projectName &&
                record.paymentAmount === project.paymentAmount &&
                record.paymentDate === paymentDate
              );

              // 如果不存在，则添加到统计中
              if (!existingRecord) {
                allPaymentRecords.push({
                  customerName: customerName,
                  paymentAmount: project.paymentAmount,
                  paymentDate: paymentDate
                });
              }
            }
          }
        });
      }
    });

    // 遍历所有回款记录数据（包括同步的），统计回款数据
    allPaymentRecords.forEach(record => {
      if (record.paymentAmount && record.paymentAmount > 0 && record.paymentDate) {
        const date = new Date(record.paymentDate);
        let periodKey;

        switch (period) {
          case 'month':
            periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            break;
          case 'quarter':
            const quarter = Math.ceil((date.getMonth() + 1) / 3);
            periodKey = `${date.getFullYear()}-Q${quarter}`;
            break;
          case 'year':
            periodKey = String(date.getFullYear());
            break;
          default:
            periodKey = record.paymentDate;
        }

        if (!paymentStats[periodKey]) {
          paymentStats[periodKey] = {
            period: periodKey,
            totalAmount: 0,
            recordCount: 0,
            customers: new Set()
          };
        }

        paymentStats[periodKey].totalAmount += record.paymentAmount;
        paymentStats[periodKey].recordCount += 1;
        paymentStats[periodKey].customers.add(record.customerName);
      }
    });

    // 转换为数组并处理Set
    const statsArray = Object.values(paymentStats).map(stat => ({
      period: stat.period,
      totalAmount: Math.round(stat.totalAmount * 100) / 100,
      recordCount: stat.recordCount,
      customerCount: stat.customers.size,
      averageAmount: Math.round((stat.totalAmount / stat.recordCount) * 100) / 100
    }));

    // 按时间排序
    statsArray.sort((a, b) => a.period.localeCompare(b.period));

    res.json({
      success: true,
      data: statsArray,
      period: period
    });
  } catch (error) {
    console.error('获取回款统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取回款统计失败: ' + error.message
    });
  }
});

// ==================== 执行计划管理API ====================

// 获取执行计划列表
router.get('/execution-plans', auth.authenticateToken, (req, res) => {
    try {
        // 使用内存中的执行计划数据，而不是从JSON文件加载

        // 根据用户权限过滤数据
        let filteredPlans = executionPlansData;
        if (req.user.permission === 'manager' && req.user.customers) {
            filteredPlans = executionPlansData.filter(plan =>
                req.user.customers.includes(plan.customerName)
            );
        }

        // 支持按客户名称过滤
        const { customerName } = req.query;
        if (customerName) {
            filteredPlans = filteredPlans.filter(plan =>
                plan.customerName === customerName
            );
        }

        res.json({
            success: true,
            data: filteredPlans
        });
    } catch (error) {
        console.error('获取执行计划失败:', error);
        res.status(500).json({
            success: false,
            message: '获取执行计划失败'
        });
    }
});

// 获取单个执行计划
router.get('/execution-plans/:id', auth.authenticateToken, (req, res) => {
    try {
        const id = parseInt(req.params.id);
        // 使用内存中的执行计划数据
        const plan = executionPlansData.find(p => p.id === id);

        if (!plan) {
            return res.status(404).json({
                success: false,
                message: '执行计划不存在'
            });
        }

        // 检查用户权限
        if (req.user.permission === 'manager' && req.user.customers) {
            if (!req.user.customers.includes(plan.customerName)) {
                return res.status(403).json({
                    success: false,
                    message: '没有权限访问该执行计划'
                });
            }
        }

        res.json({
            success: true,
            data: plan
        });
    } catch (error) {
        console.error('获取执行计划详情失败:', error);
        res.status(500).json({
            success: false,
            message: '获取执行计划详情失败'
        });
    }
});

// 创建执行计划
router.post('/execution-plans', auth.authenticateToken, async (req, res) => {
    try {
        const { customerName, deadline, planDescription, expectedEffect, actualEffect, executionStatus } = req.body;

        if (!customerName || !deadline || !planDescription || !expectedEffect) {
            return res.status(400).json({
                success: false,
                message: '客户名称、截止时间、计划说明和预期效果为必填项'
            });
        }

        // 检查用户权限
        if (req.user.permission === 'manager' && req.user.customers) {
            if (!req.user.customers.includes(customerName)) {
                return res.status(403).json({
                    success: false,
                    message: '没有权限为该客户创建执行计划'
                });
            }
        }

        // 使用内存中的执行计划数据
        const newId = executionPlansData.length > 0 ? Math.max(...executionPlansData.map(p => p.id)) + 1 : 1;

        const newPlan = {
            id: newId,
            customerName,
            deadline,
            planDescription,
            expectedEffect,
            actualEffect: actualEffect || '',
            executionStatus: executionStatus || '未完成',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        executionPlansData.push(newPlan);
        try {
            await mongoManager.saveExecutionPlansData(executionPlansData);
            console.log('✅ 执行计划数据已保存到MongoDB');
        } catch (err) {
            console.error('❌ 保存执行计划数据到MongoDB失败:', err);
        }

        res.status(201).json({
            success: true,
            data: newPlan,
            message: '执行计划创建成功'
        });
    } catch (error) {
        console.error('创建执行计划失败:', error);
        res.status(500).json({
            success: false,
            message: '创建执行计划失败'
        });
    }
});

// 更新执行计划
router.put('/execution-plans/:id', auth.authenticateToken, async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const { customerName, deadline, planDescription, expectedEffect, actualEffect, executionStatus } = req.body;

        // 使用内存中的执行计划数据
        const planIndex = executionPlansData.findIndex(p => p.id === id);

        if (planIndex === -1) {
            return res.status(404).json({
                success: false,
                message: '执行计划不存在'
            });
        }

        const existingPlan = executionPlansData[planIndex];

        // 检查用户权限
        if (req.user.permission === 'manager' && req.user.customers) {
            if (!req.user.customers.includes(existingPlan.customerName)) {
                return res.status(403).json({
                    success: false,
                    message: '没有权限修改该执行计划'
                });
            }
        }

        // 更新执行计划
        const updatedPlan = {
            ...existingPlan,
            customerName: customerName || existingPlan.customerName,
            deadline: deadline || existingPlan.deadline,
            planDescription: planDescription || existingPlan.planDescription,
            expectedEffect: expectedEffect || existingPlan.expectedEffect,
            actualEffect: actualEffect !== undefined ? actualEffect : existingPlan.actualEffect,
            executionStatus: executionStatus || existingPlan.executionStatus,
            updatedAt: new Date().toISOString()
        };

        executionPlansData[planIndex] = updatedPlan;
        try {
            await mongoManager.saveExecutionPlansData(executionPlansData);
            console.log('✅ 执行计划数据已保存到MongoDB');
        } catch (err) {
            console.error('❌ 保存执行计划数据到MongoDB失败:', err);
        }

        res.json({
            success: true,
            data: updatedPlan,
            message: '执行计划更新成功'
        });
    } catch (error) {
        console.error('更新执行计划失败:', error);
        res.status(500).json({
            success: false,
            message: '更新执行计划失败'
        });
    }
});

// 删除执行计划
router.delete('/execution-plans/:id', auth.authenticateToken, async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        // 使用内存中的执行计划数据
        const planIndex = executionPlansData.findIndex(p => p.id === id);

        if (planIndex === -1) {
            return res.status(404).json({
                success: false,
                message: '执行计划不存在'
            });
        }

        const existingPlan = executionPlansData[planIndex];

        // 检查用户权限
        if (req.user.permission === 'manager' && req.user.customers) {
            if (!req.user.customers.includes(existingPlan.customerName)) {
                return res.status(403).json({
                    success: false,
                    message: '没有权限删除该执行计划'
                });
            }
        }

        executionPlansData.splice(planIndex, 1);
        try {
            await mongoManager.saveExecutionPlansData(executionPlansData);
            console.log('✅ 执行计划数据已保存到MongoDB');
        } catch (err) {
            console.error('❌ 保存执行计划数据到MongoDB失败:', err);
        }

        res.json({
            success: true,
            message: '执行计划删除成功'
        });
    } catch (error) {
        console.error('删除执行计划失败:', error);
        res.status(500).json({
            success: false,
            message: '删除执行计划失败'
        });
    }
});

// ==================== Q&A管理API ====================
// 获取Q&A列表
router.get('/qa', auth.authenticateToken, (req, res) => {
    console.log('Q&A列表API被调用');
    try {
        const { category, search, sortBy = 'createdAt', order = 'desc' } = req.query;

        let filteredQA = [...qaData];

        // 按分类筛选
        if (category && category !== 'all') {
            filteredQA = filteredQA.filter(qa => qa.category === category);
        }

        // 按关键词搜索
        if (search) {
            const searchLower = search.toLowerCase();
            filteredQA = filteredQA.filter(qa =>
                qa.question.toLowerCase().includes(searchLower) ||
                qa.answer.toLowerCase().includes(searchLower) ||
                qa.tags.some(tag => tag.toLowerCase().includes(searchLower))
            );
        }

        // 排序
        filteredQA.sort((a, b) => {
            let aValue = a[sortBy];
            let bValue = b[sortBy];

            if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            }

            if (order === 'desc') {
                return bValue > aValue ? 1 : -1;
            }
            return aValue > bValue ? 1 : -1;
        });

        res.json({
            success: true,
            data: filteredQA,
            total: filteredQA.length
        });
    } catch (error) {
        console.error('获取Q&A列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取Q&A列表失败'
        });
    }
});

// 获取Q&A分类列表（必须在 /qa/:id 之前定义）
router.get('/qa/categories/list', auth.authenticateToken, (req, res) => {
    try {
        console.log('Q&A分类列表API被调用');
        const categories = [...new Set(qaData.map(qa => qa.category))].sort();
        res.json({
            success: true,
            data: categories
        });
    } catch (error) {
        console.error('获取分类列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取分类列表失败'
        });
    }
});



// 获取单个Q&A
router.get('/qa/:id', auth.authenticateToken, (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const qa = qaData.find(item => item.id === id);

        if (!qa) {
            return res.status(404).json({
                success: false,
                message: 'Q&A不存在'
            });
        }

        res.json({
            success: true,
            data: qa
        });
    } catch (error) {
        console.error('获取Q&A详情失败:', error);
        res.status(500).json({
            success: false,
            message: '获取Q&A详情失败'
        });
    }
});

// 创建Q&A
router.post('/qa', auth.authenticateToken, async (req, res) => {
    try {
        const { question, answer, category, tags } = req.body;

        if (!question || !answer) {
            return res.status(400).json({
                success: false,
                message: '问题和答案为必填项'
            });
        }

        const newId = qaData.length > 0 ? Math.max(...qaData.map(qa => qa.id)) + 1 : 1;

        const newQA = {
            id: newId,
            question: question.trim(),
            answer: answer.trim(),
            category: category || '常见问题',
            tags: Array.isArray(tags) ? tags : (tags ? tags.split(',').map(tag => tag.trim()) : []),
            author: req.user.name || req.user.username,
            authorId: req.user.id,
            viewCount: 0,
            helpful: 0,
            notHelpful: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        qaData.push(newQA);
        try {
    await mongoManager.saveQAData(qaData);
    console.log('✅ Q&A数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存Q&A数据到MongoDB失败:', err);
  }

        res.status(201).json({
            success: true,
            data: newQA,
            message: 'Q&A创建成功'
        });
    } catch (error) {
        console.error('创建Q&A失败:', error);
        res.status(500).json({
            success: false,
            message: '创建Q&A失败'
        });
    }
});

// 更新Q&A
router.put('/qa/:id', auth.authenticateToken, async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const { question, answer, category, tags } = req.body;

        const qaIndex = qaData.findIndex(qa => qa.id === id);

        if (qaIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Q&A不存在'
            });
        }

        const existingQA = qaData[qaIndex];

        // 更新Q&A
        const updatedQA = {
            ...existingQA,
            question: question ? question.trim() : existingQA.question,
            answer: answer ? answer.trim() : existingQA.answer,
            category: category || existingQA.category,
            tags: Array.isArray(tags) ? tags : (tags ? tags.split(',').map(tag => tag.trim()) : existingQA.tags),
            updatedAt: new Date().toISOString()
        };

        qaData[qaIndex] = updatedQA;
        try {
    await mongoManager.saveQAData(qaData);
    console.log('✅ Q&A数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存Q&A数据到MongoDB失败:', err);
  }

        res.json({
            success: true,
            data: updatedQA,
            message: 'Q&A更新成功'
        });
    } catch (error) {
        console.error('更新Q&A失败:', error);
        res.status(500).json({
            success: false,
            message: '更新Q&A失败'
        });
    }
});

// 删除Q&A
router.delete('/qa/:id', auth.authenticateToken, async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const qaIndex = qaData.findIndex(qa => qa.id === id);

        if (qaIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Q&A不存在'
            });
        }

        const deletedQA = qaData.splice(qaIndex, 1)[0];
        try {
    await mongoManager.saveQAData(qaData);
    console.log('✅ Q&A数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存Q&A数据到MongoDB失败:', err);
  }

        res.json({
            success: true,
            data: deletedQA,
            message: 'Q&A删除成功'
        });
    } catch (error) {
        console.error('删除Q&A失败:', error);
        res.status(500).json({
            success: false,
            message: '删除Q&A失败'
        });
    }
});

// 增加Q&A浏览次数
router.post('/qa/:id/view', auth.authenticateToken, async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const qaIndex = qaData.findIndex(qa => qa.id === id);

        if (qaIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Q&A不存在'
            });
        }

        qaData[qaIndex].viewCount = (qaData[qaIndex].viewCount || 0) + 1;
        try {
    await mongoManager.saveQAData(qaData);
    console.log('✅ Q&A数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存Q&A数据到MongoDB失败:', err);
  }

        res.json({
            success: true,
            data: { viewCount: qaData[qaIndex].viewCount }
        });
    } catch (error) {
        console.error('更新浏览次数失败:', error);
        res.status(500).json({
            success: false,
            message: '更新浏览次数失败'
        });
    }
});

// Q&A评价（有用/无用）
router.post('/qa/:id/rate', auth.authenticateToken, async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const { helpful } = req.body; // true为有用，false为无用

        const qaIndex = qaData.findIndex(qa => qa.id === id);

        if (qaIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Q&A不存在'
            });
        }

        if (helpful === true) {
            qaData[qaIndex].helpful = (qaData[qaIndex].helpful || 0) + 1;
        } else if (helpful === false) {
            qaData[qaIndex].notHelpful = (qaData[qaIndex].notHelpful || 0) + 1;
        }

        try {
    await mongoManager.saveQAData(qaData);
    console.log('✅ Q&A数据已保存到MongoDB');
  } catch (err) {
    console.error('❌ 保存Q&A数据到MongoDB失败:', err);
  }

        res.json({
            success: true,
            data: {
                helpful: qaData[qaIndex].helpful,
                notHelpful: qaData[qaIndex].notHelpful
            }
        });
    } catch (error) {
        console.error('Q&A评价失败:', error);
        res.status(500).json({
            success: false,
            message: 'Q&A评价失败'
        });
    }
});

module.exports = router;
