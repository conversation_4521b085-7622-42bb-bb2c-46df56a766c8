const express = require('express');
const router = express.Router();

// 主页路由
router.get('/', (req, res) => {
  res.json({
    message: '欢迎使用销售漏斗管理系统',
    version: '1.0.0',
    endpoints: {
      api: '/api',
      health: '/health'
    }
  });
});

// 健康检查路由
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

module.exports = router;
