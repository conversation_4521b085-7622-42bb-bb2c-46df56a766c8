/* 通用响应式弹窗样式 */

/* 基础弹窗样式 */
.modal, .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none; /* 默认隐藏 */
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: 1rem;
    box-sizing: border-box;
    font-size: 14px; /* 调小全局字体大小 */
}

/* 显示状态的弹窗 */
.modal.show, .modal-overlay.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: modalSlideIn 0.3s ease-out;
    font-size: 14px; /* 确保弹窗内容使用较小字体 */
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 弹窗头部 */
.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 10px 10px 0 0;
}

.modal-header h2,
.modal-header h3 {
    margin: 0;
    font-size: 1.1rem; /* 调小标题字体 */
    font-weight: 600;
}

.modal-close, .close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover, .close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 弹窗主体 */
.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
    font-size: 0.8rem; /* 调小标签字体 */
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.6rem; /* 调小内边距 */
    border: 2px solid #e0e0e0;
    border-radius: 5px;
    font-size: 0.9rem; /* 调小输入框字体 */
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
    max-height: 200px;
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
    flex-shrink: 0;
}

.form-actions button {
    padding: 0.6rem 1.2rem; /* 调小按钮内边距 */
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem; /* 调小按钮字体 */
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 90px; /* 调小最小宽度 */
}

.form-actions button[type="submit"],
.form-actions .btn-submit,
.form-actions .btn-primary {
    background: #667eea;
    color: white;
}

.form-actions button[type="submit"]:hover,
.form-actions .btn-submit:hover,
.form-actions .btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.form-actions button[type="button"],
.form-actions .btn-cancel,
.form-actions .btn-secondary {
    background: #6c757d;
    color: white;
}

.form-actions button[type="button"]:hover,
.form-actions .btn-cancel:hover,
.form-actions .btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* 平板设备适配 (768px - 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
    .modal-content {
        max-width: 80%;
        max-height: 85vh;
    }
    
    .modal-header {
        padding: 1.25rem;
    }
    
    .modal-body {
        padding: 1.25rem;
    }
}

/* 手机端适配 (最大768px) */
@media (max-width: 768px) {
    .modal, .modal-overlay {
        padding: 0.5rem;
        align-items: flex-start;
        padding-top: 2rem;
    }
    
    .modal-content {
        max-width: 100%;
        width: 100%;
        max-height: calc(100vh - 4rem);
        margin: 0;
        border-radius: 10px 10px 0 0;
    }
    
    .modal-header {
        padding: 1rem;
    }
    
    .modal-header h2,
    .modal-header h3 {
        font-size: 1rem; /* 手机端进一步调小标题 */
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .form-row .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .form-actions button {
        width: 100%;
        padding: 0.8rem; /* 调小手机端按钮内边距 */
        font-size: 1rem; /* 调小手机端按钮字体 */
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.8rem; /* 调小手机端输入框内边距 */
        font-size: 0.9rem; /* 调小手机端输入框字体 */
    }
}

/* 小屏手机适配 (最大480px) */
@media (max-width: 480px) {
    .modal, .modal-overlay {
        padding: 0.25rem;
        padding-top: 1rem;
    }
    
    .modal-content {
        max-height: calc(100vh - 2rem);
        border-radius: 8px 8px 0 0;
    }
    
    .modal-header {
        padding: 0.75rem;
    }
    
    .modal-header h2,
    .modal-header h3 {
        font-size: 0.9rem; /* 小屏手机进一步调小标题 */
    }
    
    .modal-close, .close {
        width: 32px;
        height: 32px;
        font-size: 1.2rem;
    }
    
    .modal-body {
        padding: 0.75rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-group label {
        font-size: 0.75rem; /* 调小小屏手机标签字体 */
        margin-bottom: 0.4rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.7rem; /* 调小小屏手机输入框内边距 */
        font-size: 0.85rem; /* 调小小屏手机输入框字体 */
    }
    
    .form-actions {
        margin-top: 1.5rem;
        padding-top: 0.75rem;
    }
    
    .form-actions button {
        padding: 0.7rem; /* 调小小屏手机按钮内边距 */
        font-size: 0.9rem; /* 调小小屏手机按钮字体 */
    }
}

/* 超小屏设备适配 (最大360px) */
@media (max-width: 360px) {
    .modal, .modal-overlay {
        padding: 0.125rem;
        padding-top: 0.5rem;
    }
    
    .modal-content {
        max-height: calc(100vh - 1rem);
        border-radius: 6px 6px 0 0;
    }
    
    .modal-header {
        padding: 0.5rem;
    }
    
    .modal-body {
        padding: 0.5rem;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.6rem; /* 调小超小屏输入框内边距 */
        font-size: 0.8rem; /* 调小超小屏输入框字体 */
    }
}

/* 高度受限设备适配 */
@media (max-height: 600px) {
    .modal, .modal-overlay {
        align-items: flex-start;
        padding-top: 1rem;
    }
    
    .modal-content {
        max-height: calc(100vh - 2rem);
    }
    
    .modal-header {
        padding: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
}

/* 横屏手机适配 */
@media (max-height: 500px) and (orientation: landscape) {
    .modal, .modal-overlay {
        padding: 0.5rem;
        padding-top: 0.5rem;
    }
    
    .modal-content {
        max-height: calc(100vh - 1rem);
    }
    
    .modal-header {
        padding: 0.75rem;
    }
    
    .modal-body {
        padding: 0.75rem;
    }
    
    .form-actions {
        margin-top: 1rem;
        padding-top: 0.75rem;
    }
}
