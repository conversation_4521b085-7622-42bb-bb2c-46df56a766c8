<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户组织架构管理 - 销售漏斗管理系统</title>
    <link rel="stylesheet" href="css/modal-responsive.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* 主容器样式 */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }



        .page-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .page-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        /* 客户选择区域 */
        .customer-selector {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .selector-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .selector-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }

        .customer-select {
            padding: 0.75rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            min-width: 200px;
            transition: border-color 0.3s ease;
        }

        .customer-select:focus {
            outline: none;
            border-color: #667eea;
        }

        /* 组织架构区域 */
        .org-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .org-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .org-title {
            font-size: 1.3rem;
            font-weight: 600;
        }

        .add-dept-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-dept-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .org-content {
            padding: 2rem;
            min-height: 400px;
        }

        /* 组织树样式 */
        .org-tree {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .tree-node {
            margin: 0.5rem 0;
        }

        .node-content {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .node-content:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .node-toggle {
            margin-right: 0.5rem;
            font-size: 0.8rem;
            color: #666;
            cursor: pointer;
            width: 20px;
            text-align: center;
        }

        .node-icon {
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }

        .node-info {
            flex: 1;
        }

        .node-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .node-details {
            font-size: 0.85rem;
            color: #666;
        }

        .node-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            margin: 0.1rem;
        }

        .btn-add {
            background: #28a745;
            color: white;
        }

        .btn-edit {
            background: #ffc107;
            color: #333;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-employees {
            background: #17a2b8;
            color: white;
        }

        .action-btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }

        .tree-children {
            margin-left: 2rem;
            border-left: 2px solid #e9ecef;
            padding-left: 1rem;
            margin-top: 0.5rem;
        }

        .tree-children.collapsed {
            display: none;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-text {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .empty-subtext {
            font-size: 0.9rem;
            opacity: 0.7;
        }

        /* 人员列表样式 */
        .employees-section {
            margin-top: 0.5rem;
            padding-left: 2rem;
            border-left: 2px solid #e9ecef;
        }

        .employee-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
        }

        .employee-item:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .employee-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.75rem;
            font-size: 0.9rem;
        }

        .employee-info {
            flex: 1;
        }

        .employee-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.1rem;
        }

        .employee-position {
            font-size: 0.8rem;
            color: #666;
        }

        .employee-actions {
            display: flex;
            gap: 0.25rem;
        }

        .employee-actions .action-btn {
            padding: 0.2rem 0.4rem;
            font-size: 0.7rem;
        }

        /* 组织架构特定的弹窗样式补充 */

        /* 富文本编辑器样式 */
        .rich-text-editor {
            border: 2px solid #e0e0e0;
            border-radius: 5px;
            min-height: 150px;
        }

        .editor-toolbar {
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            padding: 0.5rem;
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .editor-btn {
            padding: 0.25rem 0.5rem;
            border: 1px solid #ddd;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .editor-btn:hover {
            background: #e9ecef;
        }

        .editor-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .editor-content {
            padding: 1rem;
            min-height: 100px;
            outline: none;
            line-height: 1.5;
        }

        .editor-content:focus {
            background: #fafafa;
        }

        .editor-content:empty:before {
            content: attr(placeholder);
            color: #999;
            font-style: italic;
        }

        .editor-content:focus:empty:before {
            content: '';
        }

        /* 性别选择样式 */
        .gender-options {
            display: flex;
            gap: 1rem;
            margin-top: 0.5rem;
        }

        .gender-option {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .gender-option input[type="radio"] {
            width: auto;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                transform: translateX(-100%);
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 1000;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .sidebar-toggle {
                display: block;
            }

            .main-content {
                padding: 1rem;
            }

            .tree-children {
                margin-left: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 主内容区域 -->
    <div class="main-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">
                    <span>🏢</span>
                    客户组织架构管理
                </h1>
                <p class="page-subtitle">管理客户的组织架构和部门层级关系</p>
            </div>

            <!-- 客户选择器 -->
            <div class="customer-selector">
                <div class="selector-header">
                    <h3 class="selector-title">选择客户</h3>
                </div>
                <select id="customerSelect" class="customer-select" onchange="loadCustomerOrganization()">
                    <option value="">请选择客户...</option>
                </select>
            </div>

            <!-- 组织架构容器 -->
            <div class="org-container">
                <div class="org-header">
                    <h3 class="org-title" id="orgTitle">组织架构</h3>
                    <button class="add-dept-btn" onclick="showAddDepartmentModal()" id="addRootDeptBtn" style="display: none;">
                        ➕ 添加根部门
                    </button>
                </div>
                <div class="org-content" id="orgContent">
                    <div class="empty-state">
                        <div class="empty-icon">🏢</div>
                        <div class="empty-text">请选择客户查看组织架构</div>
                        <div class="empty-subtext">选择客户后可以管理其组织架构和部门信息</div>
                    </div>
                </div>
            </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="navbar.js"></script>
    <script src="organization.js"></script>
</body>
</html>
