const mongoManager = require('../utils/mongoManager');

async function checkProductionData() {
    try {
        console.log('🔍 检查生产环境中的证券通数据...');
        
        // 设置生产环境
        process.env.NODE_ENV = 'production';
        
        // 连接生产环境MongoDB
        await mongoManager.connect();
        
        // 加载销售数据
        const salesData = await mongoManager.loadSalesData();
        console.log(`📊 生产环境MongoDB中共有 ${salesData.length} 条销售数据`);
        
        // 查找证券通记录
        const zhengquantongRecords = salesData.filter(record => 
            record.customerName && record.customerName.includes('证券通')
        );
        
        if (zhengquantongRecords.length > 0) {
            console.log('❌ 生产环境MongoDB中仍然存在证券通相关记录:');
            zhengquantongRecords.forEach(record => {
                console.log(`   ID: ${record.id}, 客户名称: "${record.customerName}", 区域: ${record.region}, 商务经理: ${record.businessManager}`);
                console.log(`   创建时间: ${record.createdAt}, 更新时间: ${record.updatedAt}`);
            });
        } else {
            console.log('✅ 生产环境MongoDB中没有找到证券通相关记录');
        }
        
        // 检查客户详细数据
        const customerData = await mongoManager.loadCustomerData();
        console.log(`👥 生产环境MongoDB中共有 ${Object.keys(customerData).length} 个客户的详细数据`);
        
        const zhengquantongCustomers = Object.keys(customerData).filter(customerName => 
            customerName.includes('证券通')
        );
        
        if (zhengquantongCustomers.length > 0) {
            console.log('❌ 生产环境MongoDB中仍然存在证券通的客户详细数据:');
            zhengquantongCustomers.forEach(customerName => {
                const customer = customerData[customerName];
                console.log(`   客户: "${customerName}"`);
                console.log(`   项目数量: ${customer.projects ? customer.projects.length : 0}`);
                console.log(`   汇总数据: ${JSON.stringify(customer.summary, null, 2)}`);
            });
        } else {
            console.log('✅ 生产环境MongoDB中没有找到证券通的客户详细数据');
        }
        
        // 检查所有客户名称，寻找可能的相似记录
        console.log('\n🔍 检查所有客户名称中是否有相似的记录...');
        const allCustomerNames = [...new Set(salesData.map(record => record.customerName))];
        const suspiciousNames = allCustomerNames.filter(name => 
            name && (name.includes('证券') || name.includes('通'))
        );
        
        if (suspiciousNames.length > 0) {
            console.log('⚠️ 发现包含"证券"或"通"的客户名称:');
            suspiciousNames.forEach(name => {
                const records = salesData.filter(record => record.customerName === name);
                console.log(`   "${name}" - ${records.length} 条记录`);
            });
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 检查失败:', error);
        console.error('错误详情:', error.stack);
        process.exit(1);
    }
}

checkProductionData();
