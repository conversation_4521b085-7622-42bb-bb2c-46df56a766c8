<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统首页 - 销售漏斗管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #667eea;
            font-size: 1.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-name {
            font-weight: 600;
            color: #333;
        }

        .user-role {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
        }

        .logout-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
        }

        .logout-btn:hover {
            background: linear-gradient(135deg, #c82333, #a71e2a);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .welcome-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .welcome-section h2 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 2rem;
        }

        .welcome-section p {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .action-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .action-card .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .action-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1.3rem;
        }

        .action-card p {
            color: #666;
            line-height: 1.6;
        }

        .stats-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .stats-section h3 {
            color: #667eea;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat-item .number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .stat-item .label {
            color: #666;
            font-size: 0.9rem;
        }

        .admin-only {
            display: none;
        }

        .manager-only {
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏢 销售漏斗管理系统</h1>
        <div class="user-info"></div>
    </div>

    <div class="container">
        <div class="welcome-section">
            <h2>欢迎回来！</h2>
            <p>选择下方功能模块开始您的工作</p>
        </div>

        <div class="quick-actions">
            <a href="/sales-dashboard.html" class="action-card">
                <span class="icon">📊</span>
                <h3>销售仪表板</h3>
                <p>查看销售数据、统计分析和业绩报告</p>
            </a>

            <a href="/customer-detail.html" class="action-card">
                <span class="icon">👥</span>
                <h3>客户详情</h3>
                <p>查看和管理客户的详细项目信息</p>
            </a>

            <a href="/payment-records.html" class="action-card">
                <span class="icon">💰</span>
                <h3>回款数据</h3>
                <p>管理和查看所有项目的回款记录</p>
            </a>

            <a href="/organization.html" class="action-card">
                <span class="icon">🏗️</span>
                <h3>组织架构</h3>
                <p>管理客户的组织架构和人员信息</p>
            </a>

            <a href="/business-managers.html" class="action-card admin-only">
                <span class="icon">👨‍💼</span>
                <h3>商务负责人</h3>
                <p>管理商务经理和客户分配关系</p>
            </a>
        </div>

    </div>

    <script src="js/auth.js"></script>
    <script>
        // 页面加载完成后获取统计数据
        document.addEventListener('DOMContentLoaded', async function() {
            if (!authManager.isAuthenticated()) {
                return;
            }

            try {
                // 获取统计数据
                const response = await fetch('/api/stats');
                const result = await response.json();

                if (result.success && result.data.summary) {
                    const summary = result.data.summary;
                    
                }

                // 获取客户数据来计算项目总数
                const customersResponse = await fetch('/api/customers');
                const customersResult = await customersResponse.json();

                if (customersResult.success) {
                    const totalProjects = customersResult.data.reduce((sum, customer) => {
                        return sum + (customer.summary?.totalProjects || 0);
                    }, 0);
                    
                    document.getElementById('totalProjects').textContent = totalProjects;
                }

            } catch (error) {
                console.error('获取统计数据失败:', error);
            }
        });
    </script>
</body>
</html>
