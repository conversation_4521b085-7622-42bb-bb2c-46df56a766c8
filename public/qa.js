// 全局变量
let qaData = [];
let editingQAId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查认证状态
    if (!authManager.isAuthenticated()) {
        authManager.redirectToLogin();
        return;
    }
    
    loadQAData();
    loadCategories();
    
    // 绑定事件
    document.getElementById('searchInput').addEventListener('input', debounce(filterQA, 300));
    document.getElementById('categoryFilter').addEventListener('change', filterQA);
    document.getElementById('sortBy').addEventListener('change', filterQA);
    document.getElementById('qaForm').addEventListener('submit', handleFormSubmit);
    
    // 模态框点击外部关闭
    document.getElementById('qaModal').addEventListener('click', function(event) {
        if (event.target === this) {
            closeModal();
        }
    });
});

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 加载Q&A数据
async function loadQAData() {
    try {
        document.getElementById('loading').style.display = 'block';
        document.getElementById('qaList').style.display = 'none';
        document.getElementById('emptyState').style.display = 'none';
        
        const response = await authManager.authenticatedFetch('/api/qa');
        const result = await response.json();
        
        if (result.success) {
            qaData = result.data;
            renderQAList();
        } else {
            throw new Error(result.message || '获取Q&A数据失败');
        }
    } catch (error) {
        console.error('加载Q&A数据失败:', error);
        alert('加载Q&A数据失败: ' + error.message);
    } finally {
        document.getElementById('loading').style.display = 'none';
    }
}

// 加载分类列表
async function loadCategories() {
    try {
        const response = await authManager.authenticatedFetch('/api/qa/categories/list');
        const result = await response.json();
        
        if (result.success) {
            const categoryFilter = document.getElementById('categoryFilter');
            categoryFilter.innerHTML = '<option value="all">全部分类</option>';
            
            result.data.forEach(category => {
                categoryFilter.innerHTML += `<option value="${category}">${category}</option>`;
            });
        }
    } catch (error) {
        console.error('加载分类列表失败:', error);
    }
}

// 渲染Q&A列表
function renderQAList() {
    const qaList = document.getElementById('qaList');
    const emptyState = document.getElementById('emptyState');
    
    if (qaData.length === 0) {
        qaList.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }
    
    qaList.style.display = 'block';
    emptyState.style.display = 'none';
    
    qaList.innerHTML = qaData.map(qa => `
        <div class="qa-card">
            <div class="qa-header">
                <div class="qa-question">${escapeHtml(qa.question)}</div>
                <div class="qa-category">${escapeHtml(qa.category)}</div>
            </div>
            
            <div class="qa-meta">
                <span>作者：${escapeHtml(qa.author)}</span>
                <span>创建时间：${formatDate(qa.createdAt)}</span>
                ${qa.updatedAt !== qa.createdAt ? `<span>更新时间：${formatDate(qa.updatedAt)}</span>` : ''}
            </div>
            
            <div class="qa-answer">${escapeHtml(qa.answer)}</div>
            
            ${qa.tags && qa.tags.length > 0 ? `
                <div class="qa-tags">
                    ${qa.tags.map(tag => `<span class="qa-tag">${escapeHtml(tag)}</span>`).join('')}
                </div>
            ` : ''}
            
            <div class="qa-footer">
                <div class="qa-stats">
                    <span>👁️ ${qa.viewCount || 0} 次浏览</span>
                    <span>👍 ${qa.helpful || 0} 有用</span>
                    <span>👎 ${qa.notHelpful || 0} 无用</span>
                </div>
                
                <div class="qa-actions">
                    <div class="rating-buttons">
                        <button class="rating-btn helpful" onclick="rateQA(${qa.id}, true)">
                            👍 有用
                        </button>
                        <button class="rating-btn not-helpful" onclick="rateQA(${qa.id}, false)">
                            👎 无用
                        </button>
                    </div>
                    <button class="btn btn-edit" onclick="editQA(${qa.id})">编辑</button>
                    <button class="btn btn-delete" onclick="deleteQA(${qa.id})">删除</button>
                </div>
            </div>
        </div>
    `).join('');
    
    // 增加浏览次数
    qaData.forEach(qa => {
        incrementViewCount(qa.id);
    });
}

// 筛选Q&A
function filterQA() {
    const search = document.getElementById('searchInput').value;
    const category = document.getElementById('categoryFilter').value;
    const sortBy = document.getElementById('sortBy').value;
    
    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (category !== 'all') params.append('category', category);
    params.append('sortBy', sortBy);
    params.append('order', 'desc');
    
    loadFilteredQA(params);
}

// 加载筛选后的Q&A数据
async function loadFilteredQA(params) {
    try {
        const url = params.toString() ? `/api/qa?${params}` : '/api/qa';
        const response = await authManager.authenticatedFetch(url);
        const result = await response.json();
        
        if (result.success) {
            qaData = result.data;
            renderQAList();
        }
    } catch (error) {
        console.error('筛选Q&A数据失败:', error);
    }
}

// 显示添加模态框
function showAddModal() {
    document.getElementById('modalTitle').textContent = '添加Q&A';
    document.getElementById('qaForm').reset();
    editingQAId = null;
    document.getElementById('qaModal').style.display = 'block';
}

// 编辑Q&A
function editQA(qaId) {
    const qa = qaData.find(q => q.id === qaId);
    if (!qa) return;
    
    document.getElementById('modalTitle').textContent = '编辑Q&A';
    document.getElementById('questionInput').value = qa.question;
    document.getElementById('answerInput').value = qa.answer;
    document.getElementById('categoryInput').value = qa.category;
    document.getElementById('tagsInput').value = qa.tags ? qa.tags.join(', ') : '';
    
    editingQAId = qaId;
    document.getElementById('qaModal').style.display = 'block';
}

// 删除Q&A
async function deleteQA(qaId) {
    if (!confirm('确定要删除这个Q&A吗？')) return;
    
    try {
        const response = await authManager.authenticatedFetch(`/api/qa/${qaId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        if (result.success) {
            alert('Q&A删除成功');
            loadQAData();
            loadCategories();
        } else {
            throw new Error(result.message || '删除失败');
        }
    } catch (error) {
        console.error('删除Q&A失败:', error);
        alert('删除Q&A失败: ' + error.message);
    }
}

// 评价Q&A
async function rateQA(qaId, helpful) {
    try {
        const response = await authManager.authenticatedFetch(`/api/qa/${qaId}/rate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ helpful })
        });
        
        const result = await response.json();
        if (result.success) {
            // 更新本地数据
            const qa = qaData.find(q => q.id === qaId);
            if (qa) {
                qa.helpful = result.data.helpful;
                qa.notHelpful = result.data.notHelpful;
                renderQAList();
            }
        }
    } catch (error) {
        console.error('评价Q&A失败:', error);
    }
}

// 增加浏览次数
async function incrementViewCount(qaId) {
    try {
        await authManager.authenticatedFetch(`/api/qa/${qaId}/view`, {
            method: 'POST'
        });
    } catch (error) {
        console.error('更新浏览次数失败:', error);
    }
}

// 处理表单提交
async function handleFormSubmit(event) {
    event.preventDefault();
    
    const formData = {
        question: document.getElementById('questionInput').value.trim(),
        answer: document.getElementById('answerInput').value.trim(),
        category: document.getElementById('categoryInput').value.trim() || '常见问题',
        tags: document.getElementById('tagsInput').value.trim()
    };
    
    if (!formData.question || !formData.answer) {
        alert('问题和答案为必填项');
        return;
    }
    
    try {
        const url = editingQAId ? `/api/qa/${editingQAId}` : '/api/qa';
        const method = editingQAId ? 'PUT' : 'POST';
        
        const response = await authManager.authenticatedFetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        if (result.success) {
            alert(editingQAId ? 'Q&A更新成功' : 'Q&A创建成功');
            closeModal();
            loadQAData();
            loadCategories();
        } else {
            throw new Error(result.message || '保存失败');
        }
    } catch (error) {
        console.error('保存Q&A失败:', error);
        alert('保存Q&A失败: ' + error.message);
    }
}

// 关闭模态框
function closeModal() {
    document.getElementById('qaModal').style.display = 'none';
    editingQAId = null;
}

// 工具函数：HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 工具函数：格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}
