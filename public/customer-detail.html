<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户详细信息 - 销售漏斗管理系统</title>
    <link rel="stylesheet" href="css/modal-responsive.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .breadcrumb {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .customer-selector {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .customer-selector label {
            font-size: 0.9rem;
            color: #666;
            white-space: nowrap;
        }

        .customer-selector select {
            min-width: 150px;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
            background: white;
        }
        
        header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .customer-info {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 2rem;
            align-items: center;
        }
        
        .customer-basic h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .customer-meta {
            display: flex;
            gap: 2rem;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .controls {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        select, input, button {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            overflow-x: auto; /* 添加横向滚动 */
            position: relative; /* 为固定列提供定位上下文 */
        }

        table {
            width: 100%;
            min-width: 1800px; /* 设置最小宽度以适应所有列 */
            border-collapse: collapse;
            font-size: 0.85rem; /* 稍微减小字体以适应更多内容 */
            position: relative; /* 为固定列提供定位上下文 */
        }
        
        th, td {
            padding: 0.5rem; /* 减小内边距以节省空间 */
            text-align: left;
            border-bottom: 1px solid #eee;
            white-space: nowrap; /* 防止文本换行 */
            overflow: hidden;
            text-overflow: ellipsis; /* 文本溢出时显示省略号 */
        }

        /* 固定列样式 */
        .fixed-left {
            position: sticky;
            left: 0;
            background: white;
            z-index: 20;
            border-right: 2px solid #dee2e6;
            box-shadow: 2px 0 8px rgba(0,0,0,0.15);
        }

        .fixed-right {
            position: sticky;
            right: 0;
            background: white;
            z-index: 20;
            border-left: 2px solid #dee2e6;
            box-shadow: -2px 0 8px rgba(0,0,0,0.15);
        }

        /* 鼠标悬停时的固定列效果 */
        tr:hover .fixed-left,
        tr:hover .fixed-right {
            background: #f8f9fa;
        }

        /* 为特定列设置最大宽度 */
        th:nth-child(3), td:nth-child(3) { /* 项目名称 - 固定左侧 */
            max-width: 150px;
            min-width: 150px;
            width: 150px;
        }

        th:nth-child(19), td:nth-child(19) { /* 操作列 - 固定右侧 */
            min-width: 120px;
            width: 120px;
        }

        th:nth-child(17), td:nth-child(17), /* 合作伙伴 */
        th:nth-child(18), td:nth-child(18) { /* 竞争对手 */
            max-width: 120px;
        }

        /* 确保固定列的表头也有正确的背景色 */
        thead th.fixed-left,
        thead th.fixed-right {
            background: #f8f9fa;
        }

        /* 为固定列添加更明显的视觉分隔 */
        .fixed-left::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom, #dee2e6, #adb5bd);
        }

        .fixed-right::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom, #dee2e6, #adb5bd);
        }

        /* 确保固定列在滚动时保持可见 */
        .table-container::-webkit-scrollbar {
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .number {
            text-align: right;
            font-family: 'Courier New', monospace;
        }
        
        .stage-tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .stage-客户分析 { background: #fff3e0; color: #f8cbad; }
        .stage-建立信任 { background: #fff3e0; color: #ffe699; }
        .stage-挖掘需求 { background: #f3e5f5; color: #ffc000; }
        .stage-呈现价值 { background: #e3f2fd; color: #1976d2; }
        .stage-回收货款 { background: #e8f5e8; color: #388e3c; }
        
        .product-tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
            background: #f0f0f0;
            color: #666;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-edit {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .btn-edit:hover {
            background: #218838;
        }

        .btn-delete:hover {
            background: #c82333;
        }
        
        /* 客户详情页面特定样式 */
        .modal .modal-content {
            font-size: 0.85rem; /* 调小客户详情页面弹窗字体 */
            max-height: 85vh; /* 增加最大高度 */
        }

        .modal .form-group label {
            font-size: 0.75rem; /* 调小标签字体 */
        }

        .modal .form-group input,
        .modal .form-group select,
        .modal .form-group textarea {
            font-size: 0.8rem; /* 调小输入框字体 */
            padding: 0.6rem; /* 调小内边距 */
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.5rem;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }
        
        /* 列配置组件样式 */
        .column-config {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .column-config-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            user-select: none;
        }

        .column-config-header:hover {
            background: #e9ecef;
        }

        .column-config-title {
            font-weight: 600;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .column-config-toggle {
            font-size: 1.2rem;
            transition: transform 0.3s ease;
        }

        .column-config-toggle.expanded {
            transform: rotate(180deg);
        }

        .column-config-content {
            display: none;
            padding: 1rem;
            background: white;
        }

        .column-config-content.show {
            display: block;
        }

        .column-config-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .column-config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 0.5rem;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
            background: #f8f9fa;
        }

        .column-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            background: white;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            cursor: move;
            user-select: none;
            transition: all 0.2s ease;
        }

        .column-item:hover {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .column-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .column-item.fixed {
            background: #e3f2fd;
            border-color: #2196f3;
            cursor: not-allowed;
        }

        .column-item.fixed .drag-handle {
            opacity: 0.3;
            cursor: not-allowed;
        }

        .column-checkbox {
            margin: 0;
        }

        .column-label {
            flex: 1;
            font-size: 0.9rem;
            color: #495057;
        }

        .column-item.fixed .column-label {
            color: #1976d2;
            font-weight: 500;
        }

        .drag-handle {
            color: #6c757d;
            cursor: move;
            font-size: 1rem;
        }

        .column-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
            justify-content: flex-end;
        }

        .btn-column-action {
            padding: 0.5rem 1rem;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background: white;
            color: #495057;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .btn-column-action:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .btn-column-action.primary {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .btn-column-action.primary:hover {
            background: #0056b3;
            border-color: #0056b3;
        }

        .btn-column-action.success {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .btn-column-action.success:hover {
            background: #1e7e34;
            border-color: #1e7e34;
        }

        @media (max-width: 768px) {
            .breadcrumb {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .customer-selector {
                justify-content: space-between;
            }

            .customer-selector select {
                min-width: 120px;
                flex: 1;
                margin-left: 0.5rem;
            }

            .customer-info {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .customer-meta {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            /* 移动端固定列调整 */
            table {
                font-size: 0.75rem;
                min-width: 1600px;
            }

            /* 移动端弹窗优化 */
            .modal .modal-content {
                width: 95% !important;
                max-width: none !important;
                padding: 1rem !important;
                font-size: 0.75rem !important;
                max-height: 90vh !important;
            }

            .modal .form-group label {
                font-size: 0.7rem !important;
            }

            .modal .form-group input,
            .modal .form-group select,
            .modal .form-group textarea {
                font-size: 0.75rem !important;
                padding: 0.5rem !important;
            }

            th, td {
                padding: 0.3rem;
            }

            .fixed-left {
                min-width: 120px;
                width: 120px;
            }

            .fixed-right {
                min-width: 100px;
                width: 100px;
            }

            .column-config-grid {
                grid-template-columns: 1fr;
            }

            .column-config-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="breadcrumb">
            <a href="/sales-dashboard.html">← 返回销售仪表板</a>
            <div class="customer-selector">
                <label for="customerSelect">切换客户:</label>
                <select id="customerSelect" onchange="switchCustomer()">
                    <option value="">加载中...</option>
                </select>
            </div>
        </div>
        
        <header>
            <div class="customer-info">
                <div class="customer-basic">
                    <h1 id="customerName">加载中...</h1>
                    <div class="customer-meta">
                        <span id="customerRegion">-</span>
                        <span id="totalProjects">-</span>
                    </div>
                </div>
            </div>
        </header>
        
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-value" id="totalExpectedRevenue">-</div>
                <div class="stat-label">预计总销售额(万元)</div>
            </div>
            <!-- <div class="stat-card">
                <div class="stat-value" id="totalActualPayment">-</div>
                <div class="stat-label">实际总回款(万元)</div>
            </div> -->
            <div class="stat-card">
                <div class="stat-value" id="totalActualSales">-</div>
                <div class="stat-label">实际总销售额(万元)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="salesRate">-</div>
                <div class="stat-label">完成率(%)</div>
            </div>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>项目阶段:</label>
                <select id="stageFilter">
                    <option value="">全部阶段</option>
                    <option value="客户分析">客户分析</option>
                    <option value="建立信任">建立信任</option>
                    <option value="挖掘需求">挖掘需求</option>
                    <option value="呈现价值">呈现价值</option>
                    <option value="回收货款">回收货款</option>
                </select>
            </div>
            <div class="control-group">
                <label>产品类型:</label>
                <select id="productTypeFilter">
                    <option value="">全部类型</option>
                </select>
            </div>
            <div class="control-group">
                <label>排序:</label>
                <select id="sortBy">
                    <option value="id">项目ID</option>
                    <option value="projectName">项目名称</option>
                    <option value="expectedRevenue">预计收入</option>
                    <option value="actualPayment">实际回款</option>
                    <option value="percentage">完成度</option>
                </select>
                <select id="sortOrder">
                    <option value="asc">升序</option>
                    <option value="desc">降序</option>
                </select>
            </div>
            <div class="control-group">
                <button onclick="loadProjects()">刷新数据</button>
                <button onclick="showAddModal()">添加项目</button>
                <button onclick="toggleSelectAll()" id="selectAllBtn" style="background: #6c757d;">全选/取消</button>
                <button onclick="deleteSelectedProjects()" id="deleteSelectedBtn" style="background: #dc3545; display: none;">删除选中</button>
            </div>
        </div>

        <!-- 列配置组件 -->
        <div class="column-config">
            <div class="column-config-header" onclick="toggleColumnConfig()">
                <div class="column-config-title">
                    <span>🔧</span>
                    <span>表格列配置</span>
                    <small style="color: #6c757d; margin-left: 0.5rem;">(点击展开/收起)</small>
                </div>
                <div class="column-config-toggle" id="columnConfigToggle">▼</div>
            </div>
            <div class="column-config-content" id="columnConfigContent">
                <div class="column-config-actions">
                    <button class="btn-column-action" onclick="selectAllColumns()">全选</button>
                    <button class="btn-column-action" onclick="deselectAllColumns()">全不选</button>
                    <button class="btn-column-action" onclick="resetColumnConfig()">重置默认</button>
                    <button class="btn-column-action primary" onclick="saveColumnConfig()">保存配置</button>
                </div>
                <div class="column-config-grid" id="columnConfigGrid">
                    <!-- 列配置项将通过JavaScript动态生成 -->
                </div>
                <div class="column-actions">
                    <small style="color: #6c757d;">
                        💡 提示：拖拽列项可调整显示顺序，取消勾选可隐藏列。固定列（📌标记）无法移动或隐藏。
                    </small>
                </div>
            </div>
        </div>
        
        <div class="table-container">
            <div style="padding: 0.5rem; background: #e3f2fd; border-bottom: 1px solid #ddd; font-size: 0.85rem; color: #1976d2;">
                💡 提示：项目名称和操作列已固定，横向滚动时保持可见
            </div>
            <div id="loading" class="loading">正在加载项目数据...</div>
            <table id="projectsTable" style="display: none;">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()"></th>
                        <th>项目ID</th>
                        <th class="fixed-left" title="此列固定显示">项目名称 📌</th>
                        <th>产品类型</th>
                        <th>预计收入(万元)</th>
                        <th>项目阶段</th>
                        <th>完成度</th>
                        <th>预计销售额(万元)</th>
                        <th>实际回款(万元)</th>
                        <th>实际销售额(万元)</th>
                        <th>预计签约月份</th>
                        <th>实际谈判时间</th>
                        <th>实际签约时间</th>
                        <th>开票日期</th>
                        <th>验收时间</th>
                        <th>回款时间</th>
                        <th>回款金额(万元)</th>
                        <th>合作伙伴</th>
                        <th>竞争对手</th>
                        <th class="fixed-right" title="此列固定显示">操作 📌</th>
                    </tr>
                </thead>
                <tbody id="projectsTableBody">
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 编辑项目模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">编辑项目</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="projectForm">
                <div class="form-row">
                    <div class="form-group">
                        <label>项目名称:</label>
                        <input type="text" id="editProjectName" required>
                    </div>
                    <div class="form-group">
                        <label>产品类型:</label>
                        <input type="text" id="editProductType">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>预计收入(万元):</label>
                        <input type="number" id="editExpectedRevenue" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>项目阶段:</label>
                        <select id="editProjectStage">
                            <option value="客户分析">客户分析</option>
                            <option value="建立信任">建立信任</option>
                            <option value="挖掘需求">挖掘需求</option>
                            <option value="呈现价值">呈现价值</option>
                            <option value="回收货款">回收货款</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>完成度(0-1):</label>
                        <input type="number" id="editPercentage" step="0.01" min="0" max="1">
                    </div>
                    <div class="form-group">
                        <label>预计销售额(万元) <small style="color: #666;">(自动计算)</small>:</label>
                        <input type="number" id="editExpectedSales" step="0.01" readonly style="background-color: #f8f9fa;">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>实际回款(万元):</label>
                        <input type="number" id="editActualPayment" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>实际销售额(万元):</label>
                        <input type="number" id="editActualSales" step="0.01">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>预计签约月份:</label>
                        <input type="text" id="editExpectedSignMonth" placeholder="如：2025年6月">
                    </div>
                    <div class="form-group">
                        <label>季度:</label>
                        <select id="editQuarter">
                            <option value="">请选择</option>
                            <option value="Q1">Q1</option>
                            <option value="Q2">Q2</option>
                            <option value="Q3">Q3</option>
                            <option value="Q4">Q4</option>
                        </select>
                    </div>
                </div>

                <!-- 时间相关字段 -->
                <div class="form-row">
                    <div class="form-group">
                        <label>实际商务谈判/中标时间:</label>
                        <input type="date" id="editActualNegotiationTime">
                    </div>
                    <div class="form-group">
                        <label>实际签约时间:</label>
                        <input type="date" id="editActualSignTime">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>开票日期:</label>
                        <input type="date" id="editInvoiceDate">
                    </div>
                    <div class="form-group">
                        <label>验收时间:</label>
                        <input type="date" id="editAcceptanceTime">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>回款时间:</label>
                        <input type="date" id="editPaymentTime">
                    </div>
                    <div class="form-group">
                        <!-- 空白占位，保持布局对齐 -->
                    </div>
                </div>
                <!-- 回款相关字段 -->
                <div class="form-row">
                    <div class="form-group">
                        <label>回款金额(万元):</label>
                        <input type="number" id="editPaymentAmount" step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label>实时收款数据:</label>
                        <input type="text" id="editRealtimePaymentData" placeholder="如：银行转账确认">
                    </div>
                </div>
                <div class="form-group">
                    <label>回款备注:</label>
                    <textarea id="editPaymentRemarks" rows="3" placeholder="回款相关备注信息"></textarea>
                </div>

                <!-- 合作与竞争字段 -->
                <div class="form-row">
                    <div class="form-group">
                        <label>合作伙伴:</label>
                        <input type="text" id="editPartners" placeholder="多个合作伙伴用逗号分隔">
                    </div>
                    <div class="form-group">
                        <label>竞争对手:</label>
                        <input type="text" id="editCompetitors" placeholder="多个竞争对手用逗号分隔">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>项目发起部门:</label>
                        <input type="text" id="editProjectDepartment" placeholder="">
                    </div>
                    <div class="form-group">
                        <label>部门负责人:</label>
                        <input type="text" id="editDepartmentHead" placeholder="">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>部门领导:</label>
                        <input type="text" id="editDepartmentLeader" placeholder="">
                    </div>
                    <div class="form-group">
                        <label>公司分管领导:</label>
                        <input type="text" id="editCompanyLeader" placeholder="">
                    </div>
                </div>
                </form>
            </div>
            <div class="form-actions">
                <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
                <button type="submit" class="btn-primary" form="projectForm">保存</button>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="navbar.js"></script>
    <script src="customer-detail.js"></script>
</body>
</html>
