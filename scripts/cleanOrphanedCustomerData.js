const mongoManager = require('../utils/mongoManager');

async function cleanOrphanedCustomerData() {
    try {
        console.log('🧹 清理生产环境中的孤立客户数据...');
        
        // 设置生产环境
        process.env.NODE_ENV = 'production';
        
        // 连接生产环境MongoDB
        await mongoManager.connect();
        
        // 加载销售数据和客户数据
        const salesData = await mongoManager.loadSalesData();
        const customerData = await mongoManager.loadCustomerData();
        
        console.log(`📊 销售记录: ${salesData.length} 条`);
        console.log(`👥 客户详细数据: ${Object.keys(customerData).length} 个`);
        
        // 获取所有有效的客户名称（来自销售记录）
        const validCustomerNames = new Set(salesData.map(record => record.customerName));
        console.log(`✅ 有效客户名称: ${validCustomerNames.size} 个`);
        
        // 查找孤立的客户数据（存在客户详细数据但没有对应的销售记录）
        const orphanedCustomers = [];
        for (const customerName of Object.keys(customerData)) {
            if (!validCustomerNames.has(customerName)) {
                orphanedCustomers.push(customerName);
            }
        }
        
        if (orphanedCustomers.length > 0) {
            console.log(`❌ 发现 ${orphanedCustomers.length} 个孤立的客户数据:`);
            orphanedCustomers.forEach(customerName => {
                const customer = customerData[customerName];
                console.log(`   "${customerName}" - 项目数: ${customer.projects ? customer.projects.length : 0}`);
            });
            
            // 询问是否删除（在脚本中自动确认）
            console.log('\n🗑️ 开始清理孤立的客户数据...');
            
            // 删除孤立的客户数据
            orphanedCustomers.forEach(customerName => {
                delete customerData[customerName];
                console.log(`   ✅ 已删除: "${customerName}"`);
            });
            
            // 保存更新后的客户数据到MongoDB
            await mongoManager.saveCustomerData(customerData);
            console.log('✅ 客户数据已保存到生产环境MongoDB');
            
            console.log(`🎉 清理完成！删除了 ${orphanedCustomers.length} 个孤立的客户数据`);
            console.log(`📊 剩余客户数据: ${Object.keys(customerData).length} 个`);
            
        } else {
            console.log('✅ 没有发现孤立的客户数据，数据一致性良好');
        }
        
        // 特别检查证券通
        if (customerData['证券通']) {
            console.log('❌ 证券通的客户数据仍然存在，这不应该发生');
        } else {
            console.log('✅ 证券通的客户数据已被清理');
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 清理失败:', error);
        console.error('错误详情:', error.stack);
        process.exit(1);
    }
}

cleanOrphanedCustomerData();
