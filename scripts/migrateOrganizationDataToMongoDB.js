// 组织架构数据迁移脚本 - 从JSON文件迁移到MongoDB
const fs = require('fs');
const path = require('path');
const mongoManager = require('../utils/mongoManager');
const dataManager = require('../utils/dataManager');

async function migrateOrganizationData() {
    console.log('🔄 开始迁移组织架构数据到MongoDB...');
    console.log('=' .repeat(60));

    try {
        // 连接到MongoDB
        await mongoManager.connect();
        console.log('✅ MongoDB连接成功');

        // 读取JSON文件中的组织架构数据
        const jsonFilePath = path.join(__dirname, '..', 'data', 'organization_data.json');
        
        let organizationData = {};
        
        if (fs.existsSync(jsonFilePath)) {
            console.log('📄 从JSON文件读取组织架构数据...');
            const jsonData = fs.readFileSync(jsonFilePath, 'utf8');
            organizationData = JSON.parse(jsonData);
            console.log(`📄 从JSON文件读取到 ${Object.keys(organizationData).length} 个客户的组织架构数据`);
        } else {
            console.log('⚠️  JSON文件不存在，创建默认组织架构数据');
            
            // 创建一些示例组织架构数据
            organizationData = {
                "华安证券": {
                    customerName: "华安证券",
                    customerId: 1,
                    departments: [
                        {
                            id: "dept_001",
                            name: "总经理办公室",
                            type: "管理部门",
                            manager: "张总",
                            description: "公司最高管理层",
                            employees: [
                                {
                                    id: "emp_001",
                                    name: "张总",
                                    gender: "男",
                                    position: "总经理",
                                    description: "公司总经理，负责全面管理"
                                }
                            ],
                            children: [
                                {
                                    id: "dept_002",
                                    name: "技术部",
                                    type: "技术部门",
                                    manager: "李经理",
                                    description: "负责技术开发和维护",
                                    employees: [
                                        {
                                            id: "emp_002",
                                            name: "李经理",
                                            gender: "男",
                                            position: "技术总监",
                                            description: "负责技术团队管理"
                                        },
                                        {
                                            id: "emp_003",
                                            name: "王工程师",
                                            gender: "女",
                                            position: "高级工程师",
                                            description: "负责核心系统开发"
                                        }
                                    ],
                                    children: []
                                },
                                {
                                    id: "dept_003",
                                    name: "业务部",
                                    type: "业务部门",
                                    manager: "赵经理",
                                    description: "负责业务拓展和客户服务",
                                    employees: [
                                        {
                                            id: "emp_004",
                                            name: "赵经理",
                                            gender: "女",
                                            position: "业务总监",
                                            description: "负责业务团队管理"
                                        }
                                    ],
                                    children: []
                                }
                            ]
                        }
                    ],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                "国元证券": {
                    customerName: "国元证券",
                    customerId: 2,
                    departments: [
                        {
                            id: "dept_004",
                            name: "董事会",
                            type: "管理部门",
                            manager: "董事长",
                            description: "公司最高决策机构",
                            employees: [
                                {
                                    id: "emp_005",
                                    name: "董事长",
                                    gender: "男",
                                    position: "董事长",
                                    description: "公司董事长"
                                }
                            ],
                            children: []
                        }
                    ],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            };
        }

        // 检查MongoDB中是否已有数据
        const existingData = await mongoManager.loadOrganizationData();
        
        if (Object.keys(existingData).length > 0) {
            console.log(`⚠️  MongoDB中已存在 ${Object.keys(existingData).length} 个客户的组织架构数据`);
            console.log('');
            console.log('现有MongoDB数据:');
            Object.keys(existingData).forEach((customerName, index) => {
                const org = existingData[customerName];
                console.log(`   ${index + 1}. ${customerName} - ${org.departments?.length || 0} 个部门`);
            });
            
            console.log('');
            console.log('JSON文件数据:');
            Object.keys(organizationData).forEach((customerName, index) => {
                const org = organizationData[customerName];
                console.log(`   ${index + 1}. ${customerName} - ${org.departments?.length || 0} 个部门`);
            });
            
            console.log('');
            console.log('🔄 将用JSON文件数据覆盖MongoDB数据...');
        }

        // 保存到MongoDB（会覆盖现有数据）
        const success = await mongoManager.saveOrganizationData(organizationData);
        
        if (success) {
            console.log(`✅ 组织架构数据已成功迁移到MongoDB: ${Object.keys(organizationData).length} 个客户`);
            
            // 创建备份文件
            if (fs.existsSync(jsonFilePath)) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const backupPath = path.join(__dirname, '..', 'data', `organization_data_backup_${timestamp}.json`);
                fs.copyFileSync(jsonFilePath, backupPath);
                console.log(`📦 已创建JSON文件备份: ${path.basename(backupPath)}`);
            } else {
                // 创建JSON文件
                fs.writeFileSync(jsonFilePath, JSON.stringify(organizationData, null, 2));
                console.log('📄 已创建JSON文件');
            }
        } else {
            throw new Error('迁移失败');
        }

        // 验证迁移结果
        console.log('');
        console.log('🔍 验证迁移结果...');
        const verifyData = await mongoManager.loadOrganizationData();
        
        console.log(`✅ MongoDB中现有 ${Object.keys(verifyData).length} 个客户的组织架构数据:`);
        Object.keys(verifyData).forEach((customerName, index) => {
            const org = verifyData[customerName];
            console.log(`   ${index + 1}. ${customerName}`);
            console.log(`      部门数量: ${org.departments?.length || 0}`);
            console.log(`      创建时间: ${org.createdAt || '未知'}`);
            console.log('');
        });

        console.log('🎉 组织架构数据迁移完成！');
        console.log('');
        console.log('📝 后续步骤:');
        console.log('   1. 重启应用服务器');
        console.log('   2. 测试组织架构页面');
        console.log('   3. 确认所有客户的组织架构可以正常访问');
        console.log('   4. 验证权限控制是否正常工作');

    } catch (error) {
        console.error('❌ 迁移失败:', error.message);
        console.error('');
        console.error('💡 故障排除建议:');
        console.error('   1. 检查MongoDB服务是否运行');
        console.error('   2. 验证数据库连接配置');
        console.error('   3. 确认有足够的数据库权限');
        console.error('   4. 检查JSON文件格式是否正确');
        
        process.exit(1);
    } finally {
        // 关闭MongoDB连接
        await mongoManager.disconnect();
    }
}

// 运行迁移
if (require.main === module) {
    migrateOrganizationData();
}

module.exports = { migrateOrganizationData };
