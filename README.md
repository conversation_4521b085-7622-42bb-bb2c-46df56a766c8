# 2025年华东区销售漏斗管理系统

一个基于Express.js的销售漏斗管理系统，基于真实Excel数据构建，用于管理华东区销售数据和跟踪销售流程。

## 功能特性

- 📊 基于真实Excel数据的销售数据管理
- 📈 华东区销售漏斗统计分析
- 🔍 按区域、月份、季度过滤和多维度排序
- 💼 客户信息和月度计划管理
- 📅 实时回款和销售额跟踪
- 📝 完整的增删改查功能
- 🎯 交互式销售仪表板
- 🏢 客户二级详细页面
- 📋 项目级别的精细化管理
- 📊 基于项目数据的汇总计算
- 📈 月度和季度数据弹窗展示
- 🗓️ 时间维度数据筛选和分析
- 💾 动态数据存储和持久化
- 🔄 数据备份和重置功能
- 🔧 完整的数据管理界面
- 📱 响应式设计，支持移动端

## 技术栈

- **后端**: Node.js + Express.js
- **数据处理**: xlsx（Excel文件读取）
- **前端**: 原生HTML/CSS/JavaScript
- **开发工具**: Nodemon（热重载）
- **数据存储**: 动态JSON文件存储 + 自动备份机制

## 安装和运行

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

或者启动生产服务器：

```bash
npm start
```

### 3. 访问应用

- **销售仪表板**: http://localhost:3000/sales-dashboard.html （主要功能页面）
- **客户详细页面**: http://localhost:3000/customer-detail.html?customer=华安证券 （客户项目管理）
- **数据管理**: http://localhost:3000/data-management.html （数据备份和重置）
- **API文档**: http://localhost:3000 （包含交互式测试）
- **应用首页**: http://localhost:3000

## API 端点

### 销售数据管理

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/sales` | 获取所有销售数据（支持分页、过滤、排序） |
| GET | `/api/sales/:id` | 获取特定销售记录 |
| POST | `/api/sales` | 创建新销售记录 |
| PUT | `/api/sales/:id` | 更新销售记录 |
| DELETE | `/api/sales/:id` | 删除销售记录 |

### 客户和项目管理

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/customers` | 获取所有客户列表及汇总信息 |
| GET | `/api/customers/:customerName` | 获取特定客户的详细信息和所有项目 |
| GET | `/api/customers/:customerName/projects` | 获取特定客户的项目列表（支持过滤和排序） |
| GET | `/api/customers/:customerName/projects/:projectId` | 获取特定项目详情 |
| POST | `/api/customers/:customerName/projects` | 为特定客户添加新项目 |
| PUT | `/api/customers/:customerName/projects/:projectId` | 更新特定客户的特定项目 |
| DELETE | `/api/customers/:customerName/projects/:projectId` | 删除特定客户的特定项目 |

### 统计和配置

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/stats` | 获取销售统计数据（区域、项目阶段、总体统计） |
| GET | `/api/regions` | 获取所有区域列表 |



### 系统状态

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/health` | 健康检查 |

## 数据模型

### 客户记录 (Customer Record)

```json
{
  "customerName": "华安证券",
  "region": "安徽",
  "sheetName": "1华安",
  "summary": {
    "totalProjects": 10,
    "totalExpectedRevenue": 931.5,
    "totalExpectedSales": 711.5,
    "totalActualPayment": 27.1,
    "totalActualSales": 381.5,
    "paymentRate": 2.91,
    "salesRate": 40.96
  },
  "projects": [...],
  "createdAt": "2025-06-09T05:46:58.509Z",
  "updatedAt": "2025-06-09T05:46:58.509Z"
}
```

### 项目记录 (Project Record)

```json
{
  "id": 1,
  "projectName": "直播风控管理平台一期",
  "productType": "留痕系统",
  "expectedRevenue": 8.5,
  "projectStage": "回收货款",
  "percentage": 1,
  "expectedSales": 8.5,
  "actualPayment": 8.5,
  "actualSales": 8.5,
  "expectedSignMonth": "2025年2月",
  "quarter": "Q1",
  "contractTime": 45700,
  "projectDepartment": "",
  "departmentHead": "",
  "departmentLeader": "",
  "companyLeader": "",
  "relatedDepartment": "",
  "relatedDepartmentHead": "",
  "relatedCompanyLeader": "",
  "createdAt": "2025-06-09T05:46:58.509Z",
  "updatedAt": "2025-06-09T05:46:58.509Z"
}
```

### 支持的区域

- 安徽
- 福建
- 上海
- 山东
- 江苏
- 浙江

### 项目阶段

- 呈现价值
- 回收货款
- 商务谈判
- 技术交流

## 查询参数

### GET /api/sales

- `region`: 按区域过滤（可选，'all' 表示所有区域）
- `month`: 按月份过滤（可选，'all' 表示所有月份，可选值：jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec）
- `quarter`: 按季度过滤（可选，'all' 表示所有季度，可选值：Q1, Q2, Q3, Q4）
- `sortBy`: 排序字段，默认为 'id'（可选：id, customerName, totalPlan, actualPayment, region）
- `order`: 排序方向，'asc' 或 'desc'，默认为 'asc'
- `page`: 页码，默认为 1
- `limit`: 每页记录数，默认为 50

示例：
```
GET /api/sales?region=安徽&month=feb&sortBy=totalPlan&order=desc&page=1&limit=10
GET /api/sales?quarter=Q1&sortBy=actualPayment&order=desc
```

## 项目结构

```
├── app.js                                    # 主应用文件
├── package.json                              # 项目配置
├── README.md                                # 项目说明
├── 2025年华东区销售漏斗表格-崔治灿0608.xlsx    # 原始Excel数据文件
├── routes/                                  # 路由文件
│   ├── index.js                            # 主路由
│   └── api.js                              # API路由
├── public/                                  # 静态文件
│   ├── index.html                          # API文档页面
│   ├── sales-dashboard.html                # 销售仪表板页面
│   ├── sales-dashboard.js                  # 仪表板JavaScript
│   ├── customer-detail.html                # 客户详细页面
│   ├── customer-detail.js                  # 客户详细页面JavaScript
│   └── data-management.html                # 数据管理页面
├── scripts/                                # 数据处理脚本
│   ├── readExcel.js                        # Excel读取脚本
│   ├── readSheet2.js                       # Sheet2读取脚本
│   ├── processExcelData.js                 # 数据处理脚本
│   ├── processAllCustomerSheets.js         # 客户详细数据处理脚本
│   ├── excel_data.json                     # 原始Excel数据
│   ├── sheet2_data.json                    # Sheet2数据
│   ├── processed_sales_data.json           # 处理后的销售数据
│   └── all_customer_details.json           # 所有客户详细数据
├── utils/                                  # 工具类
│   └── dataManager.js                      # 数据管理器
├── data/                                   # 动态数据存储
│   ├── sales_data.json                     # 销售数据文件
│   ├── customer_data.json                  # 客户数据文件
│   └── backups/                            # 数据备份目录
└── views/                                  # 视图模板（预留）
```

## 开发说明

### 启动脚本

- `npm start`: 启动生产服务器
- `npm run dev`: 启动开发服务器（使用nodemon）

### 环境变量

- `PORT`: 服务器端口，默认3000
- `NODE_ENV`: 运行环境，影响错误信息显示

## 数据来源

本系统基于真实的Excel文件 `2025年华东区销售漏斗表格-崔治灿0608.xlsx` 构建，包含：

### 汇总数据（Sheet1）
- 30条华东区销售记录
- 7个区域（安徽、福建、上海、山东、江苏、浙江等）
- 月度销售计划数据
- 实际回款和销售额数据
- 客户占比和省份统计数据

### 客户详细数据（各客户工作表）
- 华安证券：10个具体项目
- 项目阶段：客户分析、建立信任、挖掘需求、呈现价值、回收货款
- 产品类型：留痕系统、财富管理、移动客户端、数据服务
- 项目完成度、预计收入、实际回款等详细信息
- 项目部门、负责人、预计签约时间等管理信息

### 数据层级关系
- **一级页面（销售仪表板）**：显示各客户的汇总数据
- **二级页面（客户详细）**：显示客户的具体项目列表
- **汇总计算**：一级页面的数据由二级页面的项目数据汇总计算得出

### 数据持久化机制
- **动态存储**：所有数据修改操作都会实时保存到JSON文件
- **自动备份**：重要操作前自动创建数据备份
- **数据恢复**：支持重置数据到初始状态
- **文件结构**：
  - `data/sales_data.json` - 销售汇总数据
  - `data/customer_data.json` - 客户详细数据
  - `data/backups/` - 自动备份文件

## 后续扩展

- [ ] 数据库集成（MongoDB/PostgreSQL）
- [ ] 用户认证和授权
- [ ] 图表可视化（Chart.js/ECharts）
- [ ] Excel文件导入/导出功能
- [ ] 邮件提醒和通知功能
- [ ] 移动端App开发
- [ ] 实时数据同步
- [ ] 高级数据分析和预测

## 许可证

ISC
