const express = require('express');
const dataManager = require('../utils/dataManager');
const auth = require('../middleware/auth');
const router = express.Router();

// 加载Q&A数据
let qaData = dataManager.loadQAData();
console.log(`Q&A路由: 已加载 ${qaData.length} 条Q&A数据`);

// 获取Q&A分类列表（必须在 /qa/:id 之前定义）
router.get('/categories/list', auth.authenticateToken, (req, res) => {
    try {
        const categories = [...new Set(qaData.map(qa => qa.category))].sort();
        res.json({
            success: true,
            data: categories
        });
    } catch (error) {
        console.error('获取分类列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取分类列表失败'
        });
    }
});

// 获取Q&A列表
router.get('/', auth.authenticateToken, (req, res) => {
    try {
        const { category, search, sortBy = 'createdAt', order = 'desc' } = req.query;
        
        let filteredQA = [...qaData];
        
        // 按分类筛选
        if (category && category !== 'all') {
            filteredQA = filteredQA.filter(qa => qa.category === category);
        }
        
        // 按关键词搜索
        if (search) {
            const searchLower = search.toLowerCase();
            filteredQA = filteredQA.filter(qa => 
                qa.question.toLowerCase().includes(searchLower) ||
                qa.answer.toLowerCase().includes(searchLower) ||
                qa.tags.some(tag => tag.toLowerCase().includes(searchLower))
            );
        }
        
        // 排序
        filteredQA.sort((a, b) => {
            let aValue = a[sortBy];
            let bValue = b[sortBy];
            
            if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            }
            
            if (order === 'desc') {
                return bValue > aValue ? 1 : -1;
            }
            return aValue > bValue ? 1 : -1;
        });
        
        res.json({
            success: true,
            data: filteredQA,
            total: filteredQA.length
        });
    } catch (error) {
        console.error('获取Q&A列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取Q&A列表失败'
        });
    }
});

// 获取单个Q&A
router.get('/:id', auth.authenticateToken, (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const qa = qaData.find(item => item.id === id);
        
        if (!qa) {
            return res.status(404).json({
                success: false,
                message: 'Q&A不存在'
            });
        }
        
        res.json({
            success: true,
            data: qa
        });
    } catch (error) {
        console.error('获取Q&A详情失败:', error);
        res.status(500).json({
            success: false,
            message: '获取Q&A详情失败'
        });
    }
});

// 创建Q&A
router.post('/', auth.authenticateToken, (req, res) => {
    try {
        const { question, answer, category, tags } = req.body;
        
        if (!question || !answer) {
            return res.status(400).json({
                success: false,
                message: '问题和答案为必填项'
            });
        }
        
        const newId = qaData.length > 0 ? Math.max(...qaData.map(qa => qa.id)) + 1 : 1;
        
        const newQA = {
            id: newId,
            question: question.trim(),
            answer: answer.trim(),
            category: category || '常见问题',
            tags: Array.isArray(tags) ? tags : (tags ? tags.split(',').map(tag => tag.trim()) : []),
            author: req.user.name || req.user.username,
            authorId: req.user.id,
            viewCount: 0,
            helpful: 0,
            notHelpful: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        qaData.push(newQA);
        dataManager.saveQAData(qaData);
        
        res.status(201).json({
            success: true,
            data: newQA,
            message: 'Q&A创建成功'
        });
    } catch (error) {
        console.error('创建Q&A失败:', error);
        res.status(500).json({
            success: false,
            message: '创建Q&A失败'
        });
    }
});

// 更新Q&A
router.put('/:id', auth.authenticateToken, (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const { question, answer, category, tags } = req.body;
        
        const qaIndex = qaData.findIndex(qa => qa.id === id);
        
        if (qaIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Q&A不存在'
            });
        }
        
        const existingQA = qaData[qaIndex];
        
        // 更新Q&A
        const updatedQA = {
            ...existingQA,
            question: question ? question.trim() : existingQA.question,
            answer: answer ? answer.trim() : existingQA.answer,
            category: category || existingQA.category,
            tags: Array.isArray(tags) ? tags : (tags ? tags.split(',').map(tag => tag.trim()) : existingQA.tags),
            updatedAt: new Date().toISOString()
        };
        
        qaData[qaIndex] = updatedQA;
        dataManager.saveQAData(qaData);
        
        res.json({
            success: true,
            data: updatedQA,
            message: 'Q&A更新成功'
        });
    } catch (error) {
        console.error('更新Q&A失败:', error);
        res.status(500).json({
            success: false,
            message: '更新Q&A失败'
        });
    }
});

// 删除Q&A
router.delete('/:id', auth.authenticateToken, (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const qaIndex = qaData.findIndex(qa => qa.id === id);
        
        if (qaIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Q&A不存在'
            });
        }
        
        const deletedQA = qaData.splice(qaIndex, 1)[0];
        dataManager.saveQAData(qaData);
        
        res.json({
            success: true,
            data: deletedQA,
            message: 'Q&A删除成功'
        });
    } catch (error) {
        console.error('删除Q&A失败:', error);
        res.status(500).json({
            success: false,
            message: '删除Q&A失败'
        });
    }
});

// 增加Q&A浏览次数
router.post('/:id/view', auth.authenticateToken, (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const qaIndex = qaData.findIndex(qa => qa.id === id);
        
        if (qaIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Q&A不存在'
            });
        }
        
        qaData[qaIndex].viewCount = (qaData[qaIndex].viewCount || 0) + 1;
        dataManager.saveQAData(qaData);
        
        res.json({
            success: true,
            data: { viewCount: qaData[qaIndex].viewCount }
        });
    } catch (error) {
        console.error('更新浏览次数失败:', error);
        res.status(500).json({
            success: false,
            message: '更新浏览次数失败'
        });
    }
});

// Q&A评价（有用/无用）
router.post('/:id/rate', auth.authenticateToken, (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const { helpful } = req.body; // true为有用，false为无用
        
        const qaIndex = qaData.findIndex(qa => qa.id === id);
        
        if (qaIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Q&A不存在'
            });
        }
        
        if (helpful === true) {
            qaData[qaIndex].helpful = (qaData[qaIndex].helpful || 0) + 1;
        } else if (helpful === false) {
            qaData[qaIndex].notHelpful = (qaData[qaIndex].notHelpful || 0) + 1;
        }
        
        dataManager.saveQAData(qaData);
        
        res.json({
            success: true,
            data: {
                helpful: qaData[qaIndex].helpful,
                notHelpful: qaData[qaIndex].notHelpful
            }
        });
    } catch (error) {
        console.error('Q&A评价失败:', error);
        res.status(500).json({
            success: false,
            message: 'Q&A评价失败'
        });
    }
});

module.exports = router;
