<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组织架构测试</title>
</head>
<body>
    <h1>组织架构功能测试</h1>
    
    <div>
        <h2>认证状态</h2>
        <p id="authStatus">检查中...</p>
        <button onclick="testLogin()">测试登录</button>
    </div>
    
    <div>
        <h2>客户列表</h2>
        <button onclick="testLoadCustomers()">加载客户列表</button>
        <div id="customersList"></div>
    </div>
    
    <div>
        <h2>组织架构测试</h2>
        <select id="testCustomerSelect" onchange="testLoadOrganization()">
            <option value="">请选择客户...</option>
        </select>
        <button onclick="testShowAddModal()" id="testAddBtn" style="display: none;">测试添加按钮</button>
        <div id="organizationResult"></div>
    </div>
    
    <div>
        <h2>控制台日志</h2>
        <div id="console" style="background: #f0f0f0; padding: 10px; height: 200px; overflow-y: scroll;"></div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        const originalError = console.error;
        const consoleDiv = document.getElementById('console');
        
        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? 'red' : 'black';
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleDiv.appendChild(div);
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        // 创建authManager实例
        const authManager = new AuthManager();
        
        // 页面加载时检查认证状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            checkAuthStatus();
        });
        
        function checkAuthStatus() {
            const isAuth = authManager.isAuthenticated();
            document.getElementById('authStatus').textContent = isAuth ? '已认证' : '未认证';
            console.log('认证状态:', isAuth);
            
            if (isAuth) {
                const userInfo = authManager.getUserInfo();
                console.log('用户信息:', userInfo);
            }
        }
        
        async function testLogin() {
            try {
                console.log('开始测试登录...');
                const result = await authManager.login('admin', 'admin123');
                console.log('登录结果:', result);
                checkAuthStatus();
            } catch (error) {
                console.error('登录失败:', error);
            }
        }
        
        async function testLoadCustomers() {
            try {
                console.log('开始加载客户列表...');
                const response = await authManager.authenticatedFetch('/api/customers');
                const result = await response.json();
                console.log('客户列表结果:', result);
                
                if (result.success) {
                    const select = document.getElementById('testCustomerSelect');
                    select.innerHTML = '<option value="">请选择客户...</option>';
                    
                    result.data.forEach(customer => {
                        const option = document.createElement('option');
                        option.value = customer.customerName;
                        option.textContent = customer.customerName;
                        select.appendChild(option);
                    });
                    
                    document.getElementById('customersList').textContent = `加载了 ${result.data.length} 个客户`;
                }
            } catch (error) {
                console.error('加载客户列表失败:', error);
            }
        }
        
        async function testLoadOrganization() {
            const select = document.getElementById('testCustomerSelect');
            const customerName = select.value;
            const addBtn = document.getElementById('testAddBtn');
            
            if (!customerName) {
                addBtn.style.display = 'none';
                document.getElementById('organizationResult').textContent = '';
                return;
            }
            
            try {
                console.log('加载组织架构:', customerName);
                const response = await authManager.authenticatedFetch(`/api/organizations/${encodeURIComponent(customerName)}`);
                const result = await response.json();
                console.log('组织架构结果:', result);
                
                if (response.status === 404) {
                    addBtn.style.display = 'block';
                    document.getElementById('organizationResult').textContent = '该客户暂无组织架构，可以添加';
                } else if (result.success) {
                    addBtn.style.display = 'block';
                    document.getElementById('organizationResult').textContent = `找到组织架构，部门数: ${result.data.departments ? result.data.departments.length : 0}`;
                } else {
                    addBtn.style.display = 'none';
                    document.getElementById('organizationResult').textContent = '加载失败: ' + result.message;
                }
            } catch (error) {
                console.error('加载组织架构失败:', error);
                addBtn.style.display = 'none';
                document.getElementById('organizationResult').textContent = '加载失败: ' + error.message;
            }
        }
        
        function testShowAddModal() {
            console.log('测试显示添加模态框');
            alert('添加按钮点击成功！这证明JavaScript事件处理正常工作。');
        }
    </script>
</body>
</html>
