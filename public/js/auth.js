// 认证工具类
class AuthManager {
    constructor() {
        this.token = localStorage.getItem('authToken');
        this.userInfo = this.getUserInfo();
        this.initialized = false;

        // 确保token和userInfo同步
        if (this.token && !this.userInfo) {
            // 如果有token但没有userInfo，尝试重新获取
            this.userInfo = this.getUserInfo();
        }

        this.init();
    }

    // 初始化认证管理器
    async init() {
        try {
            // 设置请求拦截器
            this.setupRequestInterceptor();

            // 检查当前页面是否需要认证
            if (this.isAuthRequired() && !this.isAuthenticated()) {
                this.redirectToLogin();
                return;
            }

            // 如果已登录，验证token有效性
            if (this.isAuthenticated()) {
                await this.verifyToken();
            }

            // 标记初始化完成
            this.initialized = true;
        } catch (error) {
            console.error('认证管理器初始化失败:', error);
            this.initialized = true; // 即使失败也标记为已初始化，避免无限等待
        }
    }

    // 检查当前页面是否需要认证
    isAuthRequired() {
        const currentPath = window.location.pathname;
        const publicPaths = ['/login.html', '/login'];
        return !publicPaths.some(path => currentPath.includes(path));
    }

    // 检查是否已认证
    isAuthenticated() {
        // 如果没有token，肯定未认证
        if (!this.token) {
            return false;
        }

        // 如果有token但没有userInfo，尝试从localStorage获取
        if (!this.userInfo) {
            this.userInfo = this.getUserInfo();
        }

        return !!this.token && !!this.userInfo;
    }

    // 获取用户信息
    getUserInfo() {
        const userInfoStr = localStorage.getItem('userInfo');
        try {
            return userInfoStr ? JSON.parse(userInfoStr) : null;
        } catch (error) {
            console.error('解析用户信息失败:', error);
            return null;
        }
    }

    // 获取当前用户
    getCurrentUser() {
        return this.userInfo;
    }

    // 检查是否是管理员
    isAdmin() {
        return this.userInfo && this.userInfo.permission === 'admin';
    }

    // 检查是否是商务经理
    isManager() {
        return this.userInfo && this.userInfo.permission === 'manager';
    }

    // 检查用户是否有权限访问特定客户
    canAccessCustomer(customerName) {
        if (this.isAdmin()) {
            return true;
        }

        if (this.isManager() && this.userInfo.customers) {
            return this.userInfo.customers.includes(customerName);
        }

        return false;
    }



    // 带认证的fetch请求
    async authenticatedFetch(url, options = {}) {
        // 确保获取最新的token
        const currentToken = this.token || localStorage.getItem('authToken');

        if (!currentToken) {
            throw new Error('用户未登录');
        }

        // 准备请求头
        const headers = {
            'Authorization': `Bearer ${currentToken}`,
            'Content-Type': 'application/json',
            ...options.headers
        };

        // 合并选项
        const requestOptions = {
            ...options,
            headers
        };

        try {
            const response = await fetch(url, requestOptions);

            // 检查响应状态
            if (response.status === 401) {
                // Token过期或无效，清除认证信息并跳转登录
                this.logout();
                throw new Error('登录已过期，请重新登录');
            }

            if (response.status === 403) {
                throw new Error('您没有权限执行此操作');
            }

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `请求失败: ${response.status}`);
            }

            return response;
        } catch (error) {
            // 如果是网络错误或其他错误，重新抛出
            throw error;
        }
    }

    // 获取用户可访问的客户列表
    getAccessibleCustomers() {
        if (this.isAdmin()) {
            return null; // 管理员可以访问所有客户
        }

        return this.userInfo ? this.userInfo.customers || [] : [];
    }

    // 验证token有效性
    async verifyToken() {
        try {
            const response = await fetch('/api/auth/verify', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (!response.ok) {
                throw new Error('Token验证失败');
            }

            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || 'Token无效');
            }

            // 更新用户信息
            this.userInfo = result.user;
            localStorage.setItem('userInfo', JSON.stringify(this.userInfo));

        } catch (error) {
            console.error('Token验证失败:', error);
            this.logout();
        }
    }

    // 设置请求拦截器
    setupRequestInterceptor() {
        // 保存原始的fetch函数
        const originalFetch = window.fetch;

        // 重写fetch函数
        window.fetch = async (url, options = {}) => {
            // 如果是API请求，自动添加Authorization头
            if (url.startsWith('/api')) {
                // 确保获取最新的token（可能从localStorage中获取）
                const currentToken = this.token || localStorage.getItem('authToken');

                if (currentToken) {
                    options.headers = {
                        ...options.headers,
                        'Authorization': `Bearer ${currentToken}`
                    };
                }
            }

            try {
                const response = await originalFetch(url, options);

                // 如果返回401，说明token过期或无效
                if (response.status === 401) {
                    this.logout();
                    return response;
                }

                return response;
            } catch (error) {
                console.error('请求失败:', error);
                throw error;
            }
        };
    }

    // 登录
    async login(username, password) {
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const result = await response.json();

            if (result.success) {
                this.token = result.token;
                this.userInfo = result.user;

                localStorage.setItem('authToken', this.token);
                localStorage.setItem('userInfo', JSON.stringify(this.userInfo));

                return { success: true, user: this.userInfo };
            } else {
                return { success: false, message: result.message };
            }
        } catch (error) {
            console.error('登录失败:', error);
            return { success: false, message: '网络错误，请稍后重试' };
        }
    }

    // 登出
    async logout() {
        // 显示确认对话框
        const confirmed = await this.showLogoutConfirmation();
        if (!confirmed) {
            return;
        }

        try {
            // 调用后端登出API
            if (this.token) {
                await fetch('/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                });
            }
        } catch (error) {
            console.error('登出API调用失败:', error);
        } finally {
            // 无论API调用是否成功，都清除本地数据
            this.token = null;
            this.userInfo = null;

            localStorage.removeItem('authToken');
            localStorage.removeItem('userInfo');

            // 显示登出成功消息
            this.showLogoutMessage();

            // 延迟跳转，让用户看到消息
            setTimeout(() => {
                this.redirectToLogin();
            }, 1500);
        }
    }

    // 显示退出登录确认对话框
    showLogoutConfirmation() {
        return new Promise((resolve) => {
            // 创建确认对话框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(5px);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease-out;
            `;

            const dialog = document.createElement('div');
            dialog.style.cssText = `
                background: white;
                border-radius: 15px;
                padding: 2rem;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                text-align: center;
                animation: slideIn 0.3s ease-out;
            `;

            dialog.innerHTML = `
                <div style="font-size: 3rem; margin-bottom: 1rem;">🚪</div>
                <h3 style="margin: 0 0 1rem 0; color: #333; font-size: 1.3rem;">确认退出登录</h3>
                <p style="margin: 0 0 2rem 0; color: #666; line-height: 1.5;">
                    您确定要退出登录吗？<br>
                    退出后需要重新输入账号密码才能访问系统。
                </p>
                <div style="display: flex; gap: 1rem; justify-content: center;">
                    <button id="cancelLogout" style="
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 0.75rem 1.5rem;
                        border-radius: 8px;
                        font-size: 1rem;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    ">取消</button>
                    <button id="confirmLogout" style="
                        background: linear-gradient(135deg, #dc3545, #c82333);
                        color: white;
                        border: none;
                        padding: 0.75rem 1.5rem;
                        border-radius: 8px;
                        font-size: 1rem;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    ">确认退出</button>
                </div>
            `;

            // 添加CSS动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes slideIn {
                    from { opacity: 0; transform: translateY(-30px) scale(0.9); }
                    to { opacity: 1; transform: translateY(0) scale(1); }
                }
            `;
            document.head.appendChild(style);

            modal.appendChild(dialog);
            document.body.appendChild(modal);

            // 绑定事件
            const cancelBtn = dialog.querySelector('#cancelLogout');
            const confirmBtn = dialog.querySelector('#confirmLogout');

            const cleanup = () => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
                if (style.parentNode) {
                    style.parentNode.removeChild(style);
                }
            };

            cancelBtn.addEventListener('click', () => {
                cleanup();
                resolve(false);
            });

            confirmBtn.addEventListener('click', () => {
                cleanup();
                resolve(true);
            });

            // ESC键取消
            const handleKeydown = (event) => {
                if (event.key === 'Escape') {
                    cleanup();
                    resolve(false);
                    document.removeEventListener('keydown', handleKeydown);
                }
            };
            document.addEventListener('keydown', handleKeydown);

            // 点击背景取消
            modal.addEventListener('click', (event) => {
                if (event.target === modal) {
                    cleanup();
                    resolve(false);
                }
            });

            // 自动聚焦到取消按钮
            setTimeout(() => cancelBtn.focus(), 100);
        });
    }

    // 显示登出成功消息
    showLogoutMessage() {
        // 创建登出成功提示
        const message = document.createElement('div');
        message.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #28a745;
            color: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            font-size: 1.1rem;
            font-weight: 600;
            animation: fadeInOut 1.5s ease-in-out;
        `;
        message.textContent = '已安全退出登录';

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInOut {
                0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(message);

        // 1.5秒后移除消息
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
            if (style.parentNode) {
                style.parentNode.removeChild(style);
            }
        }, 1500);
    }

    // 跳转到登录页
    redirectToLogin() {
        if (!window.location.pathname.includes('login')) {
            window.location.href = '/login.html';
        }
    }

    // 显示用户信息
    displayUserInfo() {
        if (!this.userInfo) return;

        // 在页面上显示用户信息
        const userInfoElements = document.querySelectorAll('.user-info');
        userInfoElements.forEach(element => {
            element.innerHTML = `
                <div class="user-profile">
                    <div class="user-details">
                        <span class="user-name">${this.userInfo.name}</span>
                        <span class="user-role">${this.isAdmin() ? '系统管理员' : '商务经理'}</span>
                        ${this.isManager() ? `<span class="user-customers">${this.userInfo.customers?.length || 0}个客户</span>` : ''}
                    </div>
                    <button onclick="authManager.logout()" class="logout-btn" title="安全退出登录">
                        <span class="logout-icon">🚪</span>
                        <span class="logout-text">退出</span>
                    </button>
                </div>
            `;
        });

        // 添加用户信息样式
        this.addUserInfoStyles();

        // 根据权限显示/隐藏功能
        this.updateUIByPermission();
    }

    // 添加用户信息样式
    addUserInfoStyles() {
        // 检查是否已经添加过样式
        if (document.getElementById('user-info-styles')) return;

        const style = document.createElement('style');
        style.id = 'user-info-styles';
        style.textContent = `
            .user-profile {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 0.5rem;
            }

            .user-details {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 0.25rem;
            }

            .user-name {
                font-weight: 600;
                color: #333;
                font-size: 1rem;
            }

            .user-role {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                padding: 0.25rem 0.75rem;
                border-radius: 15px;
                font-size: 0.8rem;
                font-weight: 500;
            }

            .user-customers {
                color: #666;
                font-size: 0.75rem;
                font-style: italic;
            }

            .logout-btn {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 8px;
                cursor: pointer;
                font-size: 0.9rem;
                font-weight: 500;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
            }

            .logout-btn:hover {
                background: linear-gradient(135deg, #c82333, #a71e2a);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
            }

            .logout-btn:active {
                transform: translateY(0);
                box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
            }

            .logout-icon {
                font-size: 1rem;
            }

            .logout-text {
                font-size: 0.9rem;
            }

            @media (max-width: 768px) {
                .user-profile {
                    flex-direction: column;
                    gap: 0.75rem;
                }

                .user-details {
                    align-items: center;
                }

                .logout-btn {
                    padding: 0.75rem 1.5rem;
                    font-size: 1rem;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 根据权限更新UI
    updateUIByPermission() {
        // 隐藏管理员专用功能
        const adminOnlyElements = document.querySelectorAll('.admin-only');
        adminOnlyElements.forEach(element => {
            element.style.display = this.isAdmin() ? 'block' : 'none';
        });

        // 隐藏商务经理专用功能
        const managerOnlyElements = document.querySelectorAll('.manager-only');
        managerOnlyElements.forEach(element => {
            element.style.display = this.isManager() ? 'block' : 'none';
        });
    }

    // 检查权限并显示错误信息
    checkPermissionAndShowError(requiredPermission, action) {
        if (requiredPermission === 'admin' && !this.isAdmin()) {
            alert('此操作需要管理员权限');
            return false;
        }

        if (requiredPermission === 'manager' && !this.isManager() && !this.isAdmin()) {
            alert('此操作需要商务经理权限');
            return false;
        }

        return true;
    }

    // 过滤客户数据（根据权限）
    filterCustomerData(customers) {
        if (this.isAdmin()) {
            return customers;
        }

        const accessibleCustomers = this.getAccessibleCustomers();
        if (!accessibleCustomers) {
            return customers;
        }

        return customers.filter(customer => 
            accessibleCustomers.includes(customer.customerName)
        );
    }
}

// 创建全局认证管理器实例
const authManager = new AuthManager();

// 页面加载完成后显示用户信息
document.addEventListener('DOMContentLoaded', function() {
    if (authManager.isAuthenticated()) {
        authManager.displayUserInfo();
    }
});

// 导出认证管理器（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
