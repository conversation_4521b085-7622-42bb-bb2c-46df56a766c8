const mongoManager = require('../utils/mongoManager');

async function checkInvoiceData() {
    try {
        console.log('🔍 检查生产环境中存在开票时间的数据...');
        
        // 设置生产环境
        process.env.NODE_ENV = 'production';
        
        // 连接生产环境MongoDB
        await mongoManager.connect();
        
        // 加载数据
        const customerData = await mongoManager.loadCustomerData();
        
        console.log(`👥 客户数据: ${Object.keys(customerData).length} 个`);
        
        // 统计开票时间数据
        let totalProjects = 0;
        let projectsWithInvoiceTime = 0;
        let invoicedProjects = [];
        
        Object.keys(customerData).forEach(customerName => {
            const customer = customerData[customerName];
            if (customer.projects && customer.projects.length > 0) {
                customer.projects.forEach(project => {
                    totalProjects++;
                    
                    if (project.invoiceTime) {
                        projectsWithInvoiceTime++;
                        
                        const paymentDate = project.paymentTime ? new Date(project.paymentTime) : null;
                        const invoiceDate = new Date(project.invoiceTime);
                        
                        // 检查是否符合已开票待回款条件
                        const isInvoicedPending = !paymentDate && !project.paymentAmount && project.expectedRevenue;
                        
                        invoicedProjects.push({
                            customer: customerName,
                            project: project.projectName || '未命名',
                            invoiceTime: project.invoiceTime,
                            paymentTime: project.paymentTime || '未设置',
                            paymentAmount: project.paymentAmount || '未设置',
                            expectedRevenue: project.expectedRevenue || '未设置',
                            projectStage: project.projectStage || '未设置',
                            isInvoicedPending: isInvoicedPending
                        });
                    }
                });
            }
        });
        
        console.log(`\n📊 统计结果:`);
        console.log(`   总项目数: ${totalProjects}`);
        console.log(`   有开票时间的项目: ${projectsWithInvoiceTime}`);
        console.log(`   开票时间覆盖率: ${((projectsWithInvoiceTime / totalProjects) * 100).toFixed(1)}%`);
        
        if (invoicedProjects.length > 0) {
            console.log(`\n📋 所有有开票时间的项目:`);
            
            invoicedProjects.forEach((proj, index) => {
                console.log(`\n   ${index + 1}. ${proj.customer} - ${proj.project}`);
                console.log(`      开票时间: ${proj.invoiceTime}`);
                console.log(`      回款时间: ${proj.paymentTime}`);
                console.log(`      回款金额: ${proj.paymentAmount} 万元`);
                console.log(`      预期收入: ${proj.expectedRevenue} 万元`);
                console.log(`      项目阶段: ${proj.projectStage}`);
                console.log(`      符合已开票待回款: ${proj.isInvoicedPending ? '✅ 是' : '❌ 否'}`);
                
                if (!proj.isInvoicedPending) {
                    const reasons = [];
                    if (proj.paymentTime !== '未设置') reasons.push('已有回款时间');
                    if (proj.paymentAmount !== '未设置') reasons.push('已有回款金额');
                    if (proj.expectedRevenue === '未设置') reasons.push('无预期收入');
                    console.log(`      不符合原因: ${reasons.join(', ')}`);
                }
            });
            
            // 统计符合已开票待回款条件的项目
            const pendingProjects = invoicedProjects.filter(proj => proj.isInvoicedPending);
            const totalPendingAmount = pendingProjects.reduce((sum, proj) => {
                return sum + (parseFloat(proj.expectedRevenue) || 0);
            }, 0);
            
            console.log(`\n💰 已开票待回款统计:`);
            console.log(`   符合条件的项目: ${pendingProjects.length} 个`);
            console.log(`   总金额: ${totalPendingAmount.toFixed(2)} 万元`);
            
            if (pendingProjects.length > 0) {
                console.log(`\n   详细列表:`);
                pendingProjects.forEach((proj, index) => {
                    console.log(`     ${index + 1}. ${proj.customer} - ${proj.project} - ${proj.expectedRevenue}万元`);
                });
            }
        } else {
            console.log(`\n❌ 没有找到任何有开票时间的项目`);
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 检查失败:', error);
        console.error('错误详情:', error.stack);
        process.exit(1);
    }
}

checkInvoiceData();
