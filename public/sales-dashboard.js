// 全局变量
let currentPage = 1;
let currentLimit = 20;
let currentData = [];
let editingId = null;

// 全局变量
let currentTab = 'sales';
let executionPlans = [];
let editingPlanId = null;
let detailedStatsData = null; // 存储详细统计数据

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查认证状态
    if (!authManager.isAuthenticated()) {
        authManager.redirectToLogin();
        return;
    }

    loadRegions();
    loadBusinessManagers();
    loadStats();
    loadDetailedStats();
    loadData();

    // 绑定事件
    document.getElementById('regionFilter').addEventListener('change', onFilterChange);
    document.getElementById('businessManagerFilter').addEventListener('change', onFilterChange);
    document.getElementById('monthFilter').addEventListener('change', onFilterChange);
    document.getElementById('quarterFilter').addEventListener('change', onFilterChange);
    document.getElementById('sortBy').addEventListener('change', loadData);
    document.getElementById('sortOrder').addEventListener('change', loadData);
    document.getElementById('salesForm').addEventListener('submit', handleFormSubmit);

    // 执行计划相关事件
    document.getElementById('planCustomerFilter').addEventListener('change', loadExecutionPlans);
    document.getElementById('planStatusFilter').addEventListener('change', loadExecutionPlans);
    document.getElementById('planForm').addEventListener('submit', handlePlanFormSubmit);
});

// 加载区域列表
async function loadRegions() {
    try {
        const response = await authManager.authenticatedFetch('/api/regions');
        const result = await response.json();
        
        if (result.success) {
            const regionFilter = document.getElementById('regionFilter');
            result.data.forEach(region => {
                const option = document.createElement('option');
                option.value = region;
                option.textContent = region;
                regionFilter.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载区域列表失败:', error);
    }
}

// 加载商务负责人列表
async function loadBusinessManagers() {
    try {
        const response = await authManager.authenticatedFetch('/api/business-managers');
        const result = await response.json();

        if (result.success) {
            const businessManagerFilter = document.getElementById('businessManagerFilter');
            const editBusinessManager = document.getElementById('editBusinessManager');

            // 更新筛选器（保留现有的"全部负责人"选项）
            const currentFilterValue = businessManagerFilter.value;
            businessManagerFilter.innerHTML = '<option value="all">全部负责人</option>';
            businessManagerFilter.innerHTML += '<option value="未分配">未分配</option>';

            // 更新编辑表单
            editBusinessManager.innerHTML = '<option value="">请选择商务负责人</option>';
            editBusinessManager.innerHTML += '<option value="未分配">未分配</option>';

            result.data.forEach(manager => {
                // 添加到筛选器
                const filterOption = document.createElement('option');
                filterOption.value = manager.name;
                filterOption.textContent = `${manager.name} (${manager.username})`;
                businessManagerFilter.appendChild(filterOption);

                // 添加到编辑表单
                const editOption = document.createElement('option');
                editOption.value = manager.name;
                editOption.textContent = `${manager.name} (${manager.username})`;
                editBusinessManager.appendChild(editOption);
            });

            // 恢复筛选器的选中状态
            businessManagerFilter.value = currentFilterValue;
        }
    } catch (error) {
        console.error('加载商务负责人列表失败:', error);
    }
}

// 加载统计数据
async function loadStats() {
    try {
        // 获取当前筛选条件
        const region = document.getElementById('regionFilter').value;
        const businessManager = document.getElementById('businessManagerFilter').value;
        const month = document.getElementById('monthFilter').value;
        const quarter = document.getElementById('quarterFilter').value;

        const params = new URLSearchParams();
        if (region !== 'all') {
            params.append('region', region);
        }
        if (businessManager !== 'all') {
            params.append('businessManager', encodeURIComponent(businessManager));
        }
        if (month !== 'all') {
            params.append('month', month);
        }
        if (quarter !== 'all') {
            params.append('quarter', quarter);
        }

        const response = await authManager.authenticatedFetch(`/api/stats?${params}`);
        const result = await response.json();

        if (result.success) {
            const stats = result.data.summary;
            document.getElementById('totalRecords').textContent = stats.totalRecords;
            document.getElementById('totalPlan').textContent = stats.totalPlan.toFixed(2);
            document.getElementById('totalPayment').textContent = stats.totalSales.toFixed(2);
            // document.getElementById('paymentRate').textContent = stats.paymentRate.toFixed(2) + '%';

            // 更新统计标签，显示筛选信息
            updateStatsLabels(month, quarter);
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
}

// 加载详细统计数据
async function loadDetailedStats() {
    try {
        // 获取当前筛选条件
        const region = document.getElementById('regionFilter').value;
        const businessManager = document.getElementById('businessManagerFilter').value;
        const month = document.getElementById('monthFilter').value;
        const quarter = document.getElementById('quarterFilter').value;

        const params = new URLSearchParams();
        if (region !== 'all') {
            params.append('region', region);
        }
        if (businessManager !== 'all') {
            params.append('businessManager', encodeURIComponent(businessManager));
        }
        if (month !== 'all') {
            params.append('month', month);
        }
        if (quarter !== 'all') {
            params.append('quarter', quarter);
        }

        const url = params.toString() ? `/api/stats/detailed?${params}` : '/api/stats/detailed';
        const response = await authManager.authenticatedFetch(url);
        const result = await response.json();

        if (result.success && result.data) {
            const stats = result.data;
            detailedStatsData = stats; // 保存数据供详情查看使用

            // 近一周项目回款
            document.getElementById('weeklyPayment').textContent = (stats.weeklyPayment.amount || 0).toFixed(2);
            document.getElementById('weeklyPaymentCount').textContent = `${stats.weeklyPayment.count || 0} 个项目`;

            // 已开票待回款项目
            document.getElementById('invoicedPending').textContent = (stats.invoicedPending.amount || 0).toFixed(2);
            document.getElementById('invoicedPendingCount').textContent = `${stats.invoicedPending.count || 0} 个项目`;

            // 近期预计待开票金额
            document.getElementById('expectedInvoicing').textContent = (stats.expectedInvoicing.amount || 0).toFixed(2);
            document.getElementById('expectedInvoicingCount').textContent = `${stats.expectedInvoicing.count || 0} 个项目`;

            // 本周已签约和完成采购项目
            document.getElementById('weeklySignedCompleted').textContent = (stats.weeklySignedCompleted.amount || 0).toFixed(2);
            document.getElementById('weeklySignedCompletedCount').textContent = `${stats.weeklySignedCompleted.count || 0} 个项目`;

            // 本周商谈及招标结束待签合同项目
            document.getElementById('weeklyNegotiationPending').textContent = (stats.weeklyNegotiationPending.amount || 0).toFixed(2);
            document.getElementById('weeklyNegotiationPendingCount').textContent = `${stats.weeklyNegotiationPending.count || 0} 个项目`;

            // 更新详细统计标签，显示筛选信息
            updateDetailedStatsLabels();
        }
    } catch (error) {
        console.error('加载详细统计数据失败:', error);
        // 设置错误状态
        const errorElements = [
            'weeklyPayment', 'weeklyPaymentCount',
            'invoicedPending', 'invoicedPendingCount',
            'expectedInvoicing', 'expectedInvoicingCount',
            'weeklySignedCompleted', 'weeklySignedCompletedCount',
            'weeklyNegotiationPending', 'weeklyNegotiationPendingCount'
        ];

        errorElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = id.includes('Count') ? '错误' : '错误';
            }
        });
    }
}

// 更新详细统计标签
function updateDetailedStatsLabels() {
    const region = document.getElementById('regionFilter').value;
    const businessManager = document.getElementById('businessManagerFilter').value;
    const month = document.getElementById('monthFilter').value;
    const quarter = document.getElementById('quarterFilter').value;

    let suffix = '';
    const filters = [];

    if (region && region !== 'all') {
        filters.push(`${region}`);
    }

    if (businessManager && businessManager !== 'all') {
        filters.push(`${businessManager}`);
    }

    if (month && month !== 'all') {
        const monthNames = {
            'jan': '1月', 'feb': '2月', 'mar': '3月', 'apr': '4月',
            'may': '5月', 'jun': '6月', 'jul': '7月', 'aug': '8月',
            'sep': '9月', 'oct': '10月', 'nov': '11月', 'dec': '12月'
        };
        filters.push(`${monthNames[month]}`);
    } else if (quarter && quarter !== 'all') {
        const quarterNames = {
            'Q1': '第一季度', 'Q2': '第二季度', 'Q3': '第三季度', 'Q4': '第四季度'
        };
        filters.push(`${quarterNames[quarter]}`);
    }

    if (filters.length > 0) {
        suffix = ` (${filters.join(' | ')})`;
    }

    // 更新详细统计卡片的标签
    const detailedStatsLabels = [
        { id: 'weeklyPayment', baseLabel: '近一周项目回款(万元)' },
        { id: 'invoicedPending', baseLabel: '已开票待回款(万元)' },
        { id: 'expectedInvoicing', baseLabel: '近期预计待开票(万元)' },
        { id: 'weeklySignedCompleted', baseLabel: '本周已签约完成(万元)' },
        { id: 'weeklyNegotiationPending', baseLabel: '本周商谈招标待签(万元)' }
    ];

    detailedStatsLabels.forEach(item => {
        const element = document.getElementById(item.id);
        if (element) {
            const labelElement = element.parentElement.querySelector('.stat-label');
            if (labelElement) {
                labelElement.textContent = item.baseLabel + suffix;
            }
        }
    });
}

// 更新统计标签
function updateStatsLabels(month, quarter) {
    let suffix = '';
    if (month && month !== 'all') {
        const monthNames = {
            'jan': '1月', 'feb': '2月', 'mar': '3月', 'apr': '4月',
            'may': '5月', 'jun': '6月', 'jul': '7月', 'aug': '8月',
            'sep': '9月', 'oct': '10月', 'nov': '11月', 'dec': '12月'
        };
        suffix = ` (${monthNames[month]})`;
    } else if (quarter && quarter !== 'all') {
        const quarterNames = {
            'Q1': '第一季度', 'Q2': '第二季度', 'Q3': '第三季度', 'Q4': '第四季度'
        };
        suffix = ` (${quarterNames[quarter]})`;
    }

    document.querySelector('#totalPlan').parentElement.querySelector('.stat-label').textContent = `总销售计划(万元)${suffix}`;
    document.querySelector('#totalPayment').parentElement.querySelector('.stat-label').textContent = `实际销售额(万元)${suffix}`;
    // document.querySelector('#paymentRate').parentElement.querySelector('.stat-label').textContent = `回款率(%)${suffix}`;

    // 更新筛选状态提示
    updateFilterStatus();
}

// 更新筛选状态提示
function updateFilterStatus() {
    const region = document.getElementById('regionFilter').value;
    const businessManager = document.getElementById('businessManagerFilter').value;
    const month = document.getElementById('monthFilter').value;
    const quarter = document.getElementById('quarterFilter').value;

    const filterStatus = document.getElementById('filterStatus');
    const filterStatusText = document.getElementById('filterStatusText');

    const filters = [];

    if (region && region !== 'all') {
        filters.push(`区域: ${region}`);
    }

    if (businessManager && businessManager !== 'all') {
        filters.push(`负责人: ${businessManager}`);
    }

    if (month && month !== 'all') {
        const monthNames = {
            'jan': '1月', 'feb': '2月', 'mar': '3月', 'apr': '4月',
            'may': '5月', 'jun': '6月', 'jul': '7月', 'aug': '8月',
            'sep': '9月', 'oct': '10月', 'nov': '11月', 'dec': '12月'
        };
        filters.push(`月份: ${monthNames[month]}`);
    }

    if (quarter && quarter !== 'all') {
        const quarterNames = {
            'Q1': '第一季度', 'Q2': '第二季度', 'Q3': '第三季度', 'Q4': '第四季度'
        };
        filters.push(`季度: ${quarterNames[quarter]}`);
    }

    if (filters.length > 0) {
        filterStatusText.textContent = `当前筛选: ${filters.join(' | ')}`;
        filterStatus.style.display = 'block';
    } else {
        filterStatus.style.display = 'none';
    }
}

// 筛选条件变化处理
function onFilterChange() {
    // 重置页码
    currentPage = 1;

    // 更新筛选状态提示
    updateFilterStatus();

    // 同时更新数据和统计
    loadData();
    loadStats();
    loadDetailedStats();
}

// 重置筛选条件
function resetFilters() {
    document.getElementById('regionFilter').value = 'all';
    document.getElementById('businessManagerFilter').value = 'all';
    document.getElementById('monthFilter').value = 'all';
    document.getElementById('quarterFilter').value = 'all';

    // 重置页码
    currentPage = 1;

    // 更新筛选状态提示
    updateFilterStatus();

    // 重新加载数据
    loadData();
    loadStats();
    loadDetailedStats();
}

// 加载销售数据
async function loadData() {
    const loading = document.getElementById('loading');
    const table = document.getElementById('salesTable');
    const pagination = document.getElementById('pagination');
    
    loading.style.display = 'block';
    table.style.display = 'none';
    pagination.style.display = 'none';
    
    try {
        const region = document.getElementById('regionFilter').value;
        const businessManager = document.getElementById('businessManagerFilter').value;
        const month = document.getElementById('monthFilter').value;
        const quarter = document.getElementById('quarterFilter').value;
        const sortBy = document.getElementById('sortBy').value;
        const sortOrder = document.getElementById('sortOrder').value;

        const params = new URLSearchParams({
            page: currentPage,
            limit: currentLimit,
            sortBy,
            order: sortOrder
        });

        if (region !== 'all') {
            params.append('region', region);
        }

        if (businessManager !== 'all') {
            params.append('businessManager', encodeURIComponent(businessManager));
        }

        if (month !== 'all') {
            params.append('month', month);
        }

        if (quarter !== 'all') {
            params.append('quarter', quarter);
        }
        
        const response = await authManager.authenticatedFetch(`/api/sales?${params}`);
        const result = await response.json();
        
        if (result.success) {
            currentData = result.data;
            renderTable(result.data);
            renderPagination(result);
            
            loading.style.display = 'none';
            table.style.display = 'table';
            pagination.style.display = 'flex';
        } else {
            throw new Error(result.message || '加载数据失败');
        }
    } catch (error) {
        console.error('加载数据失败:', error);
        loading.textContent = '加载数据失败: ' + error.message;
    }
}

// 渲染表格
function renderTable(data) {
    const tbody = document.getElementById('salesTableBody');
    tbody.innerHTML = '';

    // 获取当前筛选条件
    const month = document.getElementById('monthFilter').value;
    const quarter = document.getElementById('quarterFilter').value;

    data.forEach(item => {
        const row = document.createElement('tr');



        // 根据筛选条件显示不同的标题信息
        let filterLabel = '';
        if (month && month !== 'all') {
            const monthNames = {
                'jan': '1月', 'feb': '2月', 'mar': '3月', 'apr': '4月',
                'may': '5月', 'jun': '6月', 'jul': '7月', 'aug': '8月',
                'sep': '9月', 'oct': '10月', 'nov': '11月', 'dec': '12月'
            };
            filterLabel = ` (${monthNames[month]}数据)`;
        } else if (quarter && quarter !== 'all') {
            const quarterNames = {
                'Q1': '第一季度', 'Q2': '第二季度', 'Q3': '第三季度', 'Q4': '第四季度'
            };
            filterLabel = ` (${quarterNames[quarter]}数据)`;
        }

        row.innerHTML = `
            <td>${item.id}</td>
            <td><span class="region-tag region-${item.region}">${item.region}</span></td>
            <td>
                <a href="/customer-detail.html?customer=${encodeURIComponent(item.customerName)}"
                   style="color: #667eea; text-decoration: none; font-weight: 500;">
                    ${item.customerName}${filterLabel}
                </a>
            </td>
            <td>
                <span style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 0.25rem 0.5rem; border-radius: 12px; font-size: 0.8rem; font-weight: 500;">
                    ${item.businessManager || '未分配'}
                </span>
            </td>
            <td class="number">${item.totalPlan.toFixed(2)}</td>
            <td class="number">${item.actualPayment.toFixed(2)}</td>
            <td class="number">${item.actualSales.toFixed(2)}</td>
            <td>
                <button class="data-button" onclick="showMonthlyDataForCustomer('${item.customerName}')">
                    月度
                </button>
            </td>
            <td>
                <button class="data-button" onclick="showQuarterlyDataForCustomer('${item.customerName}')">
                    季度
                </button>
            </td>
            <td class="action-buttons">
                <button class="btn-edit" onclick="editRecord(${item.id})">编辑</button>
                <button class="btn-delete" onclick="deleteRecord(${item.id})">删除</button>
            </td>
        `;

        tbody.appendChild(row);
    });
}

// 渲染分页
function renderPagination(result) {
    const pageInfo = document.getElementById('pageInfo');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    pageInfo.textContent = `第 ${result.page} 页，共 ${result.totalPages} 页 (总计 ${result.total} 条记录)`;
    
    prevBtn.disabled = result.page <= 1;
    nextBtn.disabled = result.page >= result.totalPages;
}

// 翻页
function changePage(direction) {
    const newPage = currentPage + direction;
    if (newPage >= 1) {
        currentPage = newPage;
        loadData();
    }
}

// 显示添加模态框
function showAddModal() {
    editingId = null;
    document.getElementById('modalTitle').textContent = '添加销售记录';
    document.getElementById('salesForm').reset();

    // 自动建议下一个可用序号
    if (currentData && currentData.length > 0) {
        const maxId = Math.max(...currentData.map(item => item.id));
        document.getElementById('editId').value = maxId + 1;
        document.getElementById('editId').placeholder = `建议序号: ${maxId + 1}`;
    } else {
        document.getElementById('editId').value = 1;
        document.getElementById('editId').placeholder = '建议序号: 1';
    }

    document.getElementById('editModal').classList.add('show');
}

// 编辑记录
async function editRecord(id) {
    try {
        const response = await authManager.authenticatedFetch(`/api/sales/${id}`);
        const result = await response.json();
        
        if (result.success) {
            const data = result.data;
            editingId = id;
            
            document.getElementById('modalTitle').textContent = '编辑销售记录';
            document.getElementById('editId').value = data.id;
            document.getElementById('editRegion').value = data.region;
            document.getElementById('editCustomerName').value = data.customerName;
            document.getElementById('editBusinessManager').value = data.businessManager || '';

            document.getElementById('editModal').classList.add('show');
        }
    } catch (error) {
        console.error('加载记录失败:', error);
        alert('加载记录失败: ' + error.message);
    }
}

// 删除记录
async function deleteRecord(id) {
    if (!confirm('确定要删除这条记录吗？')) {
        return;
    }
    
    try {
        const response = await authManager.authenticatedFetch(`/api/sales/${id}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('删除成功');
            loadData();
            loadStats();
            loadDetailedStats();
        } else {
            throw new Error(result.message || '删除失败');
        }
    } catch (error) {
        console.error('删除失败:', error);
        alert('删除失败: ' + error.message);
    }
}

// 处理表单提交
async function handleFormSubmit(event) {
    event.preventDefault();
    
    // 获取序号值
    const idValue = document.getElementById('editId').value;

    const formData = {
        region: document.getElementById('editRegion').value,
        customerName: document.getElementById('editCustomerName').value,
        businessManager: document.getElementById('editBusinessManager').value
    };

    // 如果用户输入了序号，添加到formData中
    if (idValue && idValue.trim() !== '') {
        formData.id = parseInt(idValue);
    }
    
    try {
        const url = editingId ? `/api/sales/${editingId}` : '/api/sales';
        const method = editingId ? 'PUT' : 'POST';
        
        const response = await authManager.authenticatedFetch(url, {
            method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            let message = editingId ? '更新成功' : '添加成功';

            // 如果是新增且同时创建了客户详细信息，显示额外信息
            if (!editingId && result.customerDetails && result.customerDetails.created) {
                message += '\n\n✅ 已自动创建客户详细信息：';
                message += `\n• 客户名称：${formData.customerName}`;
                message += `\n• 所属区域：${formData.region}`;
                message += `\n• 项目数量：${result.customerDetails.summary.totalProjects}`;
                message += `\n• 预计收入：${result.customerDetails.summary.totalExpectedRevenue.toFixed(2)}万元`;
                message += '\n\n您可以点击客户名称查看详细页面管理具体项目。';
            }

            alert(message);
            closeModal();
            loadData();
            loadStats();
            loadDetailedStats();
        } else {
            throw new Error(result.message || '保存失败');
        }
    } catch (error) {
        console.error('保存失败:', error);
        alert('保存失败: ' + error.message);
    }
}

// 验证序号是否重复
function validateId() {
    const idInput = document.getElementById('editId');
    const messageElement = document.getElementById('idValidationMessage');
    const inputValue = idInput.value;

    if (!inputValue || inputValue.trim() === '') {
        messageElement.textContent = '序号用于排序和标识，建议使用唯一数字';
        messageElement.style.color = '#666';
        return;
    }

    const requestedId = parseInt(inputValue);

    // 检查是否与现有记录冲突（编辑时排除自己）
    const existingRecord = currentData.find(item =>
        item.id === requestedId && (!editingId || item.id !== editingId)
    );

    if (existingRecord) {
        messageElement.textContent = `⚠️ 序号 ${requestedId} 已被客户"${existingRecord.customerName}"使用`;
        messageElement.style.color = '#dc3545';
        idInput.style.borderColor = '#dc3545';
    } else {
        messageElement.textContent = `✅ 序号 ${requestedId} 可用`;
        messageElement.style.color = '#28a745';
        idInput.style.borderColor = '#28a745';
    }
}

// 关闭模态框
function closeModal() {
    document.getElementById('editModal').classList.remove('show');
    editingId = null;

    // 重置序号验证状态
    const idInput = document.getElementById('editId');
    const messageElement = document.getElementById('idValidationMessage');
    idInput.style.borderColor = '#ddd';
    messageElement.textContent = '序号用于排序和标识，建议使用唯一数字';
    messageElement.style.color = '#666';
}

// 为特定客户显示月度数据
function showMonthlyDataForCustomer(customerName) {
    const customer = currentData.find(item => item.customerName === customerName);
    if (customer) {
        showMonthlyData(customerName, customer.monthlyPlan || {}, customer.monthlyPayment || {}, customer.monthlySales || {});
    }
}

// 为特定客户显示季度数据
function showQuarterlyDataForCustomer(customerName) {
    const customer = currentData.find(item => item.customerName === customerName);
    if (customer) {
        showQuarterlyData(customerName, customer.quarterlyPlan || {}, customer.quarterlyPayment || {}, customer.quarterlySales || {});
    }
}

// 显示月度数据弹窗
function showMonthlyData(customerName, monthlyPlan, monthlyPayment, monthlySales) {
    const modal = document.getElementById('monthlyDataModal');
    const title = document.getElementById('monthlyModalTitle');
    const grid = document.getElementById('monthlyDataGrid');

    title.textContent = `${customerName} - 月度数据详情`;

    const months = [
        { key: 'jan', name: '1月' },
        { key: 'feb', name: '2月' },
        { key: 'mar', name: '3月' },
        { key: 'apr', name: '4月' },
        { key: 'may', name: '5月' },
        { key: 'jun', name: '6月' },
        { key: 'jul', name: '7月' },
        { key: 'aug', name: '8月' },
        { key: 'sep', name: '9月' },
        { key: 'oct', name: '10月' },
        { key: 'nov', name: '11月' },
        { key: 'dec', name: '12月' }
    ];

    grid.innerHTML = '';

    months.forEach(month => {
        const plan = monthlyPlan[month.key] || 0;
        const payment = monthlyPayment[month.key] || 0;
        const sales = monthlySales[month.key] || 0;
        if (plan > 0 || payment > 0 || sales > 0) {
            const item = document.createElement('div');
            item.className = 'data-item';
            item.innerHTML = `
                <div class="data-item-label">${month.name}</div>
                <div class="data-item-value">${plan.toFixed(2)}</div>
                <div class="data-item-sub">预计收入(万元)</div>
                <div class="data-item-value" style="color: #28a745;">${payment.toFixed(2)}</div>
                <div class="data-item-sub">实际回款(万元)</div>
                <div class="data-item-value" style="color: #17a2b8;">${sales.toFixed(2)}</div>
                <div class="data-item-sub">实际销售(万元)</div>
            `;
            grid.appendChild(item);
        }
    });

    if (grid.children.length === 0) {
        grid.innerHTML = '<div style="text-align: center; color: #666; padding: 2rem;">该客户暂无月度数据</div>';
    }

    modal.style.display = 'flex';
}

// 显示季度数据弹窗
function showQuarterlyData(customerName, quarterlyPlan, quarterlyPayment, quarterlySales) {
    const modal = document.getElementById('quarterlyDataModal');
    const title = document.getElementById('quarterlyModalTitle');
    const grid = document.getElementById('quarterlyDataGrid');

    title.textContent = `${customerName} - 季度数据详情`;

    const quarters = [
        { key: 'Q1', name: '第一季度' },
        { key: 'Q2', name: '第二季度' },
        { key: 'Q3', name: '第三季度' },
        { key: 'Q4', name: '第四季度' }
    ];

    grid.innerHTML = '';

    quarters.forEach(quarter => {
        const plan = quarterlyPlan[quarter.key] || 0;
        const payment = quarterlyPayment[quarter.key] || 0;
        const sales = quarterlySales[quarter.key] || 0;
        const item = document.createElement('div');
        item.className = 'data-item';
        item.innerHTML = `
            <div class="data-item-label">${quarter.name}</div>
            <div class="data-item-value">${plan.toFixed(2)}</div>
            <div class="data-item-sub">预计收入(万元)</div>
            <div class="data-item-value" style="color: #28a745;">${payment.toFixed(2)}</div>
            <div class="data-item-sub">实际回款(万元)</div>
            <div class="data-item-value" style="color: #17a2b8;">${sales.toFixed(2)}</div>
            <div class="data-item-sub">实际销售(万元)</div>
        `;
        grid.appendChild(item);
    });

    modal.style.display = 'flex';
}

// 关闭数据弹窗
function closeDataModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// 点击模态框外部关闭
document.getElementById('editModal').addEventListener('click', function(event) {
    if (event.target === this) {
        closeModal();
    }
});

// 为数据弹窗添加点击外部关闭功能
document.getElementById('monthlyDataModal').addEventListener('click', function(event) {
    if (event.target === this) {
        closeDataModal('monthlyDataModal');
    }
});

document.getElementById('quarterlyDataModal').addEventListener('click', function(event) {
    if (event.target === this) {
        closeDataModal('quarterlyDataModal');
    }
});

// ==================== 标签页管理 ====================

// 切换标签页
function switchTab(tabName, element) {
    // 更新标签按钮状态
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });

    // 如果传入了element参数，使用它；否则查找当前活动的按钮
    const activeButton = element || document.querySelector(`[onclick="switchTab('${tabName}')"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }

    // 更新标签内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(tabName + '-tab').classList.add('active');

    currentTab = tabName;

    // 根据标签页加载相应数据
    if (tabName === 'execution-plans') {
        loadExecutionPlans();
        loadCustomersForPlan();
    }
}

// ==================== 执行计划管理 ====================

// 加载执行计划数据
async function loadExecutionPlans() {
    try {
        document.getElementById('planLoading').style.display = 'block';
        document.getElementById('executionPlansContainer').style.display = 'none';

        const customerFilter = document.getElementById('planCustomerFilter').value;
        const statusFilter = document.getElementById('planStatusFilter').value;

        let url = '/api/execution-plans';
        const params = new URLSearchParams();

        if (customerFilter && customerFilter !== 'all') {
            params.append('customerName', customerFilter);
        }

        if (params.toString()) {
            url += '?' + params.toString();
        }

        const response = await authManager.authenticatedFetch(url);
        const result = await response.json();

        if (result.success) {
            executionPlans = result.data;

            // 按状态过滤
            if (statusFilter && statusFilter !== 'all') {
                executionPlans = executionPlans.filter(plan => plan.executionStatus === statusFilter);
            }

            renderExecutionPlans();
        } else {
            throw new Error(result.message || '获取执行计划失败');
        }
    } catch (error) {
        console.error('加载执行计划失败:', error);
        alert('加载执行计划失败: ' + error.message);
    } finally {
        document.getElementById('planLoading').style.display = 'none';
        document.getElementById('executionPlansContainer').style.display = 'block';
    }
}

// 渲染执行计划列表
function renderExecutionPlans() {
    const container = document.getElementById('executionPlansContainer');

    if (executionPlans.length === 0) {
        container.innerHTML = '<div style="text-align: center; color: #666; padding: 2rem;">暂无执行计划</div>';
        return;
    }

    container.innerHTML = executionPlans.map(plan => `
        <div class="execution-plan-card">
            <div class="execution-plan-header">
                <div>
                    <div class="execution-plan-title">${plan.planDescription}</div>
                    <div class="execution-plan-customer">客户：${plan.customerName}</div>
                </div>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 0.5rem;">
                    <div class="execution-plan-deadline">截止：${plan.deadline}</div>
                    <div class="execution-plan-status ${plan.executionStatus === '完成' ? 'status-completed' : 'status-pending'}">
                        ${plan.executionStatus}
                    </div>
                </div>
            </div>

            <div class="execution-plan-description">
                ${plan.planDescription}
            </div>

            <div class="execution-plan-effects">
                <div class="effect-item">
                    <div class="effect-label">预期效果</div>
                    <div class="effect-content">${plan.expectedEffect}</div>
                </div>
                <div class="effect-item">
                    <div class="effect-label">实际效果</div>
                    <div class="effect-content">${plan.actualEffect || '暂无记录'}</div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn-edit" onclick="editPlan(${plan.id})">编辑</button>
                <button class="btn-delete" onclick="deletePlan(${plan.id})">删除</button>
            </div>
        </div>
    `).join('');
}

// 加载客户列表用于执行计划
async function loadCustomersForPlan() {
    try {
        const response = await authManager.authenticatedFetch('/api/sales');
        const result = await response.json();

        if (result.success) {
            const customers = [...new Set(result.data.map(item => item.customerName))].sort();

            // 更新客户筛选下拉框
            const customerFilter = document.getElementById('planCustomerFilter');
            customerFilter.innerHTML = '<option value="all">全部客户</option>';
            customers.forEach(customer => {
                customerFilter.innerHTML += `<option value="${customer}">${customer}</option>`;
            });

            // 更新执行计划表单中的客户下拉框
            const planCustomerName = document.getElementById('planCustomerName');
            planCustomerName.innerHTML = '<option value="">请选择客户</option>';
            customers.forEach(customer => {
                planCustomerName.innerHTML += `<option value="${customer}">${customer}</option>`;
            });
        }
    } catch (error) {
        console.error('加载客户列表失败:', error);
    }
}

// 显示添加执行计划模态框
function showAddPlanModal() {
    document.getElementById('planModalTitle').textContent = '添加执行计划';
    document.getElementById('planForm').reset();
    editingPlanId = null;
    document.getElementById('planModal').classList.add('show');
}

// 编辑执行计划
function editPlan(planId) {
    const plan = executionPlans.find(p => p.id === planId);
    if (!plan) return;

    document.getElementById('planModalTitle').textContent = '编辑执行计划';
    document.getElementById('planCustomerName').value = plan.customerName;
    document.getElementById('planDeadline').value = plan.deadline;
    document.getElementById('planDescription').value = plan.planDescription;
    document.getElementById('planExpectedEffect').value = plan.expectedEffect;
    document.getElementById('planActualEffect').value = plan.actualEffect || '';
    document.getElementById('planExecutionStatus').value = plan.executionStatus;

    editingPlanId = planId;
    document.getElementById('planModal').classList.add('show');
}

// 删除执行计划
async function deletePlan(planId) {
    if (!confirm('确定要删除这个执行计划吗？')) return;

    try {
        const response = await authManager.authenticatedFetch(`/api/execution-plans/${planId}`, {
            method: 'DELETE'
        });

        const result = await response.json();
        if (result.success) {
            alert('执行计划删除成功');
            loadExecutionPlans();
        } else {
            throw new Error(result.message || '删除失败');
        }
    } catch (error) {
        console.error('删除执行计划失败:', error);
        alert('删除执行计划失败: ' + error.message);
    }
}

// 处理执行计划表单提交
async function handlePlanFormSubmit(event) {
    event.preventDefault();

    const formData = {
        customerName: document.getElementById('planCustomerName').value,
        deadline: document.getElementById('planDeadline').value,
        planDescription: document.getElementById('planDescription').value,
        expectedEffect: document.getElementById('planExpectedEffect').value,
        actualEffect: document.getElementById('planActualEffect').value,
        executionStatus: document.getElementById('planExecutionStatus').value
    };

    try {
        const url = editingPlanId ? `/api/execution-plans/${editingPlanId}` : '/api/execution-plans';
        const method = editingPlanId ? 'PUT' : 'POST';

        const response = await authManager.authenticatedFetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();
        if (result.success) {
            alert(editingPlanId ? '执行计划更新成功' : '执行计划创建成功');
            closePlanModal();
            loadExecutionPlans();
        } else {
            throw new Error(result.message || '保存失败');
        }
    } catch (error) {
        console.error('保存执行计划失败:', error);
        alert('保存执行计划失败: ' + error.message);
    }
}

// 关闭执行计划模态框
function closePlanModal() {
    document.getElementById('planModal').classList.remove('show');
    editingPlanId = null;
}

// 为执行计划模态框添加点击外部关闭功能
document.getElementById('planModal').addEventListener('click', function(event) {
    if (event.target === this) {
        closePlanModal();
    }
});

// ==================== 项目详情功能 ====================

// 显示项目详情
async function showProjectDetails(type) {
    try {
        // 获取当前筛选条件
        const region = document.getElementById('regionFilter').value;
        const businessManager = document.getElementById('businessManagerFilter').value;
        const month = document.getElementById('monthFilter').value;
        const quarter = document.getElementById('quarterFilter').value;

        const params = new URLSearchParams();
        if (region !== 'all') {
            params.append('region', region);
        }
        if (businessManager !== 'all') {
            params.append('businessManager', encodeURIComponent(businessManager));
        }
        if (month !== 'all') {
            params.append('month', month);
        }
        if (quarter !== 'all') {
            params.append('quarter', quarter);
        }

        const url = params.toString() ?
            `/api/project-details/${type}?${params}` :
            `/api/project-details/${type}`;

        const response = await authManager.authenticatedFetch(url);
        const result = await response.json();

        if (result.success) {
            renderProjectDetails(result.data, type);
        } else {
            throw new Error(result.message || '获取项目详情失败');
        }
    } catch (error) {
        console.error('获取项目详情失败:', error);
        alert('获取项目详情失败: ' + error.message);
    }
}

// 渲染项目详情
function renderProjectDetails(projects, type) {
    const modal = document.getElementById('projectDetailsModal');
    const title = document.getElementById('projectDetailsTitle');
    const content = document.getElementById('projectDetailsContent');

    // 设置标题
    const typeNames = {
        'weeklyPayment': '近一周项目回款详情',
        'invoicedPending': '已开票待回款项目详情',
        'expectedInvoicing': '近期预计待开票项目详情',
        'weeklySignedCompleted': '本周已签约完成项目详情',
        'weeklyNegotiationPending': '本周商谈招标待签项目详情'
    };
    title.textContent = typeNames[type] || '项目详情';

    if (projects.length === 0) {
        content.innerHTML = '<div style="text-align: center; color: #666; padding: 2rem;">暂无相关项目</div>';
    } else {
        // 生成表格
        let tableHTML = `
            <table class="project-details-table">
                <thead>
                    <tr>
                        <th>客户名称</th>
                        <th>项目名称</th>
                        <th>产品类型</th>
                        <th>项目阶段</th>
                        <th>商务负责人</th>
                        <th>预期收入(万元)</th>
                        ${type === 'weeklyPayment' ? '<th>回款金额(万元)</th><th>回款时间</th>' : ''}
                        ${type === 'invoicedPending' ? '<th>已回款(万元)</th><th>待回款(万元)</th>' : ''}
                        ${type === 'expectedInvoicing' ? '<th>完成度</th><th>预期签约月份</th>' : ''}
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
        `;

        projects.forEach(project => {
            const stageClass = `stage-${project.projectStage.replace(/\s+/g, '')}`;
            tableHTML += `
                <tr>
                    <td>${project.customerName}</td>
                    <td>${project.projectName}</td>
                    <td>${project.productType || '-'}</td>
                    <td><span class="project-stage-tag ${stageClass}">${project.projectStage}</span></td>
                    <td>${project.businessManager || '未分配'}</td>
                    <td class="amount-highlight">${(project.expectedRevenue || 0).toFixed(2)}</td>
            `;

            // 根据类型添加特定列
            if (type === 'weeklyPayment') {
                tableHTML += `
                    <td class="amount-highlight">${(project.paymentAmount || 0).toFixed(2)}</td>
                    <td>${project.paymentTime || '-'}</td>
                `;
            } else if (type === 'invoicedPending') {
                tableHTML += `
                    <td class="amount-highlight">${(project.paymentAmount || 0).toFixed(2)}</td>
                    <td class="amount-highlight" style="color: #f57c00;">${(project.pendingAmount || 0).toFixed(2)}</td>
                `;
            } else if (type === 'expectedInvoicing') {
                tableHTML += `
                    <td>${((project.percentage || 0) * 100).toFixed(0)}%</td>
                    <td>${project.expectedSignMonth || '-'}</td>
                `;
            }

            tableHTML += `
                    <td>
                        <a href="/customer-detail.html?customer=${encodeURIComponent(project.customerName)}"
                           target="_blank" style="color: #667eea; text-decoration: none;">
                            查看详情
                        </a>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                </tbody>
            </table>
        `;

        // 计算总金额
        let totalAmount = 0;
        let summaryHTML = `
            <div class="project-summary">
                <strong>统计汇总：</strong>共 ${projects.length} 个项目
        `;

        if (type === 'weeklyPayment') {
            totalAmount = projects.reduce((sum, p) => sum + (parseFloat(p.paymentAmount) || 0), 0);
            summaryHTML += `，总回款金额：${totalAmount.toFixed(2)} 万元`;
        } else if (type === 'invoicedPending') {
            totalAmount = projects.reduce((sum, p) => sum + (parseFloat(p.pendingAmount) || 0), 0);
            summaryHTML += `，总待回款金额：${totalAmount.toFixed(2)} 万元`;
        } else {
            totalAmount = projects.reduce((sum, p) => sum + (parseFloat(p.expectedRevenue) || 0), 0);
            summaryHTML += `，总预期收入：${totalAmount.toFixed(2)} 万元`;
        }

        summaryHTML += '</div>';
        content.innerHTML = tableHTML + summaryHTML;
    }

    modal.classList.add('show');
}

// 关闭项目详情模态框
function closeProjectDetailsModal() {
    document.getElementById('projectDetailsModal').classList.remove('show');
}

// 为项目详情模态框添加点击外部关闭功能
document.getElementById('projectDetailsModal').addEventListener('click', function(event) {
    if (event.target === this) {
        closeProjectDetailsModal();
    }
});
