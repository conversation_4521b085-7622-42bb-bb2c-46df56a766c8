const { MongoClient } = require('mongodb');
const config = require('../config/database');

class MongoManager {
    constructor() {
        this.config = config.mongodb;
        this.url = this.config.getConnectionString();
        this.dbName = this.config.database;
        this.options = this.config.getConnectionOptions();
        this.client = null;
        this.db = null;
        this.isConnected = false;
    }

    // 连接到MongoDB
    async connect() {
        try {
            if (this.isConnected) {
                return this.db;
            }

            console.log(`🔄 正在连接MongoDB: ${this.url.replace(/:([^:@]+)@/, ':***@')}`);
            this.client = new MongoClient(this.url, this.options);
            await this.client.connect();
            // 测试连接
            await this.client.db('admin').command({ ping: 1 });

            this.db = this.client.db(this.dbName);
            this.isConnected = true;

            const maskedUrl = this.url.replace(/:([^:@]+)@/, ':***@');
            console.log(`✅ MongoDB连接成功: ${maskedUrl}`);

            // 显示连接信息
            if (this.config.isProduction()) {
                console.log('🔒 生产环境连接已建立');
            } else {
                console.log('🔧 开发环境连接已建立');
            }

            return this.db;
        } catch (error) {
            console.error('❌ MongoDB连接失败:', error.message);

            // 提供更详细的错误信息
            if (error.code === 'ENOTFOUND') {
                console.error('   原因: 无法解析主机名，请检查网络连接和主机地址');
            } else if (error.code === 'ECONNREFUSED') {
                console.error('   原因: 连接被拒绝，请检查MongoDB服务是否运行');
            } else if (error.code === 18) {
                console.error('   原因: 认证失败，请检查用户名和密码');
            }

            throw error;
        }
    }

    // 断开连接
    async disconnect() {
        try {
            if (this.client) {
                await this.client.close();
                this.isConnected = false;
                console.log('✅ MongoDB连接已关闭');
            }
        } catch (error) {
            console.error('❌ MongoDB断开连接失败:', error.message);
        }
    }

    // 确保连接
    async ensureConnection() {
        if (!this.isConnected) {
            await this.connect();
        }
        return this.db;
    }

    // ==================== 销售数据管理 ====================
    
    // 加载销售数据
    async loadSalesData() {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('sales_funnel_data');
            const data = await collection.find({}).toArray();
            console.log(`📊 从MongoDB加载 ${data.length} 条销售数据`);
            return data;
        } catch (error) {
            console.error('加载销售数据失败:', error);
            return [];
        }
    }

    // 保存销售数据
    async saveSalesData(data) {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('sales_funnel_data');
            
            // 清空集合并插入新数据
            await collection.deleteMany({});
            if (data.length > 0) {
                await collection.insertMany(data);
            }
            
            console.log(`💾 销售数据已保存到MongoDB: ${data.length} 条记录`);
            return true;
        } catch (error) {
            console.error('保存销售数据失败:', error);
            return false;
        }
    }

    // ==================== 客户详细数据管理 ====================
    
    // 加载客户数据
    async loadCustomerData() {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('customer_details_data');
            const documents = await collection.find({}).toArray();
            
            // 转换为原有的对象格式 {customerName: customerData}
            const customerData = {};
            documents.forEach(doc => {
                const { _id, ...customerInfo } = doc;
                customerData[customerInfo.customerName] = customerInfo;
            });
            
            console.log(`👥 从MongoDB加载 ${Object.keys(customerData).length} 个客户的详细数据`);
            return customerData;
        } catch (error) {
            console.error('加载客户数据失败:', error);
            return {};
        }
    }

    // 保存客户数据
    async saveCustomerData(customerData) {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('customer_details_data');
            
            // 清空集合
            await collection.deleteMany({});
            
            // 转换为文档数组并插入
            const documents = Object.values(customerData);
            if (documents.length > 0) {
                await collection.insertMany(documents);
            }
            
            console.log(`💾 客户数据已保存到MongoDB: ${documents.length} 个客户`);
            return true;
        } catch (error) {
            console.error('保存客户数据失败:', error);
            return false;
        }
    }

    // ==================== 组织架构数据管理 ====================
    
    // 加载组织架构数据
    async loadOrganizationData() {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('organization_data');
            const documents = await collection.find({}).toArray();
            
            // 转换为原有的对象格式 {customerName: organizationData}
            const organizationData = {};
            documents.forEach(doc => {
                const { _id, ...orgInfo } = doc;
                organizationData[orgInfo.customerName] = orgInfo;
            });
            
            console.log(`🏢 从MongoDB加载 ${Object.keys(organizationData).length} 个客户的组织架构数据`);
            return organizationData;
        } catch (error) {
            console.error('加载组织架构数据失败:', error);
            return {};
        }
    }

    // 保存组织架构数据
    async saveOrganizationData(organizationData) {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('organization_data');
            
            // 清空集合
            await collection.deleteMany({});
            
            // 转换为文档数组并插入
            const documents = Object.values(organizationData);
            if (documents.length > 0) {
                await collection.insertMany(documents);
            }
            
            console.log(`💾 组织架构数据已保存到MongoDB: ${documents.length} 个客户`);
            return true;
        } catch (error) {
            console.error('保存组织架构数据失败:', error);
            return false;
        }
    }

    // ==================== 商务经理数据管理 ====================

    // 加载商务经理数据（用于向后兼容）
    async loadBusinessManagersData() {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('business_managers');
            const data = await collection.find({}).toArray();
            console.log(`👔 从MongoDB加载 ${data.length} 条商务经理数据`);
            return data;
        } catch (error) {
            console.error('加载商务经理数据失败:', error);
            return [];
        }
    }

    // 保存商务经理数据（用于向后兼容）
    async saveBusinessManagersData(data) {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('business_managers');

            // 清空集合并插入新数据
            await collection.deleteMany({});
            if (data.length > 0) {
                await collection.insertMany(data);
            }

            console.log(`💾 商务经理数据已保存到MongoDB: ${data.length} 条记录`);
            return true;
        } catch (error) {
            console.error('保存商务经理数据失败:', error);
            return false;
        }
    }

    // ==================== 回款记录数据管理 ====================
    
    // 加载回款记录数据
    async loadPaymentRecordsData() {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('payment_records_data');
            const data = await collection.find({}).toArray();
            console.log(`💰 从MongoDB加载 ${data.length} 条回款记录数据`);
            return data;
        } catch (error) {
            console.error('加载回款记录数据失败:', error);
            return [];
        }
    }

    // 保存回款记录数据
    async savePaymentRecordsData(data) {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('payment_records_data');
            
            // 清空集合并插入新数据
            await collection.deleteMany({});
            if (data.length > 0) {
                await collection.insertMany(data);
            }
            
            console.log(`💾 回款记录数据已保存到MongoDB: ${data.length} 条记录`);
            return true;
        } catch (error) {
            console.error('保存回款记录数据失败:', error);
            return false;
        }
    }

    // ==================== Q&A数据管理 ====================
    
    // 加载Q&A数据
    async loadQAData() {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('qa_data');
            const data = await collection.find({}).toArray();
            console.log(`📚 从MongoDB加载 ${data.length} 条Q&A数据`);
            return data;
        } catch (error) {
            console.error('加载Q&A数据失败:', error);
            return [];
        }
    }

    // 保存Q&A数据
    async saveQAData(data) {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('qa_data');
            
            // 清空集合并插入新数据
            await collection.deleteMany({});
            if (data.length > 0) {
                await collection.insertMany(data);
            }
            
            console.log(`💾 Q&A数据已保存到MongoDB: ${data.length} 条记录`);
            return true;
        } catch (error) {
            console.error('保存Q&A数据失败:', error);
            return false;
        }
    }

    // 根据用户名查找商务经理
    async findBusinessManagerByUsername(username) {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('business_managers');
            const manager = await collection.findOne({ username: username });
            return manager;
        } catch (error) {
            console.error('查找商务经理失败:', error);
            return null;
        }
    }

    // 验证商务经理登录
    async authenticateBusinessManager(username, password) {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('business_managers');
            const manager = await collection.findOne({
                username: username,
                password: password
            });
            return manager;
        } catch (error) {
            console.error('验证商务经理登录失败:', error);
            return null;
        }
    }

    // ==================== 执行计划数据管理 ====================
    
    // 加载执行计划数据
    async loadExecutionPlansData() {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('execution_plans');
            const data = await collection.find({}).toArray();
            console.log(`📋 从MongoDB加载 ${data.length} 条执行计划数据`);
            return data;
        } catch (error) {
            console.error('加载执行计划数据失败:', error);
            return [];
        }
    }

    // 保存执行计划数据
    async saveExecutionPlansData(data) {
        try {
            const db = await this.ensureConnection();
            const collection = db.collection('execution_plans');
            
            // 清空集合并插入新数据
            await collection.deleteMany({});
            if (data.length > 0) {
                await collection.insertMany(data);
            }
            
            console.log(`💾 执行计划数据已保存到MongoDB: ${data.length} 条记录`);
            return true;
        } catch (error) {
            console.error('保存执行计划数据失败:', error);
            return false;
        }
    }

    // ==================== 数据迁移和备份 ====================
    
    // 从JSON文件迁移数据到MongoDB
    async migrateFromJSON(jsonDataManager) {
        try {
            console.log('🔄 开始从JSON文件迁移数据到MongoDB...');
            
            // 迁移各种数据
            const salesData = jsonDataManager.loadSalesData();
            await this.saveSalesData(salesData);
            
            const customerData = jsonDataManager.loadCustomerData();
            await this.saveCustomerData(customerData);
            
            const organizationData = jsonDataManager.loadOrganizationData();
            await this.saveOrganizationData(organizationData);
            
            const businessManagersData = jsonDataManager.loadBusinessManagersData();
            await this.saveBusinessManagersData(businessManagersData);
            
            const paymentRecordsData = jsonDataManager.loadPaymentRecordsData();
            await this.savePaymentRecordsData(paymentRecordsData);
            
            const qaData = jsonDataManager.loadQAData();
            await this.saveQAData(qaData);
            
            const executionPlansData = jsonDataManager.loadExecutionPlansData();
            await this.saveExecutionPlansData(executionPlansData);
            
            console.log('✅ 数据迁移完成！');
            return true;
        } catch (error) {
            console.error('❌ 数据迁移失败:', error);
            return false;
        }
    }

    // 备份数据（导出到JSON）
    async backupToJSON(jsonDataManager) {
        try {
            console.log('🔄 开始备份MongoDB数据到JSON文件...');
            
            // 从MongoDB加载数据并保存到JSON
            const salesData = await this.loadSalesData();
            jsonDataManager.saveSalesData(salesData);
            
            const customerData = await this.loadCustomerData();
            jsonDataManager.saveCustomerData(customerData);
            
            const organizationData = await this.loadOrganizationData();
            jsonDataManager.saveOrganizationData(organizationData);
            
            const businessManagersData = await this.loadBusinessManagersData();
            jsonDataManager.saveBusinessManagersData(businessManagersData);
            
            const paymentRecordsData = await this.loadPaymentRecordsData();
            jsonDataManager.savePaymentRecordsData(paymentRecordsData);
            
            const qaData = await this.loadQAData();
            jsonDataManager.saveQAData(qaData);
            
            const executionPlansData = await this.loadExecutionPlansData();
            jsonDataManager.saveExecutionPlansData(executionPlansData);
            
            console.log('✅ 数据备份完成！');
            return true;
        } catch (error) {
            console.error('❌ 数据备份失败:', error);
            return false;
        }
    }
}

// 创建全局MongoDB管理器实例
const mongoManager = new MongoManager();

// 优雅关闭处理
process.on('SIGINT', async () => {
    console.log('\n🔄 正在关闭MongoDB连接...');
    await mongoManager.disconnect();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🔄 正在关闭MongoDB连接...');
    await mongoManager.disconnect();
    process.exit(0);
});

module.exports = mongoManager;
