const mongoManager = require('../utils/mongoManager');

async function checkAllInvoiceFields() {
    try {
        console.log('🔍 检查项目数据中所有可能的开票日期字段...');
        
        // 设置生产环境
        process.env.NODE_ENV = 'production';
        
        // 连接生产环境MongoDB
        await mongoManager.connect();
        
        // 加载数据
        const customerData = await mongoManager.loadCustomerData();
        
        console.log(`👥 客户数据: ${Object.keys(customerData).length} 个`);
        
        // 统计所有可能的开票相关字段
        let totalProjects = 0;
        let invoiceFieldStats = {};
        let projectsWithInvoiceData = [];
        
        // 可能的开票字段名称
        const possibleInvoiceFields = [
            'invoiceTime', 'invoiceDate', 'billingTime', 'billingDate',
            'invoicing', 'invoice', '开票时间', '开票日期', 'billing'
        ];
        
        Object.keys(customerData).forEach(customerName => {
            const customer = customerData[customerName];
            if (customer.projects && customer.projects.length > 0) {
                customer.projects.forEach(project => {
                    totalProjects++;
                    
                    // 检查所有字段
                    const projectFields = Object.keys(project);
                    let hasInvoiceField = false;
                    let invoiceFields = [];
                    
                    // 检查每个可能的开票字段
                    possibleInvoiceFields.forEach(field => {
                        if (project[field] !== undefined && project[field] !== null && project[field] !== '') {
                            hasInvoiceField = true;
                            invoiceFields.push({
                                field: field,
                                value: project[field]
                            });
                            
                            if (!invoiceFieldStats[field]) {
                                invoiceFieldStats[field] = 0;
                            }
                            invoiceFieldStats[field]++;
                        }
                    });
                    
                    // 检查所有包含"invoice"或"开票"的字段
                    projectFields.forEach(field => {
                        const fieldLower = field.toLowerCase();
                        if ((fieldLower.includes('invoice') || fieldLower.includes('bill') || field.includes('开票')) 
                            && project[field] !== undefined && project[field] !== null && project[field] !== '') {
                            
                            if (!possibleInvoiceFields.includes(field)) {
                                hasInvoiceField = true;
                                invoiceFields.push({
                                    field: field,
                                    value: project[field]
                                });
                                
                                if (!invoiceFieldStats[field]) {
                                    invoiceFieldStats[field] = 0;
                                }
                                invoiceFieldStats[field]++;
                            }
                        }
                    });
                    
                    if (hasInvoiceField) {
                        projectsWithInvoiceData.push({
                            customer: customerName,
                            project: project.projectName || '未命名',
                            invoiceFields: invoiceFields,
                            allFields: projectFields
                        });
                    }
                    
                    // 显示前几个项目的所有字段（用于调试）
                    if (totalProjects <= 3) {
                        console.log(`\n📋 项目 ${totalProjects}: ${customerName} - ${project.projectName || '未命名'}`);
                        console.log(`   所有字段: ${projectFields.join(', ')}`);
                        
                        // 显示所有字段的值
                        projectFields.forEach(field => {
                            const value = project[field];
                            if (value !== undefined && value !== null && value !== '') {
                                console.log(`     ${field}: ${value}`);
                            }
                        });
                    }
                });
            }
        });
        
        console.log(`\n📊 统计结果:`);
        console.log(`   总项目数: ${totalProjects}`);
        console.log(`   有开票相关字段的项目: ${projectsWithInvoiceData.length}`);
        
        if (Object.keys(invoiceFieldStats).length > 0) {
            console.log(`\n📋 开票字段统计:`);
            Object.keys(invoiceFieldStats).forEach(field => {
                console.log(`   ${field}: ${invoiceFieldStats[field]} 个项目`);
            });
        } else {
            console.log(`\n❌ 没有找到任何开票相关字段`);
        }
        
        if (projectsWithInvoiceData.length > 0) {
            console.log(`\n📋 有开票数据的项目:`);
            
            projectsWithInvoiceData.forEach((proj, index) => {
                console.log(`\n   ${index + 1}. ${proj.customer} - ${proj.project}`);
                proj.invoiceFields.forEach(field => {
                    console.log(`      ${field.field}: ${field.value}`);
                });
            });
        }
        
        // 随机检查几个项目的完整字段结构
        console.log(`\n🔍 随机项目字段结构检查:`);
        let checkedProjects = 0;
        Object.keys(customerData).forEach(customerName => {
            const customer = customerData[customerName];
            if (customer.projects && customer.projects.length > 0 && checkedProjects < 5) {
                customer.projects.forEach(project => {
                    if (checkedProjects < 5) {
                        checkedProjects++;
                        console.log(`\n   项目 ${checkedProjects}: ${customerName} - ${project.projectName || '未命名'}`);
                        
                        // 显示所有非空字段
                        Object.keys(project).forEach(field => {
                            const value = project[field];
                            if (value !== undefined && value !== null && value !== '') {
                                console.log(`     ${field}: ${value}`);
                            }
                        });
                    }
                });
            }
        });
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 检查失败:', error);
        console.error('错误详情:', error.stack);
        process.exit(1);
    }
}

checkAllInvoiceFields();
