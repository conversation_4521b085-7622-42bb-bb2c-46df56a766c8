<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2025年华东区销售漏斗表格</title>
    <link rel="stylesheet" href="css/modal-responsive.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .header-title {
            text-align: left;
        }

        .header-title h1 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
        }

        .header-title .subtitle {
            margin: 0;
            opacity: 0.9;
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .header-title {
                text-align: center;
            }
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .controls {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        select, input, button {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }
        
        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .number {
            text-align: right;
            font-family: 'Courier New', monospace;
        }
        
        .region-tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .region-安徽 { background: #e3f2fd; color: #1976d2; }
        .region-福建 { background: #f3e5f5; color: #7b1fa2; }
        .region-上海 { background: #e8f5e8; color: #388e3c; }
        .region-山东 { background: #fff3e0; color: #f57c00; }
        .region-江苏 { background: #fce4ec; color: #c2185b; }
        .region-浙江 { background: #e0f2f1; color: #00796b; }
        .region-未分类 { background: #f5f5f5; color: #616161; }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            background: white;
        }
        
        .pagination button {
            padding: 0.5rem 1rem;
            background: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .pagination button.active {
            background: #667eea;
            color: white;
        }
        
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-edit {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        /* 销售仪表盘页面弹窗样式将使用通用响应式样式 */
        
        /* 表单样式使用通用响应式样式 */
        
        .monthly-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .monthly-input {
            text-align: center;
        }

        .data-button {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            margin: 0 0.25rem;
        }

        .data-button:hover {
            background: #138496;
        }

        .data-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            /* 使用 Flexbox 布局 */
            align-items: center;
            justify-content: center;
            padding: 1rem;
            box-sizing: border-box;
            font-size: 14px;
        }

        .data-modal-content {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 800px;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            font-size: 14px;
        }

        .data-modal-content h3 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            margin: 0;
            border-radius: 10px 10px 0 0;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .data-modal-content .data-grid {
            padding: 1.5rem;
            overflow-y: auto;
            flex: 1;
        }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .data-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .data-item-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.5rem;
        }

        .data-item-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }

        .data-item-sub {
            font-size: 0.8rem;
            color: #888;
            margin-top: 0.25rem;
        }

        /* 标签页样式 */
        .tabs {
            background: white;
            border-radius: 10px 10px 0 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 0;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 1px solid #eee;
        }

        .tab-button {
            padding: 1rem 2rem;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .tab-button.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: #f8f9fa;
        }

        .tab-button:hover {
            background: #f8f9fa;
            color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 执行计划样式 */
        .execution-plan-card {
            background: white;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .execution-plan-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .execution-plan-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .execution-plan-customer {
            color: #667eea;
            font-weight: 500;
        }

        .execution-plan-deadline {
            background: #fff3cd;
            color: #856404;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .execution-plan-status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #f8d7da;
            color: #721c24;
        }

        .execution-plan-description {
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .execution-plan-effects {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .effect-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }

        .effect-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .effect-content {
            color: #666;
            line-height: 1.5;
        }

        /* 可点击卡片样式 */
        .clickable-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
        }

        .clickable-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .click-hint {
            position: absolute;
            bottom: 0.5rem;
            right: 0.75rem;
            font-size: 0.7rem;
            opacity: 0.7;
            font-style: italic;
        }

        /* 项目详情表格样式 */
        .project-details-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            font-size: 0.9rem;
        }

        .project-details-table th,
        .project-details-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .project-details-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .project-details-table tr:hover {
            background: #f8f9fa;
        }

        /* 项目详情弹窗特殊样式 */
        #projectDetailsModal .modal-body {
            padding: 0;
            max-height: 60vh;
            overflow-y: auto;
        }

        #projectDetailsModal .project-details-table {
            margin-top: 0;
        }

        /* 统计汇总区域样式 */
        .project-summary {
            margin: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 0.9rem;
            position: sticky;
            bottom: 0;
            background: white;
            border-top: 2px solid #e9ecef;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }

        /* 移动设备响应式样式 */
        @media (max-width: 768px) {
            #projectDetailsModal .modal-body {
                max-height: 50vh;
            }

            .project-details-table {
                font-size: 0.8rem;
            }

            .project-details-table th,
            .project-details-table td {
                padding: 0.5rem;
            }

            .project-summary {
                margin: 0.5rem;
                padding: 0.75rem;
                font-size: 0.8rem;
            }
        }

        @media (max-width: 480px) {
            #projectDetailsModal .modal-body {
                max-height: 45vh;
            }

            .project-details-table {
                font-size: 0.75rem;
            }

            .project-details-table th,
            .project-details-table td {
                padding: 0.4rem;
            }
        }

        .project-stage-tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        /* 移动端弹窗优化 */
        @media (max-width: 768px) {
            .data-modal.show {
                display: flex !important;
                padding: 0.5rem !important;
                align-items: flex-start !important;
                padding-top: 2rem !important;
            }

            .data-modal-content {
                width: 100% !important;
                max-width: none !important;
                max-height: calc(100vh - 4rem) !important;
                font-size: 0.8rem !important;
            }

            .data-modal-content h3 {
                padding: 1rem !important;
                font-size: 1rem !important;
            }

            .data-modal-content .data-grid {
                padding: 1rem !important;
            }
        }

        .stage-呈现价值 { background: #e3f2fd; color: #1976d2; }
        .stage-已签约 { background: #e8f5e8; color: #388e3c; }
        .stage-回收货款 { background: #fff3e0; color: #f57c00; }

        .amount-highlight {
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <div class="header-title">
                    <h1>📊 销售仪表板</h1>
                    <p class="subtitle">Sales Dashboard - 数据驱动的销售分析</p>
                    <div id="filterStatus" style="margin-top: 1rem; padding: 0.5rem; background: rgba(255,255,255,0.1); border-radius: 5px; font-size: 0.9rem; display: none;">
                        <span id="filterStatusText"></span>
                        <button onclick="resetFilters()" style="margin-left: 1rem; padding: 0.25rem 0.5rem; background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; border-radius: 3px; cursor: pointer;">清除筛选</button>
                    </div>
                </div>
                <div class="user-info"></div>
            </div>
        </header>
        
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-value" id="totalRecords">-</div>
                <div class="stat-label">总记录数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalPlan">-</div>
                <div class="stat-label">总销售计划(万元)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalPayment">-</div>
                <div class="stat-label">实际销售额(万元)</div>
            </div>
        </div>

        <!-- 详细统计数据 -->
        <div class="stats-grid" id="detailedStatsGrid" style="margin-top: 1rem;">
            <div class="stat-card clickable-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;" onclick="showProjectDetails('weeklyPayment')">
                <div class="stat-value" id="weeklyPayment">-</div>
                <div class="stat-label">近一周项目回款(万元)</div>
                <div class="stat-sub" id="weeklyPaymentCount" style="font-size: 0.8rem; opacity: 0.9; margin-top: 0.25rem;">- 个项目</div>
                <div class="click-hint">点击查看详情</div>
            </div>
            <div class="stat-card clickable-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;" onclick="showProjectDetails('invoicedPending')">
                <div class="stat-value" id="invoicedPending">-</div>
                <div class="stat-label">已开票待回款(万元)</div>
                <div class="stat-sub" id="invoicedPendingCount" style="font-size: 0.8rem; opacity: 0.9; margin-top: 0.25rem;">- 个项目</div>
                <div class="click-hint">点击查看详情</div>
            </div>
            <div class="stat-card clickable-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;" onclick="showProjectDetails('expectedInvoicing')">
                <div class="stat-value" id="expectedInvoicing">-</div>
                <div class="stat-label">近期预计待开票(万元)</div>
                <div class="stat-sub" id="expectedInvoicingCount" style="font-size: 0.8rem; opacity: 0.9; margin-top: 0.25rem;">- 个项目</div>
                <div class="click-hint">点击查看详情</div>
            </div>
            <div class="stat-card clickable-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;" onclick="showProjectDetails('weeklySignedCompleted')">
                <div class="stat-value" id="weeklySignedCompleted">-</div>
                <div class="stat-label">本周已签约完成(万元)</div>
                <div class="stat-sub" id="weeklySignedCompletedCount" style="font-size: 0.8rem; opacity: 0.9; margin-top: 0.25rem;">- 个项目</div>
                <div class="click-hint">点击查看详情</div>
            </div>
            <div class="stat-card clickable-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;" onclick="showProjectDetails('weeklyNegotiationPending')">
                <div class="stat-value" id="weeklyNegotiationPending">-</div>
                <div class="stat-label">本周商谈招标待签(万元)</div>
                <div class="stat-sub" id="weeklyNegotiationPendingCount" style="font-size: 0.8rem; opacity: 0.9; margin-top: 0.25rem;">- 个项目</div>
                <div class="click-hint">点击查看详情</div>
            </div>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>区域筛选:</label>
                <select id="regionFilter">
                    <option value="all">全部区域</option>
                </select>
            </div>
            <div class="control-group">
                <label>商务负责人:</label>
                <select id="businessManagerFilter">
                    <option value="all">全部负责人</option>
                    <!-- 选项将通过JavaScript动态加载 -->
                </select>
            </div>
            <div class="control-group">
                <label>月份筛选:</label>
                <select id="monthFilter">
                    <option value="all">全部月份</option>
                    <option value="jan">1月</option>
                    <option value="feb">2月</option>
                    <option value="mar">3月</option>
                    <option value="apr">4月</option>
                    <option value="may">5月</option>
                    <option value="jun">6月</option>
                    <option value="jul">7月</option>
                    <option value="aug">8月</option>
                    <option value="sep">9月</option>
                    <option value="oct">10月</option>
                    <option value="nov">11月</option>
                    <option value="dec">12月</option>
                </select>
            </div>
            <div class="control-group">
                <label>季度筛选:</label>
                <select id="quarterFilter">
                    <option value="all">全部季度</option>
                    <option value="Q1">第一季度</option>
                    <option value="Q2">第二季度</option>
                    <option value="Q3">第三季度</option>
                    <option value="Q4">第四季度</option>
                </select>
            </div>
            <div class="control-group">
                <label>排序:</label>
                <select id="sortBy">
                    <option value="id">序号</option>
                    <option value="customerName">客户名称</option>
                    <option value="totalPlan">销售计划</option>
                    <option value="actualPayment">实际回款</option>
                    <option value="region">区域</option>
                </select>
                <select id="sortOrder">
                    <option value="asc">升序</option>
                    <option value="desc">降序</option>
                </select>
            </div>
            <div class="control-group">
                <button onclick="resetFilters()">重置筛选</button>
                <button onclick="loadData()">刷新数据</button>
                <button onclick="showAddModal()">添加记录</button>
            </div>
        </div>

        <!-- 标签页导航 -->
        <div class="tabs">
            <div class="tab-buttons">
                <button class="tab-button active" onclick="switchTab('sales', this)">📊 销售记录</button>
                <button class="tab-button" onclick="switchTab('execution-plans', this)">📋 执行计划</button>
            </div>
        </div>

        <!-- 销售记录标签页 -->
        <div id="sales-tab" class="tab-content active">
            <div class="table-container">
            <div id="loading" class="loading">正在加载数据...</div>
            <table id="salesTable" style="display: none;">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>区域</th>
                        <th>客户名称</th>
                        <th>商务负责人</th>
                        <th style="text-align: right;">销售计划(万元)</th>
                        <th style="text-align: right;">实际回款(万元)</th>
                        <th style="text-align: right;">实际销售额(万元)</th>
                        <th>月度数据</th>
                        <th>季度数据</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="salesTableBody">
                </tbody>
            </table>
            
            <div class="pagination" id="pagination" style="display: none;">
                <button onclick="changePage(-1)" id="prevBtn">上一页</button>
                <span id="pageInfo">第 1 页，共 1 页</span>
                <button onclick="changePage(1)" id="nextBtn">下一页</button>
            </div>
            </div>
        </div>

        <!-- 执行计划标签页 -->
        <div id="execution-plans-tab" class="tab-content">
            <div class="table-container">
                <div class="controls" style="border-radius: 0;">
                    <div class="control-group">
                        <label>客户筛选:</label>
                        <select id="planCustomerFilter">
                            <option value="all">全部客户</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>执行状态:</label>
                        <select id="planStatusFilter">
                            <option value="all">全部状态</option>
                            <option value="完成">完成</option>
                            <option value="未完成">未完成</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <button onclick="loadExecutionPlans()">刷新数据</button>
                        <button onclick="showAddPlanModal()">添加执行计划</button>
                    </div>
                </div>

                <div id="planLoading" class="loading">正在加载执行计划...</div>
                <div id="executionPlansContainer" style="display: none; padding: 1rem;">
                    <!-- 执行计划列表将在这里动态生成 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加/编辑模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加销售记录</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="salesForm">
                <div class="form-group">
                    <label>序号:</label>
                    <input type="number" id="editId" min="1" step="1" placeholder="请输入序号" onblur="validateId()">
                    <small id="idValidationMessage" style="color: #666; font-size: 0.8rem;">序号用于排序和标识，建议使用唯一数字</small>
                </div>
                <div class="form-group">
                    <label>区域:</label>
                    <select id="editRegion" required>
                        <option value="">请选择区域</option>
                        <option value="安徽">安徽</option>
                        <option value="福建">福建</option>
                        <option value="上海">上海</option>
                        <option value="山东">山东</option>
                        <option value="江苏">江苏</option>
                        <option value="浙江">浙江</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>客户名称:</label>
                    <input type="text" id="editCustomerName" required>
                    <small style="color: #666; font-size: 0.8rem;">客户的销售数据将从客户详情页面自动计算</small>
                </div>
                <div class="form-group">
                    <label>商务负责人:</label>
                    <select id="editBusinessManager" required>
                        <option value="">请选择商务负责人</option>
                        <!-- 选项将通过JavaScript动态加载 -->
                    </select>
                </div>
                <div class="form-group">
                    <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; border-left: 4px solid #007bff;">
                        <h4 style="margin: 0 0 0.5rem 0; color: #007bff;">📊 数据说明</h4>
                        <p style="margin: 0; font-size: 0.9rem; color: #666;">
                            销售记录表格中显示的所有销售数据（实际回款、实际销售额、月度计划等）均从各客户详情表中的项目数据自动汇总计算，无需手动输入。
                        </p>
                        <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem; color: #666;">
                            要修改销售数据，请前往对应的<strong>客户详情页面</strong>编辑具体项目信息。
                        </p>
                    </div>
                </div>
                </form>
            </div>
            <div class="form-actions">
                <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
                <button type="submit" class="btn-primary" form="salesForm">保存</button>
            </div>
        </div>
    </div>

    <!-- 月度数据弹窗 -->
    <div id="monthlyDataModal" class="data-modal">
        <div class="data-modal-content">
            <h3 id="monthlyModalTitle">月度数据详情</h3>
            <div id="monthlyDataGrid" class="data-grid">
                <!-- 月度数据将在这里动态生成 -->
            </div>
            <div class="form-actions">
                <button type="button" class="btn-primary" onclick="closeDataModal('monthlyDataModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 季度数据弹窗 -->
    <div id="quarterlyDataModal" class="data-modal">
        <div class="data-modal-content">
            <h3 id="quarterlyModalTitle">季度数据详情</h3>
            <div id="quarterlyDataGrid" class="data-grid">
                <!-- 季度数据将在这里动态生成 -->
            </div>
            <div class="form-actions">
                <button type="button" class="btn-primary" onclick="closeDataModal('quarterlyDataModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 执行计划添加/编辑模态框 -->
    <div id="planModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="planModalTitle">添加执行计划</h3>
                <button class="modal-close" onclick="closePlanModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="planForm">
                <div class="form-group">
                    <label>客户名称:</label>
                    <select id="planCustomerName" required>
                        <option value="">请选择客户</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>截止时间:</label>
                    <input type="date" id="planDeadline" required>
                </div>
                <div class="form-group">
                    <label>计划说明:</label>
                    <textarea id="planDescription" rows="4" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px; resize: vertical;" placeholder="请详细描述执行计划的内容和目标" required></textarea>
                </div>
                <div class="form-group">
                    <label>预期效果:</label>
                    <textarea id="planExpectedEffect" rows="3" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px; resize: vertical;" placeholder="请描述预期达到的效果和目标" required></textarea>
                </div>
                <div class="form-group">
                    <label>实际效果:</label>
                    <textarea id="planActualEffect" rows="3" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px; resize: vertical;" placeholder="请描述实际达到的效果（可选）"></textarea>
                </div>
                <div class="form-group">
                    <label>执行状态:</label>
                    <select id="planExecutionStatus" required>
                        <option value="未完成">未完成</option>
                        <option value="完成">完成</option>
                    </select>
                </div>
                </form>
            </div>
            <div class="form-actions">
                <button type="button" class="btn-secondary" onclick="closePlanModal()">取消</button>
                <button type="submit" class="btn-primary" form="planForm">保存</button>
            </div>
        </div>
    </div>

    <!-- 项目详情弹窗 -->
    <div id="projectDetailsModal" class="modal">
        <div class="modal-content" style="max-width: 1000px;">
            <div class="modal-header">
                <h3 id="projectDetailsTitle">项目详情</h3>
                <button class="modal-close" onclick="closeProjectDetailsModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="projectDetailsContent">
                    <!-- 项目详情将在这里动态生成 -->
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn-primary" onclick="closeProjectDetailsModal()">关闭</button>
            </div>
        </div>
    </div>

    <script src="navbar.js"></script>
    <script src="js/auth.js"></script>
    <script src="sales-dashboard.js"></script>
</body>
</html>
