<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Q&A知识库 - 销售管理系统</title>
    <link rel="stylesheet" href="css/modal-responsive.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .controls {
            padding: 2rem;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .search-box input:focus {
            outline: none;
            border-color: #667eea;
        }

        .filter-group {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .filter-group select {
            padding: 0.75rem 1rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-edit {
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .content {
            padding: 2rem;
        }

        .qa-list {
            display: grid;
            gap: 1.5rem;
        }

        .qa-card {
            background: white;
            border: 1px solid #eee;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .qa-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .qa-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .qa-question {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            flex: 1;
        }

        .qa-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 1rem;
        }

        .qa-category {
            background: #e3f2fd;
            color: #1976d2;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .qa-answer {
            color: #555;
            line-height: 1.6;
            margin-bottom: 1rem;
            white-space: pre-wrap;
        }

        .qa-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .qa-tag {
            background: #f8f9fa;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            border: 1px solid #dee2e6;
        }

        .qa-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }

        .qa-stats {
            display: flex;
            gap: 1rem;
            font-size: 0.9rem;
            color: #666;
        }

        .qa-actions {
            display: flex;
            gap: 0.5rem;
        }

        .rating-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .rating-btn {
            padding: 0.25rem 0.5rem;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s;
        }

        .rating-btn:hover {
            background: #f8f9fa;
        }

        .rating-btn.helpful {
            color: #28a745;
            border-color: #28a745;
        }

        .rating-btn.not-helpful {
            color: #dc3545;
            border-color: #dc3545;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: #333;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal h3 {
            margin-bottom: 1.5rem;
            color: #333;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                min-width: auto;
            }

            .filter-group {
                justify-content: space-between;
            }

            .qa-header {
                flex-direction: column;
                gap: 1rem;
            }

            .qa-footer {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 Q&A知识库</h1>
            <p>常见问题与解答，快速找到您需要的答案</p>
        </div>

        <div class="controls">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索问题、答案或标签...">
            </div>
            <div class="filter-group">
                <select id="categoryFilter">
                    <option value="all">全部分类</option>
                </select>
                <select id="sortBy">
                    <option value="createdAt">按创建时间</option>
                    <option value="updatedAt">按更新时间</option>
                    <option value="viewCount">按浏览次数</option>
                    <option value="helpful">按有用评价</option>
                </select>
                <button class="btn btn-primary" onclick="showAddModal()">添加Q&A</button>
            </div>
        </div>

        <div class="content">
            <div id="loading" class="loading">正在加载Q&A数据...</div>
            <div id="qaList" class="qa-list" style="display: none;"></div>
            <div id="emptyState" class="empty-state" style="display: none;">
                <h3>暂无Q&A数据</h3>
                <p>点击"添加Q&A"按钮创建第一个问答</p>
            </div>
        </div>
    </div>

    <!-- Q&A添加/编辑模态框 -->
    <div id="qaModal" class="modal">
        <div class="modal-content">
            <h3 id="modalTitle">添加Q&A</h3>
            <form id="qaForm">
                <div class="form-group">
                    <label>问题 *</label>
                    <textarea id="questionInput" placeholder="请输入问题..." required></textarea>
                </div>
                <div class="form-group">
                    <label>答案 *</label>
                    <textarea id="answerInput" placeholder="请输入答案..." required></textarea>
                </div>
                <div class="form-group">
                    <label>分类</label>
                    <input type="text" id="categoryInput" placeholder="如：常见问题、技术支持等">
                </div>
                <div class="form-group">
                    <label>标签</label>
                    <input type="text" id="tagsInput" placeholder="多个标签用逗号分隔">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="navbar.js"></script>
    <script src="qa.js"></script>
</body>
</html>
