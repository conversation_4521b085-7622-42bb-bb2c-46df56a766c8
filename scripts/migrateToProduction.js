// 生产环境数据迁移脚本
const mongoManager = require('../utils/mongoManager');

async function migrateToProduction() {
    console.log('🚀 生产环境数据迁移');
    console.log('=' .repeat(60));

    try {
        // 确保使用生产环境配置
        process.env.NODE_ENV = 'production';
        
        // 连接到生产环境MongoDB
        await mongoManager.connect();
        console.log('✅ 生产环境MongoDB连接成功');

        // 检查现有商务经理数据
        console.log('\n📊 检查现有商务经理数据...');
        const existingManagers = await mongoManager.loadBusinessManagersData();
        console.log(`   现有商务经理数量: ${existingManagers.length}`);

        if (existingManagers.length > 0) {
            console.log('   现有商务经理:');
            existingManagers.forEach((manager, index) => {
                console.log(`     ${index + 1}. ${manager.name} (${manager.username})`);
            });
        }

        // 创建标准商务经理数据
        const businessManagers = [
            {
                id: 0,
                name: "系统管理员",
                username: "admin",
                password: "admin123",
                permission: "admin",
                customers: []
            },
            {
                id: 1,
                name: "王伟",
                username: "wang_wei",
                password: "123456",
                permission: "manager",
                customers: [
                    "华安证券", "国元证券", "东亚前海", "南京证券", "西部证券", 
                    "国金证券", "国泰君安", "华福证券", "兴业证券", "华创证券", 
                    "中银证券", "招商证券"
                ]
            },
            {
                id: 2,
                name: "崔治灿",
                username: "cui_zhican",
                password: "123456",
                permission: "manager",
                customers: [
                    "华福证券", "兴业证券", "东亚前海", "南京证券", 
                    "西部证券", "国金证券", "国泰君安"
                ]
            },
            {
                id: 3,
                name: "张前锋",
                username: "zhang_qianfeng",
                password: "123456",
                permission: "manager",
                customers: [
                    "华创证券", "中银证券", "招商证券", "光大证券"
                ]
            }
        ];

        console.log('\n📝 准备迁移的商务经理数据:');
        businessManagers.forEach((manager, index) => {
            console.log(`   ${index + 1}. ${manager.name} (${manager.username})`);
            console.log(`      权限: ${manager.permission}`);
            console.log(`      负责客户: ${manager.customers.length} 个`);
            if (manager.customers.length > 0) {
                console.log(`      客户列表: ${manager.customers.slice(0, 3).join(', ')}${manager.customers.length > 3 ? '...' : ''}`);
            }
            console.log('');
        });

        // 确认迁移
        console.log('⚠️  注意: 这将覆盖生产环境中的现有商务经理数据！');
        console.log('');

        // 执行迁移
        console.log('🔄 开始迁移商务经理数据到生产环境...');
        const success = await mongoManager.saveBusinessManagersData(businessManagers);

        if (success) {
            console.log('✅ 商务经理数据迁移成功！');
            
            // 验证迁移结果
            console.log('\n🔍 验证迁移结果...');
            const verifyManagers = await mongoManager.loadBusinessManagersData();
            console.log(`✅ 验证成功: ${verifyManagers.length} 个商务经理账号已创建`);
            
            verifyManagers.forEach((manager, index) => {
                console.log(`   ${index + 1}. ${manager.name} (${manager.username}) - ${manager.permission}`);
            });

            // 测试登录功能
            console.log('\n🧪 测试登录功能...');
            
            const testAccounts = [
                { username: 'admin', password: 'admin123', name: '系统管理员' },
                { username: 'wang_wei', password: '123456', name: '王伟' },
                { username: 'cui_zhican', password: '123456', name: '崔治灿' },
                { username: 'zhang_qianfeng', password: '123456', name: '张前锋' }
            ];

            for (const account of testAccounts) {
                try {
                    const result = await mongoManager.authenticateBusinessManager(account.username, account.password);
                    if (result) {
                        console.log(`✅ ${account.name} (${account.username}) - 登录测试成功`);
                    } else {
                        console.log(`❌ ${account.name} (${account.username}) - 登录测试失败`);
                    }
                } catch (error) {
                    console.log(`❌ ${account.name} (${account.username}) - 登录测试错误: ${error.message}`);
                }
            }

        } else {
            throw new Error('商务经理数据迁移失败');
        }

        console.log('\n' + '=' .repeat(60));
        console.log('🎉 生产环境数据迁移完成！');
        
        console.log('\n📋 迁移总结:');
        console.log(`✅ 成功迁移 ${businessManagers.length} 个商务经理账号`);
        console.log('✅ 所有账号登录功能正常');
        console.log('✅ 权限配置正确');
        console.log('✅ 客户分配完整');

        console.log('\n🔧 后续步骤:');
        console.log('1. 重启生产环境服务器');
        console.log('2. 测试所有用户登录功能');
        console.log('3. 验证权限控制是否正常');
        console.log('4. 检查数据访问是否正确');

    } catch (error) {
        console.error('❌ 迁移失败:', error.message);
        console.error(error.stack);
        
        console.error('\n💡 故障排除建议:');
        console.error('1. 检查生产环境MongoDB连接');
        console.error('2. 验证数据库权限');
        console.error('3. 确认网络连接正常');
        console.error('4. 检查环境变量配置');
        
        process.exit(1);
    } finally {
        // 关闭MongoDB连接
        await mongoManager.disconnect();
    }
}

// 运行迁移
if (require.main === module) {
    migrateToProduction();
}

module.exports = { migrateToProduction };
