{"name": "sales-funnel-app", "version": "1.0.0", "description": "Sales funnel management application", "main": "app.js", "scripts": {"start": "NODE_ENV=development node app.js", "start:prod": "NODE_ENV=production node app.js", "dev": "NODE_ENV=development nodemon app.js", "dev:prod": "NODE_ENV=production nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "sales", "funnel", "management"], "author": "", "license": "ISC", "dependencies": {"dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "node-fetch": "^2.7.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.10"}}