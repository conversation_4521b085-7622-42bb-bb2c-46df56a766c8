const mongoManager = require('../utils/mongoManager');

async function checkHaitongData() {
    try {
        console.log('🔍 检查证券通数据（生产环境）...');

        // 设置生产环境
        process.env.NODE_ENV = 'production';

        // 连接MongoDB
        await mongoManager.connect();
        
        // 加载销售数据
        const salesData = await mongoManager.loadSalesData();
        console.log(`📊 MongoDB中共有 ${salesData.length} 条销售数据`);
        
        // 查找证券通
        const zhengquantongRecords = salesData.filter(record => record.customerName === '证券通');

        if (zhengquantongRecords.length > 0) {
            console.log('❌ MongoDB中仍然存在证券通记录:');
            zhengquantongRecords.forEach(record => {
                console.log(`   ID: ${record.id}, 区域: ${record.region}, 商务经理: ${record.businessManager}`);
            });
        } else {
            console.log('✅ MongoDB中没有找到证券通记录');
        }

        // 检查客户详细数据
        const customerData = await mongoManager.loadCustomerData();
        if (customerData['证券通']) {
            console.log('❌ MongoDB中仍然存在证券通的客户详细数据');
        } else {
            console.log('✅ MongoDB中没有找到证券通的客户详细数据');
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 检查失败:', error);
        process.exit(1);
    }
}

checkHaitongData();
