// 全局变量
let currentCustomer = null;
let organizationData = {};
let customers = [];

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查认证状态
    if (!authManager.isAuthenticated()) {
        authManager.redirectToLogin();
        return;
    }

    loadCustomers();
});

// 加载客户列表
async function loadCustomers() {
    try {
        const response = await authManager.authenticatedFetch('/api/customers');
        const result = await response.json();

        if (result.success) {
            customers = result.data;
            populateCustomerSelect();
        } else {
            console.error('加载客户列表失败:', result.message);
        }
    } catch (error) {
        console.error('加载客户列表失败:', error);

        // 如果是认证错误，跳转到登录页面
        if (error.message.includes('登录已过期') || error.message.includes('用户未登录')) {
            authManager.redirectToLogin();
            return;
        }
    }
}

// 填充客户选择器
function populateCustomerSelect() {
    const select = document.getElementById('customerSelect');
    select.innerHTML = '<option value="">请选择客户...</option>';
    
    customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.customerName;
        option.textContent = `${customer.customerName} (${customer.region})`;
        select.appendChild(option);
    });
}

// 加载客户组织架构
async function loadCustomerOrganization() {
    const select = document.getElementById('customerSelect');
    const customerName = select.value;
    
    if (!customerName) {
        showEmptyState();
        return;
    }
    
    currentCustomer = customerName;
    
    try {
        const response = await authManager.authenticatedFetch(`/api/organizations/${encodeURIComponent(customerName)}`);

        if (response.status === 404) {
            // 客户组织架构不存在，显示空状态并允许创建
            showEmptyOrganization();
            return;
        }

        const result = await response.json();

        if (result.success) {
            organizationData = result.data;
            renderOrganization();
        } else {
            console.error('加载组织架构失败:', result.message);
            showEmptyOrganization();
        }
    } catch (error) {
        console.error('加载组织架构失败:', error);

        // 如果是权限错误，显示提示
        if (error.message.includes('权限')) {
            alert('您没有权限访问该客户的组织架构信息');
            return;
        }

        showEmptyOrganization();
    }
}

// 显示空状态
function showEmptyState() {
    const orgContent = document.getElementById('orgContent');
    const orgTitle = document.getElementById('orgTitle');
    const addBtn = document.getElementById('addRootDeptBtn');
    
    orgTitle.textContent = '组织架构';
    addBtn.style.display = 'none';
    
    orgContent.innerHTML = `
        <div class="empty-state">
            <div class="empty-icon">🏢</div>
            <div class="empty-text">请选择客户查看组织架构</div>
            <div class="empty-subtext">选择客户后可以管理其组织架构和部门信息</div>
        </div>
    `;
}

// 显示空组织架构（可以添加根部门）
function showEmptyOrganization() {
    const orgContent = document.getElementById('orgContent');
    const orgTitle = document.getElementById('orgTitle');
    const addBtn = document.getElementById('addRootDeptBtn');
    
    orgTitle.textContent = `${currentCustomer} - 组织架构`;
    addBtn.style.display = 'block';
    
    orgContent.innerHTML = `
        <div class="empty-state">
            <div class="empty-icon">🏗️</div>
            <div class="empty-text">该客户暂无组织架构</div>
            <div class="empty-subtext">点击"添加根部门"开始创建组织架构</div>
        </div>
    `;
}

// 渲染组织架构
function renderOrganization() {
    const orgContent = document.getElementById('orgContent');
    const orgTitle = document.getElementById('orgTitle');
    const addBtn = document.getElementById('addRootDeptBtn');
    
    orgTitle.textContent = `${currentCustomer} - 组织架构`;
    addBtn.style.display = 'block';
    
    if (!organizationData.departments || organizationData.departments.length === 0) {
        showEmptyOrganization();
        return;
    }
    
    const treeHtml = renderDepartmentTree(organizationData.departments);
    orgContent.innerHTML = `<div class="org-tree">${treeHtml}</div>`;
}

// 渲染部门树
function renderDepartmentTree(departments, level = 0) {
    if (!departments || departments.length === 0) {
        return '';
    }
    
    return departments.map(dept => {
        const hasChildren = dept.children && dept.children.length > 0;
        const childrenHtml = hasChildren ? renderDepartmentTree(dept.children, level + 1) : '';
        
        const employees = dept.employees || [];
        const employeesHtml = employees.length > 0 ? `
            <div class="employees-section">
                ${employees.slice(0, 3).map(emp => `
                    <div class="employee-item" onclick="showEmployeeDetail('${dept.id}', '${emp.id}')" style="cursor: pointer;" title="点击查看详情">
                        <div class="employee-avatar">${emp.name.charAt(0)}</div>
                        <div class="employee-info">
                            <div class="employee-name">${emp.name}</div>
                            <div class="employee-position">${emp.position} • ${emp.gender}</div>
                        </div>
                    </div>
                `).join('')}
                ${employees.length > 3 ? `<div style="text-align: center; color: #666; font-size: 0.8rem; padding: 0.5rem;">还有 ${employees.length - 3} 人...</div>` : ''}
            </div>
        ` : '';

        return `
            <div class="tree-node">
                <div class="node-content">
                    <span class="node-toggle" onclick="toggleNode(this)" style="visibility: ${hasChildren || employees.length > 0 ? 'visible' : 'hidden'}">
                        ${hasChildren || employees.length > 0 ? '▼' : ''}
                    </span>
                    <span class="node-icon">${getDepartmentIcon(dept.type)}</span>
                    <div class="node-info">
                        <div class="node-name">${dept.name}</div>
                        <div class="node-details">
                            ${dept.type} ${dept.manager ? `• 负责人: ${dept.manager}` : ''} ${employees.length ? `• ${employees.length}人` : ''}
                        </div>
                    </div>
                    <div class="node-actions">
                        <button class="action-btn btn-employees" onclick="showEmployeesModal('${dept.id}')">人员管理</button>
                        <button class="action-btn btn-add" onclick="showAddDepartmentModal('${dept.id}')">添加子部门</button>
                        <button class="action-btn btn-edit" onclick="showEditDepartmentModal('${dept.id}')">编辑</button>
                        <button class="action-btn btn-delete" onclick="deleteDepartment('${dept.id}')">删除</button>
                    </div>
                </div>
                ${employeesHtml}
                ${hasChildren ? `<div class="tree-children">${childrenHtml}</div>` : ''}
            </div>
        `;
    }).join('');
}

// 获取部门图标
function getDepartmentIcon(type) {
    const icons = {
        '总部': '🏢',
        '分公司': '🏬',
        '事业部': '🏭',
        '部门': '📁',
        '小组': '👥',
        '其他': '📋'
    };
    return icons[type] || '📁';
}

// 切换节点展开/折叠
function toggleNode(toggleElement) {
    const nodeContent = toggleElement.parentElement;
    const treeNode = nodeContent.parentElement;
    const treeChildren = treeNode.querySelector('.tree-children');
    const employeesSection = treeNode.querySelector('.employees-section');

    let hasCollapsibleContent = false;

    if (treeChildren) {
        treeChildren.classList.toggle('collapsed');
        hasCollapsibleContent = true;
    }

    if (employeesSection) {
        employeesSection.classList.toggle('collapsed');
        hasCollapsibleContent = true;
    }

    if (hasCollapsibleContent) {
        const isCollapsed = (treeChildren && treeChildren.classList.contains('collapsed')) ||
                          (employeesSection && employeesSection.classList.contains('collapsed'));
        toggleElement.textContent = isCollapsed ? '▶' : '▼';
    }
}

// 显示添加部门模态框
function showAddDepartmentModal(parentId = null) {
    const isRoot = !parentId;
    const title = isRoot ? '添加根部门' : '添加子部门';
    
    const modalHtml = `
        <div class="modal-overlay" onclick="closeModal()">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="closeModal()">✕</button>
                </div>
                <div class="modal-body">
                    <form id="departmentForm" onsubmit="saveDepartment(event, '${parentId || ''}')">
                        <div class="form-group">
                            <label>部门名称 *</label>
                            <input type="text" id="deptName" required placeholder="请输入部门名称">
                        </div>
                        <div class="form-group">
                            <label>部门类型 *</label>
                            <select id="deptType" required>
                                <option value="">请选择类型</option>
                                <option value="总部">总部</option>
                                <option value="分公司">分公司</option>
                                <option value="事业部">事业部</option>
                                <option value="部门">部门</option>
                                <option value="小组">小组</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>负责人</label>
                            <input type="text" id="deptManager" placeholder="请输入负责人姓名">
                        </div>
                        <div class="form-group">
                            <label>人员数量</label>
                            <input type="number" id="deptEmployeeCount" min="0" placeholder="请输入人员数量">
                        </div>
                        <div class="form-group">
                            <label>部门描述</label>
                            <textarea id="deptDescription" rows="3" placeholder="请输入部门描述"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" onclick="closeModal()">取消</button>
                            <button type="submit">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = document.querySelector('.modal-overlay:last-child');
    if (modal) {
        modal.classList.add('show');
    }
}

// 显示编辑部门模态框
function showEditDepartmentModal(deptId) {
    const dept = findDepartmentById(organizationData.departments, deptId);
    if (!dept) {
        alert('部门不存在');
        return;
    }
    
    const modalHtml = `
        <div class="modal-overlay" onclick="closeModal()">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h3>编辑部门</h3>
                    <button class="modal-close" onclick="closeModal()">✕</button>
                </div>
                <div class="modal-body">
                    <form id="departmentForm" onsubmit="updateDepartment(event, '${deptId}')">
                        <div class="form-group">
                            <label>部门名称 *</label>
                            <input type="text" id="deptName" required value="${dept.name}">
                        </div>
                        <div class="form-group">
                            <label>部门类型 *</label>
                            <select id="deptType" required>
                                <option value="">请选择类型</option>
                                <option value="总部" ${dept.type === '总部' ? 'selected' : ''}>总部</option>
                                <option value="分公司" ${dept.type === '分公司' ? 'selected' : ''}>分公司</option>
                                <option value="事业部" ${dept.type === '事业部' ? 'selected' : ''}>事业部</option>
                                <option value="部门" ${dept.type === '部门' ? 'selected' : ''}>部门</option>
                                <option value="小组" ${dept.type === '小组' ? 'selected' : ''}>小组</option>
                                <option value="其他" ${dept.type === '其他' ? 'selected' : ''}>其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>负责人</label>
                            <input type="text" id="deptManager" value="${dept.manager || ''}">
                        </div>
                        <div class="form-group">
                            <label>人员数量</label>
                            <input type="number" id="deptEmployeeCount" min="0" value="${dept.employeeCount || ''}">
                        </div>
                        <div class="form-group">
                            <label>部门描述</label>
                            <textarea id="deptDescription" rows="3">${dept.description || ''}</textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" onclick="closeModal()">取消</button>
                            <button type="submit">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = document.querySelector('.modal-overlay:last-child');
    if (modal) {
        modal.classList.add('show');
    }
}

// 关闭模态框
function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// 保存部门
async function saveDepartment(event, parentId) {
    event.preventDefault();
    
    const formData = {
        name: document.getElementById('deptName').value,
        type: document.getElementById('deptType').value,
        manager: document.getElementById('deptManager').value,
        employeeCount: parseInt(document.getElementById('deptEmployeeCount').value) || 0,
        description: document.getElementById('deptDescription').value,
        parentId: parentId || null
    };
    
    try {
        const response = await authManager.authenticatedFetch(`/api/organizations/${encodeURIComponent(currentCustomer)}/departments`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            closeModal();
            loadCustomerOrganization(); // 重新加载组织架构
            alert('部门添加成功');
        } else {
            alert('添加失败: ' + result.message);
        }
    } catch (error) {
        console.error('添加部门失败:', error);
        alert('添加失败: ' + error.message);
    }
}

// 更新部门
async function updateDepartment(event, deptId) {
    event.preventDefault();

    const formData = {
        name: document.getElementById('deptName').value,
        type: document.getElementById('deptType').value,
        manager: document.getElementById('deptManager').value,
        employeeCount: parseInt(document.getElementById('deptEmployeeCount').value) || 0,
        description: document.getElementById('deptDescription').value
    };

    try {
        const response = await authManager.authenticatedFetch(`/api/organizations/${encodeURIComponent(currentCustomer)}/departments/${deptId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            closeModal();
            loadCustomerOrganization(); // 重新加载组织架构
            alert('部门更新成功');
        } else {
            alert('更新失败: ' + result.message);
        }
    } catch (error) {
        console.error('更新部门失败:', error);
        alert('更新失败: ' + error.message);
    }
}

// 删除部门
async function deleteDepartment(deptId) {
    const dept = findDepartmentById(organizationData.departments, deptId);
    if (!dept) {
        alert('部门不存在');
        return;
    }

    const hasChildren = dept.children && dept.children.length > 0;
    const confirmMessage = hasChildren
        ? `确定要删除部门"${dept.name}"吗？\n注意：删除后其所有子部门也将被删除！`
        : `确定要删除部门"${dept.name}"吗？`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        const response = await authManager.authenticatedFetch(`/api/organizations/${encodeURIComponent(currentCustomer)}/departments/${deptId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            loadCustomerOrganization(); // 重新加载组织架构
            alert('部门删除成功');
        } else {
            alert('删除失败: ' + result.message);
        }
    } catch (error) {
        console.error('删除部门失败:', error);
        alert('删除失败: ' + error.message);
    }
}

// ==================== 人员管理功能 ====================

// 显示人员管理模态框
function showEmployeesModal(deptId) {
    const dept = findDepartmentById(organizationData.departments, deptId);
    if (!dept) {
        alert('部门不存在');
        return;
    }

    const employees = dept.employees || [];

    const modalHtml = `
        <div class="modal-overlay" onclick="closeModal()">
            <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 800px;">
                <div class="modal-header">
                    <h3>${dept.name} - 人员管理</h3>
                    <button class="modal-close" onclick="closeModal()">✕</button>
                </div>
                <div class="modal-body">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <h4>部门人员列表 (${employees.length}人)</h4>
                        <button class="action-btn btn-add" onclick="showAddEmployeeModal('${deptId}')">➕ 添加人员</button>
                    </div>
                    <div id="employeesList">
                        ${renderEmployeesList(employees, deptId)}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = document.querySelector('.modal-overlay:last-child');
    if (modal) {
        modal.classList.add('show');
    }
}

// 渲染人员列表
function renderEmployeesList(employees, deptId) {
    if (!employees || employees.length === 0) {
        return `
            <div class="empty-state" style="padding: 2rem; text-align: center;">
                <div style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;">👥</div>
                <div style="color: #666;">该部门暂无人员</div>
                <div style="color: #999; font-size: 0.9rem; margin-top: 0.5rem;">点击"添加人员"开始管理部门人员</div>
            </div>
        `;
    }

    return employees.map(employee => `
        <div class="employee-item">
            <div class="employee-avatar">
                ${employee.name.charAt(0)}
            </div>
            <div class="employee-info">
                <div class="employee-name">${employee.name}</div>
                <div class="employee-position">${employee.position} • ${employee.gender}</div>
            </div>
            <div class="employee-actions">
                <button class="action-btn btn-edit" onclick="showEditEmployeeModal('${deptId}', '${employee.id}')">编辑</button>
                <button class="action-btn btn-delete" onclick="deleteEmployee('${deptId}', '${employee.id}')">删除</button>
            </div>
        </div>
    `).join('');
}

// 显示添加人员模态框
function showAddEmployeeModal(deptId) {
    const dept = findDepartmentById(organizationData.departments, deptId);
    if (!dept) {
        alert('部门不存在');
        return;
    }

    const modalHtml = `
        <div class="modal-overlay" onclick="closeModal()">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h3>添加人员 - ${dept.name}</h3>
                    <button class="modal-close" onclick="closeModal()">✕</button>
                </div>
                <div class="modal-body">
                    <form id="employeeForm" onsubmit="saveEmployee(event, '${deptId}')">
                        <div class="form-group">
                            <label>姓名 *</label>
                            <input type="text" id="empName" required placeholder="请输入姓名">
                        </div>
                        <div class="form-group">
                            <label>性别 *</label>
                            <div class="gender-options">
                                <div class="gender-option">
                                    <input type="radio" id="genderMale" name="gender" value="男" required>
                                    <label for="genderMale">男</label>
                                </div>
                                <div class="gender-option">
                                    <input type="radio" id="genderFemale" name="gender" value="女" required>
                                    <label for="genderFemale">女</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>职位 *</label>
                            <input type="text" id="empPosition" required placeholder="请输入职位">
                        </div>
                        <div class="form-group">
                            <label>描述</label>
                            <div class="rich-text-editor">
                                <div class="editor-toolbar">
                                    <button type="button" class="editor-btn" onclick="formatText('bold')"><b>B</b></button>
                                    <button type="button" class="editor-btn" onclick="formatText('italic')"><i>I</i></button>
                                    <button type="button" class="editor-btn" onclick="formatText('underline')"><u>U</u></button>
                                    <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')">• 列表</button>
                                    <button type="button" class="editor-btn" onclick="formatText('insertOrderedList')">1. 列表</button>
                                </div>
                                <div class="editor-content" id="empDescription" contenteditable="true" placeholder="请输入人员描述信息..."></div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="button" onclick="closeModal()">取消</button>
                            <button type="submit">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = document.querySelector('.modal-overlay:last-child');
    if (modal) {
        modal.classList.add('show');
    }
}

// 富文本编辑器格式化功能
function formatText(command) {
    document.execCommand(command, false, null);
    document.getElementById('empDescription').focus();
}

// 根据ID查找部门
function findDepartmentById(departments, id) {
    for (const dept of departments) {
        if (dept.id === id) {
            return dept;
        }
        if (dept.children) {
            const found = findDepartmentById(dept.children, id);
            if (found) return found;
        }
    }
    return null;
}

// 保存人员
async function saveEmployee(event, deptId) {
    event.preventDefault();

    const formData = {
        name: document.getElementById('empName').value,
        gender: document.querySelector('input[name="gender"]:checked').value,
        position: document.getElementById('empPosition').value,
        description: document.getElementById('empDescription').innerHTML
    };

    try {
        const response = await authManager.authenticatedFetch(`/api/organizations/${encodeURIComponent(currentCustomer)}/departments/${deptId}/employees`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            closeModal();
            loadCustomerOrganization(); // 重新加载组织架构
            alert('人员添加成功');
        } else {
            alert('添加失败: ' + result.message);
        }
    } catch (error) {
        console.error('添加人员失败:', error);
        alert('添加失败: ' + error.message);
    }
}

// 显示编辑人员模态框
function showEditEmployeeModal(deptId, empId) {
    const dept = findDepartmentById(organizationData.departments, deptId);
    if (!dept) {
        alert('部门不存在');
        return;
    }

    const employee = dept.employees.find(emp => emp.id === empId);
    if (!employee) {
        alert('人员不存在');
        return;
    }

    const modalHtml = `
        <div class="modal-overlay" onclick="closeModal()">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h3>编辑人员 - ${employee.name}</h3>
                    <button class="modal-close" onclick="closeModal()">✕</button>
                </div>
                <div class="modal-body">
                    <form id="employeeForm" onsubmit="updateEmployee(event, '${deptId}', '${empId}')">
                        <div class="form-group">
                            <label>姓名 *</label>
                            <input type="text" id="empName" required value="${employee.name}">
                        </div>
                        <div class="form-group">
                            <label>性别 *</label>
                            <div class="gender-options">
                                <div class="gender-option">
                                    <input type="radio" id="genderMale" name="gender" value="男" ${employee.gender === '男' ? 'checked' : ''} required>
                                    <label for="genderMale">男</label>
                                </div>
                                <div class="gender-option">
                                    <input type="radio" id="genderFemale" name="gender" value="女" ${employee.gender === '女' ? 'checked' : ''} required>
                                    <label for="genderFemale">女</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>职位 *</label>
                            <input type="text" id="empPosition" required value="${employee.position}">
                        </div>
                        <div class="form-group">
                            <label>描述</label>
                            <div class="rich-text-editor">
                                <div class="editor-toolbar">
                                    <button type="button" class="editor-btn" onclick="formatText('bold')"><b>B</b></button>
                                    <button type="button" class="editor-btn" onclick="formatText('italic')"><i>I</i></button>
                                    <button type="button" class="editor-btn" onclick="formatText('underline')"><u>U</u></button>
                                    <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')">• 列表</button>
                                    <button type="button" class="editor-btn" onclick="formatText('insertOrderedList')">1. 列表</button>
                                </div>
                                <div class="editor-content" id="empDescription" contenteditable="true">${employee.description || ''}</div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="button" onclick="closeModal()">取消</button>
                            <button type="submit">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = document.querySelector('.modal-overlay:last-child');
    if (modal) {
        modal.classList.add('show');
    }
}

// 更新人员
async function updateEmployee(event, deptId, empId) {
    event.preventDefault();

    const formData = {
        name: document.getElementById('empName').value,
        gender: document.querySelector('input[name="gender"]:checked').value,
        position: document.getElementById('empPosition').value,
        description: document.getElementById('empDescription').innerHTML
    };

    try {
        const response = await authManager.authenticatedFetch(`/api/organizations/${encodeURIComponent(currentCustomer)}/departments/${deptId}/employees/${empId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            closeModal();
            loadCustomerOrganization(); // 重新加载组织架构
            alert('人员更新成功');
        } else {
            alert('更新失败: ' + result.message);
        }
    } catch (error) {
        console.error('更新人员失败:', error);
        alert('更新失败: ' + error.message);
    }
}

// 删除人员
async function deleteEmployee(deptId, empId) {
    const dept = findDepartmentById(organizationData.departments, deptId);
    if (!dept) {
        alert('部门不存在');
        return;
    }

    const employee = dept.employees.find(emp => emp.id === empId);
    if (!employee) {
        alert('人员不存在');
        return;
    }

    if (!confirm(`确定要删除人员"${employee.name}"吗？`)) {
        return;
    }

    try {
        const response = await authManager.authenticatedFetch(`/api/organizations/${encodeURIComponent(currentCustomer)}/departments/${deptId}/employees/${empId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            loadCustomerOrganization(); // 重新加载组织架构
            alert('人员删除成功');
        } else {
            alert('删除失败: ' + result.message);
        }
    } catch (error) {
        console.error('删除人员失败:', error);
        alert('删除失败: ' + error.message);
    }
}

// 显示人员详情
function showEmployeeDetail(deptId, empId) {
    const dept = findDepartmentById(organizationData.departments, deptId);
    if (!dept) {
        alert('部门不存在');
        return;
    }

    const employee = dept.employees.find(emp => emp.id === empId);
    if (!employee) {
        alert('人员不存在');
        return;
    }

    const modalHtml = `
        <div class="modal-overlay" onclick="closeModal()">
            <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 600px;">
                <div class="modal-header">
                    <h3>人员详情</h3>
                    <button class="modal-close" onclick="closeModal()">✕</button>
                </div>
                <div class="modal-body">
                    <div style="display: flex; align-items: center; margin-bottom: 2rem;">
                        <div class="employee-avatar" style="width: 60px; height: 60px; font-size: 1.5rem; margin-right: 1rem;">
                            ${employee.name.charAt(0)}
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #333;">${employee.name}</h3>
                            <p style="margin: 0.5rem 0 0 0; color: #666;">${employee.position} • ${employee.gender}</p>
                            <p style="margin: 0.25rem 0 0 0; color: #999; font-size: 0.9rem;">所属部门：${dept.name}</p>
                        </div>
                    </div>

                    ${employee.description ? `
                        <div style="margin-bottom: 1.5rem;">
                            <h4 style="margin-bottom: 1rem; color: #333;">详细描述</h4>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; border-left: 4px solid #667eea;">
                                ${employee.description}
                            </div>
                        </div>
                    ` : ''}

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.9rem; color: #666;">
                        <div>
                            <strong>创建时间：</strong><br>
                            ${new Date(employee.createdAt).toLocaleString('zh-CN')}
                        </div>
                        <div>
                            <strong>更新时间：</strong><br>
                            ${new Date(employee.updatedAt).toLocaleString('zh-CN')}
                        </div>
                    </div>

                    <div class="form-actions" style="margin-top: 2rem;">
                        <button type="button" onclick="closeModal()">关闭</button>
                        <button type="button" onclick="closeModal(); showEditEmployeeModal('${deptId}', '${empId}')" style="background: #ffc107; color: #333;">编辑</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = document.querySelector('.modal-overlay:last-child');
    if (modal) {
        modal.classList.add('show');
    }
}
