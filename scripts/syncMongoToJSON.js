const mongoManager = require('../utils/mongoManager');
const dataManager = require('../utils/dataManager');

async function syncMongoToJSON() {
    try {
        console.log('🔄 开始同步MongoDB数据到JSON文件...');
        
        // 连接MongoDB
        await mongoManager.connect();
        
        // 从MongoDB加载所有数据
        console.log('📥 从MongoDB加载数据...');
        const salesData = await mongoManager.loadSalesData();
        const customerData = await mongoManager.loadCustomerData();
        const organizationData = await mongoManager.loadOrganizationData();
        const businessManagersData = await mongoManager.loadBusinessManagersData();
        const paymentRecordsData = await mongoManager.loadPaymentRecordsData();
        const qaData = await mongoManager.loadQAData();
        const executionPlansData = await mongoManager.loadExecutionPlansData();
        
        console.log(`📊 销售数据: ${salesData.length} 条`);
        console.log(`👥 客户数据: ${Object.keys(customerData).length} 个`);
        console.log(`🏢 组织数据: ${Object.keys(organizationData).length} 个`);
        console.log(`👨‍💼 商务经理: ${businessManagersData.length} 个`);
        console.log(`💰 回款记录: ${paymentRecordsData.length} 条`);
        console.log(`📚 Q&A数据: ${qaData.length} 条`);
        console.log(`📋 执行计划: ${executionPlansData.length} 条`);
        
        // 保存到JSON文件
        console.log('💾 保存到JSON文件...');
        dataManager.saveSalesData(salesData);
        dataManager.saveCustomerData(customerData);
        dataManager.saveOrganizationData(organizationData);
        dataManager.saveBusinessManagersData(businessManagersData);
        dataManager.savePaymentRecordsData(paymentRecordsData);
        dataManager.saveQAData(qaData);
        dataManager.saveExecutionPlansData(executionPlansData);
        
        console.log('✅ 所有数据已同步到JSON文件');
        
        // 验证海通证券是否已删除
        const haitongInSales = salesData.find(record => record.customerName === '海通证券');
        const haitongInCustomer = customerData['海通证券'];
        
        if (!haitongInSales && !haitongInCustomer) {
            console.log('🎉 确认：海通证券数据已完全删除');
        } else {
            console.log('⚠️ 警告：海通证券数据仍然存在');
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 同步失败:', error);
        process.exit(1);
    }
}

syncMongoToJSON();
