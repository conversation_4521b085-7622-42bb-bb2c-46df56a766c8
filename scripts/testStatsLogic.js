const mongoManager = require('../utils/mongoManager');

async function testStatsLogic() {
    try {
        console.log('🧪 测试修改后的统计逻辑...');
        
        // 设置生产环境
        process.env.NODE_ENV = 'production';
        
        // 连接生产环境MongoDB
        await mongoManager.connect();
        
        // 加载数据
        const salesData = await mongoManager.loadSalesData();
        const customerData = await mongoManager.loadCustomerData();
        
        console.log(`📊 销售记录: ${salesData.length} 条`);
        console.log(`👥 客户数据: ${Object.keys(customerData).length} 个`);
        
        // 模拟统计逻辑
        const now = new Date();
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        console.log(`📅 当前时间: ${now.toISOString().split('T')[0]}`);
        console.log(`📅 一周前: ${oneWeekAgo.toISOString().split('T')[0]}`);
        
        // 初始化统计数据
        const stats = {
            weeklyPayment: { amount: 0, count: 0, projects: [] },
            invoicedPending: { amount: 0, count: 0, projects: [] },
            expectedInvoicing: { amount: 0, count: 0, projects: [] },
            weeklySignedCompleted: { amount: 0, count: 0, projects: [] },
            weeklyNegotiationPending: { amount: 0, count: 0, projects: [] }
        };
        
        // 遍历所有客户的项目
        Object.values(customerData).forEach(customer => {
            if (!customer.projects) return;
            
            customer.projects.forEach(project => {
                const paymentDate = project.paymentTime ? new Date(project.paymentTime) : null;
                const contractDate = project.actualSignTime ? new Date(project.actualSignTime) : null;
                const invoiceDate = project.invoiceTime ? new Date(project.invoiceTime) : null;
                const negotiationEndDate = project.negotiationBiddingEndTime ? new Date(project.negotiationBiddingEndTime) : null;
                
                // 1. 近一周项目回款
                if (paymentDate && paymentDate >= oneWeekAgo && project.paymentAmount) {
                    stats.weeklyPayment.amount += parseFloat(project.paymentAmount) || 0;
                    stats.weeklyPayment.count++;
                    stats.weeklyPayment.projects.push({
                        customer: customer.customerName,
                        project: project.projectName,
                        amount: project.paymentAmount,
                        date: project.paymentTime
                    });
                }
                
                // 2. 已开票待回款（修正后的逻辑：必须有开票时间）
                if (invoiceDate && !paymentDate && !project.paymentAmount && project.expectedRevenue) {
                    stats.invoicedPending.amount += parseFloat(project.expectedRevenue) || 0;
                    stats.invoicedPending.count++;
                    stats.invoicedPending.projects.push({
                        customer: customer.customerName,
                        project: project.projectName,
                        amount: project.expectedRevenue,
                        stage: project.projectStage,
                        invoiceDate: project.invoiceTime
                    });
                }
                
                // 3. 近期预计待开票
                if (contractDate && contractDate >= oneWeekAgo && !invoiceDate && project.expectedRevenue) {
                    stats.expectedInvoicing.amount += parseFloat(project.expectedRevenue) || 0;
                    stats.expectedInvoicing.count++;
                    stats.expectedInvoicing.projects.push({
                        customer: customer.customerName,
                        project: project.projectName,
                        amount: project.expectedRevenue,
                        contractDate: project.actualSignTime
                    });
                }
                
                // 4. 本周已签约完成
                if (contractDate && contractDate >= oneWeekAgo && project.expectedRevenue) {
                    stats.weeklySignedCompleted.amount += parseFloat(project.expectedRevenue) || 0;
                    stats.weeklySignedCompleted.count++;
                    stats.weeklySignedCompleted.projects.push({
                        customer: customer.customerName,
                        project: project.projectName,
                        amount: project.expectedRevenue,
                        contractDate: project.actualSignTime
                    });
                }
                
                // 5. 本周商谈招标待签
                if (negotiationEndDate && negotiationEndDate >= oneWeekAgo && project.expectedRevenue) {
                    stats.weeklyNegotiationPending.amount += parseFloat(project.expectedRevenue) || 0;
                    stats.weeklyNegotiationPending.count++;
                    stats.weeklyNegotiationPending.projects.push({
                        customer: customer.customerName,
                        project: project.projectName,
                        amount: project.expectedRevenue,
                        negotiationEndDate: project.negotiationBiddingEndTime
                    });
                }
            });
        });
        
        // 输出统计结果
        console.log('\n📈 统计结果:');
        console.log(`1. 近一周项目回款: ${stats.weeklyPayment.amount.toFixed(2)}万元 (${stats.weeklyPayment.count}个项目)`);
        console.log(`2. 已开票待回款: ${stats.invoicedPending.amount.toFixed(2)}万元 (${stats.invoicedPending.count}个项目)`);
        console.log(`3. 近期预计待开票: ${stats.expectedInvoicing.amount.toFixed(2)}万元 (${stats.expectedInvoicing.count}个项目)`);
        console.log(`4. 本周已签约完成: ${stats.weeklySignedCompleted.amount.toFixed(2)}万元 (${stats.weeklySignedCompleted.count}个项目)`);
        console.log(`5. 本周商谈招标待签: ${stats.weeklyNegotiationPending.amount.toFixed(2)}万元 (${stats.weeklyNegotiationPending.count}个项目)`);
        
        // 显示详细项目信息（如果有的话）
        Object.keys(stats).forEach(key => {
            if (stats[key].projects.length > 0) {
                console.log(`\n📋 ${key} 详细项目:`);
                stats[key].projects.forEach((proj, index) => {
                    console.log(`   ${index + 1}. ${proj.customer} - ${proj.project} - ${proj.amount}万元`);
                });
            }
        });
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 测试失败:', error);
        console.error('错误详情:', error.stack);
        process.exit(1);
    }
}

testStatsLogic();
