const mongoManager = require('../utils/mongoManager');

async function checkDongYaData() {
    try {
        console.log('🔍 检查东亚前海的项目数据...');
        
        // 设置生产环境
        process.env.NODE_ENV = 'production';
        
        // 连接生产环境MongoDB
        await mongoManager.connect();
        
        // 加载数据
        const customerData = await mongoManager.loadCustomerData();
        
        // 查找东亚前海的数据
        const dongYaCustomer = customerData['东亚前海'];
        
        if (!dongYaCustomer) {
            console.log('❌ 没有找到东亚前海的客户数据');
            process.exit(1);
        }
        
        console.log(`📋 东亚前海客户信息:`);
        console.log(`   项目数量: ${dongYaCustomer.projects ? dongYaCustomer.projects.length : 0}`);
        
        if (dongYaCustomer.projects && dongYaCustomer.projects.length > 0) {
            console.log('\n📊 项目详细信息:');
            
            dongYaCustomer.projects.forEach((project, index) => {
                console.log(`\n   项目 ${index + 1}: ${project.projectName || '未命名'}`);
                console.log(`     - 项目阶段: ${project.projectStage || '未设置'}`);
                console.log(`     - 预期收入: ${project.expectedRevenue || '未设置'} 万元`);
                console.log(`     - 回款金额: ${project.paymentAmount || '未设置'} 万元`);
                console.log(`     - 回款时间: ${project.paymentTime || '未设置'}`);
                console.log(`     - 签约时间: ${project.actualSignTime || '未设置'}`);
                console.log(`     - 开票时间: ${project.invoiceTime || '未设置'}`);
                console.log(`     - 商谈招标结束时间: ${project.negotiationBiddingEndTime || '未设置'}`);
                
                // 检查是否符合已开票待回款条件（修改后的逻辑）
                const invoiceDate = project.invoiceTime ? new Date(project.invoiceTime) : null;
                const paymentDate = project.paymentTime ? new Date(project.paymentTime) : null;

                const isInvoicedPending = (invoiceDate && !paymentDate && !project.paymentAmount) ||
                                         (project.projectStage === '回收货款' && !project.paymentAmount);
                const shouldBeInvoicedPending = isInvoicedPending && project.expectedRevenue;

                console.log(`     - 是否符合已开票待回款条件: ${shouldBeInvoicedPending ? '✅ 是' : '❌ 否'}`);

                if (!shouldBeInvoicedPending) {
                    console.log(`       原因分析:`);
                    console.log(`         - 条件1 (有开票时间且无回款): ${invoiceDate && !paymentDate && !project.paymentAmount ? '✅' : '❌'}`);
                    console.log(`         - 条件2 (项目阶段为回收货款且无回款): ${project.projectStage === '回收货款' && !project.paymentAmount ? '✅' : '❌'}`);
                    console.log(`         - 有预期收入: ${project.expectedRevenue ? '✅' : '❌'}`);
                } else {
                    console.log(`       ✅ 符合条件，应该被统计为已开票待回款: ${project.expectedRevenue}万元`);
                }
            });
        }
        
        // 检查汇总数据
        if (dongYaCustomer.summary) {
            console.log('\n📈 汇总数据:');
            console.log(`   总项目数: ${dongYaCustomer.summary.totalProjects}`);
            console.log(`   总预期收入: ${dongYaCustomer.summary.totalExpectedRevenue} 万元`);
            console.log(`   总实际回款: ${dongYaCustomer.summary.totalActualPayment} 万元`);
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 检查失败:', error);
        console.error('错误详情:', error.stack);
        process.exit(1);
    }
}

checkDongYaData();
