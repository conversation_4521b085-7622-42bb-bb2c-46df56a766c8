const dataManager = require('../utils/dataManager');
const mongoManager = require('../utils/mongoManager');

async function backupData() {
    try {
        console.log('🔄 开始数据备份：MongoDB → JSON文件');
        console.log('=' .repeat(50));
        
        // 连接到MongoDB
        await mongoManager.connect();
        
        // 执行数据备份
        const success = await mongoManager.backupToJSON(dataManager);
        
        if (success) {
            console.log('=' .repeat(50));
            console.log('✅ 数据备份完成！');
            console.log('📊 所有数据已成功从MongoDB备份到JSON文件');
            console.log('📁 备份位置: data/ 目录下的各个JSON文件');
            console.log('💡 提示：这些JSON文件可以作为数据恢复的备份');
        } else {
            console.log('❌ 数据备份失败！');
        }
        
    } catch (error) {
        console.error('❌ 备份过程中发生错误:', error.message);
        console.error('详细错误:', error);
    } finally {
        // 关闭MongoDB连接
        await mongoManager.disconnect();
        process.exit(0);
    }
}

// 运行备份
backupData();
