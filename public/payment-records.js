// 全局变量
let currentData = [];
let filteredData = [];
let currentPage = 1;
let pageSize = 20;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查认证状态
    if (!authManager.isAuthenticated()) {
        authManager.redirectToLogin();
        return;
    }

    loadRegions();
    loadBusinessManagers();
    loadPaymentData();
    loadCustomers();
    loadBusinessManagersForForm();

    // 绑定事件
    document.getElementById('regionFilter').addEventListener('change', applyFilters);
    document.getElementById('businessManagerFilter').addEventListener('change', applyFilters);
    document.getElementById('customerSearch').addEventListener('input', applyFilters);
    document.getElementById('projectSearch').addEventListener('input', applyFilters);
    document.getElementById('paymentDateStart').addEventListener('change', applyFilters);
    document.getElementById('paymentDateEnd').addEventListener('change', applyFilters);

    // 绑定模态框事件
    document.getElementById('addPaymentForm').addEventListener('submit', handleAddPayment);

    // 设置默认日期为今天
    document.getElementById('paymentDate').value = new Date().toISOString().split('T')[0];
});

// 加载区域列表
async function loadRegions() {
    try {
        const response = await authManager.authenticatedFetch('/api/regions');
        const result = await response.json();

        if (result.success) {
            const regionFilter = document.getElementById('regionFilter');
            result.data.forEach(region => {
                const option = document.createElement('option');
                option.value = region;
                option.textContent = region;
                regionFilter.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载区域列表失败:', error);
    }
}

// 加载商务负责人列表
async function loadBusinessManagers() {
    try {
        const response = await authManager.authenticatedFetch('/api/business-managers');
        const result = await response.json();

        if (result.success) {
            const businessManagerFilter = document.getElementById('businessManagerFilter');
            businessManagerFilter.innerHTML = '<option value="all">全部负责人</option>';
            businessManagerFilter.innerHTML += '<option value="未分配">未分配</option>';

            result.data.forEach(manager => {
                const option = document.createElement('option');
                option.value = manager.name;
                option.textContent = `${manager.name} (${manager.username})`;
                businessManagerFilter.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载商务负责人列表失败:', error);
    }
}

// 加载客户列表
async function loadCustomers() {
    try {
        const response = await authManager.authenticatedFetch('/api/customers');
        const result = await response.json();

        if (result.success) {
            const customerSelect = document.getElementById('customerName');
            customerSelect.innerHTML = '<option value="">请选择客户</option>';

            result.data.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.customerName;
                option.textContent = `${customer.customerName} (${customer.region})`;
                customerSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载客户列表失败:', error);
    }
}

// 加载商务经理列表（用于表单）
async function loadBusinessManagersForForm() {
    try {
        const response = await authManager.authenticatedFetch('/api/business-managers');
        const result = await response.json();

        if (result.success) {
            const businessManagerSelect = document.getElementById('businessManager');
            businessManagerSelect.innerHTML = '<option value="">请选择商务经理</option>';

            result.data.forEach(manager => {
                const option = document.createElement('option');
                option.value = manager.name;
                option.textContent = `${manager.name} (${manager.username})`;
                businessManagerSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载商务经理列表失败:', error);
    }
}

// 加载回款数据
async function loadPaymentData() {
    const loading = document.getElementById('loading');
    const table = document.getElementById('paymentTable');
    const noData = document.getElementById('noData');
    
    loading.style.display = 'block';
    table.style.display = 'none';
    noData.style.display = 'none';
    
    try {
        const response = await authManager.authenticatedFetch('/api/payment-records');
        const result = await response.json();
        
        if (result.success) {
            currentData = result.data;
            filteredData = [...currentData];
            
            updateStats();
            renderTable();
            updatePagination();
            
            loading.style.display = 'none';
            if (currentData.length > 0) {
                table.style.display = 'table';
            } else {
                noData.style.display = 'block';
            }
        } else {
            throw new Error(result.message || '加载数据失败');
        }
    } catch (error) {
        console.error('加载回款数据失败:', error);
        loading.textContent = '加载数据失败: ' + error.message;
    }
}

// 应用筛选条件
function applyFilters() {
    const region = document.getElementById('regionFilter').value;
    const businessManager = document.getElementById('businessManagerFilter').value;
    const customerSearch = document.getElementById('customerSearch').value.toLowerCase();
    const projectSearch = document.getElementById('projectSearch').value.toLowerCase();
    const paymentDateStart = document.getElementById('paymentDateStart').value;
    const paymentDateEnd = document.getElementById('paymentDateEnd').value;
    
    filteredData = currentData.filter(record => {
        // 区域筛选
        if (region !== 'all' && record.region !== region) {
            return false;
        }
        
        // 商务负责人筛选
        if (businessManager !== 'all' && record.businessManager !== businessManager) {
            return false;
        }
        
        // 客户搜索
        if (customerSearch && !record.customerName.toLowerCase().includes(customerSearch)) {
            return false;
        }
        
        // 项目搜索
        if (projectSearch && !record.projectName.toLowerCase().includes(projectSearch)) {
            return false;
        }
        
        // 回款时间区间筛选
        if ((paymentDateStart || paymentDateEnd) && record.paymentDate) {
            const recordDate = record.paymentDate; // YYYY-MM-DD 格式

            // 检查开始日期
            if (paymentDateStart && recordDate < paymentDateStart) {
                return false;
            }

            // 检查结束日期
            if (paymentDateEnd && recordDate > paymentDateEnd) {
                return false;
            }
        }
        
        return true;
    });
    
    currentPage = 1;
    updateStats();
    renderTable();
    updatePagination();
}

// 更新统计数据
function updateStats() {
    const totalRecords = filteredData.length;
    const totalAmount = filteredData.reduce((sum, record) => sum + (record.paymentAmount || 0), 0);
    
    // 计算本月回款
    const currentMonth = new Date().toISOString().substring(0, 7);
    const monthlyAmount = filteredData
        .filter(record => record.paymentDate && record.paymentDate.substring(0, 7) === currentMonth)
        .reduce((sum, record) => sum + (record.paymentAmount || 0), 0);
    
    const averageAmount = totalRecords > 0 ? totalAmount / totalRecords : 0;
    
    document.getElementById('totalRecords').textContent = totalRecords;
    document.getElementById('totalAmount').textContent = totalAmount.toFixed(2);
    document.getElementById('monthlyAmount').textContent = monthlyAmount.toFixed(2);
    document.getElementById('averageAmount').textContent = averageAmount.toFixed(2);
}

// 渲染表格
function renderTable() {
    const tbody = document.getElementById('paymentTableBody');
    tbody.innerHTML = '';

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageData = filteredData.slice(startIndex, endIndex);

    pageData.forEach((record, index) => {
        const row = document.createElement('tr');

        // 格式化日期并提取年月
        const invoiceDate = record.invoiceDate ? new Date(record.invoiceDate) : null;
        const contractDate = record.contractDate ? new Date(record.contractDate) : null;
        const paymentDate = record.paymentDate ? new Date(record.paymentDate) : null;

        // 提取年月信息，处理无效日期
        const invoiceYear = (invoiceDate && !isNaN(invoiceDate.getTime())) ? invoiceDate.getFullYear() : '-';
        const invoiceMonth = (invoiceDate && !isNaN(invoiceDate.getTime())) ? invoiceDate.getMonth() + 1 : '-';
        const invoiceDateStr = (invoiceDate && !isNaN(invoiceDate.getTime())) ? invoiceDate.toLocaleDateString('zh-CN') : '-';

        const contractYear = (contractDate && !isNaN(contractDate.getTime())) ? contractDate.getFullYear() : '-';
        const contractMonth = (contractDate && !isNaN(contractDate.getTime())) ? contractDate.getMonth() + 1 : '-';

        const paymentYear = (paymentDate && !isNaN(paymentDate.getTime())) ? paymentDate.getFullYear() : '-';
        const paymentMonth = (paymentDate && !isNaN(paymentDate.getTime())) ? paymentDate.getMonth() + 1 : '-';
        const paymentDay = (paymentDate && !isNaN(paymentDate.getTime())) ? paymentDate.getDate() : '-';

        // 判断数据来源并添加相应的标识
        const sourceIndicator = record.source === 'customer_details'
            ? '<span class="source-tag" title="来自客户详细信息，不可编辑">📋</span>'
            : '<span class="source-tag" title="独立回款记录">💰</span>';

        // 生成操作按钮
        const actionButtons = generateActionButtons(record);

        row.innerHTML = `
            <td>${startIndex + index + 1}</td>
            <td>${invoiceYear}</td>
            <td>${invoiceMonth}</td>
            <td class="date">${invoiceDateStr}</td>
            <td>${contractYear}</td>
            <td>${contractMonth}</td>
            <td>${record.contractNumber || '-'}</td>
            <td>
                <a href="/customer-detail.html?customer=${encodeURIComponent(record.customerName)}"
                   class="customer-link">${record.customerName}</a>
            </td>
            <td class="project-name">${record.projectName} ${sourceIndicator}</td>
            <td>${record.invoiceEntity || '-'}</td>
            <td class="amount">${record.contractRevenue ? record.contractRevenue.toFixed(2) : '-'}</td>
            <td class="amount">${record.contractFinalPayment ? record.contractFinalPayment.toFixed(2) : '-'}</td>
            <td class="amount">${record.invoiceRevenue ? record.invoiceRevenue.toFixed(2) : '-'}</td>
            <td>${paymentYear}</td>
            <td>${paymentMonth}</td>
            <td>${paymentDay}</td>
            <td class="amount">${record.paymentAmount ? record.paymentAmount.toFixed(2) : '-'}</td>
            <td><span class="manager-tag">${record.businessManager || '未分配'}</span></td>
            <td>${actionButtons}</td>
        `;

        tbody.appendChild(row);
    });
}

// 更新分页信息
function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    const pageInfo = document.getElementById('pageInfo');
    const prevButton = document.getElementById('prevPage');
    const nextButton = document.getElementById('nextPage');
    
    pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页 (${filteredData.length} 条记录)`;
    
    prevButton.disabled = currentPage <= 1;
    nextButton.disabled = currentPage >= totalPages;
}

// 切换页面
function changePage(direction) {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    
    if (direction === -1 && currentPage > 1) {
        currentPage--;
    } else if (direction === 1 && currentPage < totalPages) {
        currentPage++;
    }
    
    renderTable();
    updatePagination();
}

// 重置筛选条件
function resetFilters() {
    document.getElementById('regionFilter').value = 'all';
    document.getElementById('businessManagerFilter').value = 'all';
    document.getElementById('customerSearch').value = '';
    document.getElementById('projectSearch').value = '';
    document.getElementById('paymentDateStart').value = '';
    document.getElementById('paymentDateEnd').value = '';

    applyFilters();
}

// 快速设置日期区间
function setDateRange(period) {
    const now = new Date();
    let startDate, endDate;

    switch (period) {
        case 'thisMonth':
            // 本月
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            break;

        case 'lastMonth':
            // 上月
            startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            endDate = new Date(now.getFullYear(), now.getMonth(), 0);
            break;

        case 'thisQuarter':
            // 本季度
            const currentQuarter = Math.floor(now.getMonth() / 3);
            startDate = new Date(now.getFullYear(), currentQuarter * 3, 1);
            endDate = new Date(now.getFullYear(), (currentQuarter + 1) * 3, 0);
            break;

        case 'thisYear':
            // 本年
            startDate = new Date(now.getFullYear(), 0, 1);
            endDate = new Date(now.getFullYear(), 11, 31);
            break;

        default:
            return;
    }

    // 格式化日期为 YYYY-MM-DD
    const formatDate = (date) => {
        return date.toISOString().split('T')[0];
    };

    document.getElementById('paymentDateStart').value = formatDate(startDate);
    document.getElementById('paymentDateEnd').value = formatDate(endDate);

    // 应用筛选
    applyFilters();
}

// 显示新增模态框

// 显示新增模态框
function showAddModal() {
    document.getElementById('addPaymentModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// 关闭新增模态框
function closeAddModal() {
    document.getElementById('addPaymentModal').style.display = 'none';
    document.body.style.overflow = 'auto';

    // 重置表单
    document.getElementById('addPaymentForm').reset();
    document.getElementById('paymentDate').value = new Date().toISOString().split('T')[0];
}

// 处理新增回款记录
async function handleAddPayment(event) {
    event.preventDefault();

    const customerName = document.getElementById('customerName').value;
    const projectName = document.getElementById('projectName').value;
    const contractNumber = document.getElementById('contractNumber').value;
    const businessManager = document.getElementById('businessManager').value;
    const invoiceDate = document.getElementById('invoiceDate').value;
    const contractDate = document.getElementById('contractDate').value;
    const paymentDate = document.getElementById('paymentDate').value;
    const paymentAmount = parseFloat(document.getElementById('paymentAmount').value);
    const invoiceEntity = document.getElementById('invoiceEntity').value;
    const contractRevenue = parseFloat(document.getElementById('contractRevenue').value) || 0;
    const contractFinalPayment = parseFloat(document.getElementById('contractFinalPayment').value) || 0;
    const invoiceRevenue = parseFloat(document.getElementById('invoiceRevenue').value) || 0;

    if (!customerName || !projectName || !paymentAmount || !paymentDate) {
        alert('请填写所有必填字段');
        return;
    }

    try {
        const response = await authManager.authenticatedFetch('/api/payment-records', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                customerName,
                projectName,
                contractNumber: contractNumber || null,
                businessManager: businessManager || null,
                invoiceDate: invoiceDate || null,
                contractDate: contractDate || null,
                paymentDate,
                paymentAmount,
                invoiceEntity: invoiceEntity || null,
                contractRevenue,
                contractFinalPayment,
                invoiceRevenue
            })
        });

        const result = await response.json();

        if (result.success) {
            alert('回款记录添加成功！');
            closeAddModal();
            loadPaymentData(); // 重新加载数据
        } else {
            alert('添加失败: ' + result.message);
        }
    } catch (error) {
        console.error('添加回款记录失败:', error);
        alert('添加失败: ' + error.message);
    }
}

// 导出数据
function exportData() {
    if (filteredData.length === 0) {
        alert('没有数据可以导出');
        return;
    }

    // 创建CSV内容
    const headers = ['序号', '开票年', '开票月', '开票日期', '签约年', '签约月', '合同号', '客户', '项目',
                    '开票主体', '合同收入', '合同尾款', '开票收入', '收款年', '收款月份', '收款日', '收款金额', '商务经理'];
    const csvContent = [
        headers.join(','),
        ...filteredData.map((record, index) => {
            const invoiceDate = record.invoiceDate ? new Date(record.invoiceDate) : null;
            const contractDate = record.contractDate ? new Date(record.contractDate) : null;
            const paymentDate = record.paymentDate ? new Date(record.paymentDate) : null;

            return [
                index + 1,
                (invoiceDate && !isNaN(invoiceDate.getTime())) ? invoiceDate.getFullYear() : '',
                (invoiceDate && !isNaN(invoiceDate.getTime())) ? invoiceDate.getMonth() + 1 : '',
                (invoiceDate && !isNaN(invoiceDate.getTime())) ? invoiceDate.toLocaleDateString('zh-CN') : '',
                (contractDate && !isNaN(contractDate.getTime())) ? contractDate.getFullYear() : '',
                (contractDate && !isNaN(contractDate.getTime())) ? contractDate.getMonth() + 1 : '',
                record.contractNumber || '',
                record.customerName,
                record.projectName,
                record.invoiceEntity || '',
                record.contractRevenue || 0,
                record.contractFinalPayment || 0,
                record.invoiceRevenue || 0,
                (paymentDate && !isNaN(paymentDate.getTime())) ? paymentDate.getFullYear() : '',
                (paymentDate && !isNaN(paymentDate.getTime())) ? paymentDate.getMonth() + 1 : '',
                (paymentDate && !isNaN(paymentDate.getTime())) ? paymentDate.getDate() : '',
                record.paymentAmount || 0,
                record.businessManager || '未分配'
            ].join(',');
        })
    ].join('\n');

    // 下载文件
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `回款数据_${new Date().toISOString().substring(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 生成操作按钮
function generateActionButtons(record) {
    // 只有手动新增的记录才能编辑和删除
    if (record.source !== 'independent') {
        return '<div class="action-buttons"><span style="color: #999; font-size: 0.8rem;">不可操作</span></div>';
    }

    // 检查权限：管理员或对应的商务负责人
    const currentUser = authManager.getCurrentUser();
    const canEdit = currentUser.permission === 'admin' ||
                   (currentUser.permission === 'manager' && currentUser.name === record.businessManager);

    if (!canEdit) {
        return '<div class="action-buttons"><span style="color: #999; font-size: 0.8rem;">无权限</span></div>';
    }

    return `
        <div class="action-buttons">
            <button class="btn-edit" onclick="showEditModal(${record.id})">编辑</button>
            <button class="btn-delete" onclick="confirmDeleteRecord(${record.id})">删除</button>
        </div>
    `;
}

// 显示编辑模态框
function showEditModal(recordId) {
    const record = currentData.find(r => r.id == recordId); // 使用 == 进行类型转换比较
    if (!record) {
        alert('找不到要编辑的记录');
        return;
    }

    // 填充表单数据
    document.getElementById('editRecordId').value = record.id;
    document.getElementById('editCustomerName').value = record.customerName || '';
    document.getElementById('editProjectName').value = record.projectName || '';
    document.getElementById('editContractNumber').value = record.contractNumber || '';
    document.getElementById('editBusinessManager').value = record.businessManager || '';
    document.getElementById('editInvoiceDate').value = record.invoiceDate || '';
    document.getElementById('editContractDate').value = record.contractDate || '';
    document.getElementById('editPaymentAmount').value = record.paymentAmount || '';
    document.getElementById('editPaymentDate').value = record.paymentDate || '';
    document.getElementById('editInvoiceEntity').value = record.invoiceEntity || '';
    document.getElementById('editContractRevenue').value = record.contractRevenue || '';
    document.getElementById('editContractFinalPayment').value = record.contractFinalPayment || '';
    document.getElementById('editInvoiceRevenue').value = record.invoiceRevenue || '';

    // 显示模态框
    document.getElementById('editModal').style.display = 'block';
}

// 关闭编辑模态框
function closeEditModal() {
    document.getElementById('editModal').style.display = 'none';
    document.getElementById('editForm').reset();
}

// 更新回款记录
async function updatePaymentRecord(event) {
    event.preventDefault();

    const recordId = document.getElementById('editRecordId').value;
    const formData = {
        customerName: document.getElementById('editCustomerName').value,
        projectName: document.getElementById('editProjectName').value,
        contractNumber: document.getElementById('editContractNumber').value,
        businessManager: document.getElementById('editBusinessManager').value,
        invoiceDate: document.getElementById('editInvoiceDate').value,
        contractDate: document.getElementById('editContractDate').value,
        paymentAmount: parseFloat(document.getElementById('editPaymentAmount').value),
        paymentDate: document.getElementById('editPaymentDate').value,
        invoiceEntity: document.getElementById('editInvoiceEntity').value,
        contractRevenue: parseFloat(document.getElementById('editContractRevenue').value) || 0,
        contractFinalPayment: parseFloat(document.getElementById('editContractFinalPayment').value) || 0,
        invoiceRevenue: parseFloat(document.getElementById('editInvoiceRevenue').value) || 0
    };

    try {
        const response = await authManager.authenticatedFetch(`/api/payment-records/${recordId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            alert('回款记录更新成功！');
            closeEditModal();
            loadPaymentData(); // 重新加载数据
        } else {
            alert('更新失败: ' + result.message);
        }
    } catch (error) {
        console.error('更新回款记录失败:', error);
        alert('更新失败: ' + error.message);
    }
}

// 确认删除记录
function confirmDeleteRecord(recordId) {
    const record = currentData.find(r => r.id == recordId); // 使用 == 进行类型转换比较
    if (!record) {
        alert('找不到要删除的记录');
        return;
    }

    if (confirm(`确定要删除客户"${record.customerName}"的项目"${record.projectName}"的回款记录吗？\n\n此操作不可撤销！`)) {
        deletePaymentRecord(recordId);
    }
}

// 删除回款记录
async function deletePaymentRecord(recordId) {
    try {
        const response = await authManager.authenticatedFetch(`/api/payment-records/${recordId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            alert('回款记录删除成功！');
            loadPaymentData(); // 重新加载数据
        } else {
            alert('删除失败: ' + result.message);
        }
    } catch (error) {
        console.error('删除回款记录失败:', error);
        alert('删除失败: ' + error.message);
    }
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const editModal = document.getElementById('editModal');
    if (event.target === editModal) {
        closeEditModal();
    }
}
