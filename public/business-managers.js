// 全局变量
let currentData = [];
let editingId = null;
let currentCustomers = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadData();

    // 绑定事件
    document.getElementById('permissionFilter').addEventListener('change', filterData);
    document.getElementById('searchInput').addEventListener('input', filterData);
    document.getElementById('managerForm').addEventListener('submit', handleFormSubmit);
});

// 加载商务负责人数据
async function loadData() {
    const loading = document.getElementById('loading');
    const table = document.getElementById('managersTable');
    
    loading.style.display = 'block';
    table.style.display = 'none';
    
    try {
        const response = await fetch('/api/business-managers');
        const result = await response.json();
        
        if (result.success) {
            currentData = result.data;
            renderTable(result.data);
            
            loading.style.display = 'none';
            table.style.display = 'table';
        } else {
            throw new Error(result.message || '加载数据失败');
        }
    } catch (error) {
        console.error('加载数据失败:', error);
        loading.textContent = '加载数据失败: ' + error.message;
    }
}

// 权限显示映射
const permissionMap = {
    'admin': '管理员',
    'manager': '经理'
};

// 渲染表格
function renderTable(data) {
    const tbody = document.getElementById('managersTableBody');
    tbody.innerHTML = '';

    data.forEach(manager => {
        const row = document.createElement('tr');

        // 客户列表显示
        const customersDisplay = manager.customers.length > 0
            ? `<span class="customers-list" title="${manager.customers.join(', ')}">${manager.customers.slice(0, 3).join(', ')}${manager.customers.length > 3 ? '...' : ''}</span><span class="customers-count">${manager.customers.length}</span>`
            : '<span style="color: #999;">暂无客户</span>';

        // 权限显示
        const permissionDisplay = permissionMap[manager.permission] || manager.permission;
        const permissionClass = manager.permission === 'admin' ? 'status-admin' : 'status-manager';

        row.innerHTML = `
            <td>${manager.id}</td>
            <td><strong>${manager.name}</strong></td>
            <td><code style="background: #f5f5f5; padding: 0.25rem 0.5rem; border-radius: 4px;">${manager.username}</code></td>
            <td><span class="status-badge ${permissionClass}">${permissionDisplay}</span></td>
            <td>${customersDisplay}</td>
            <td class="action-buttons">
                <button class="btn-edit" onclick="editManager(${manager.id})">编辑</button>
                <button class="btn-delete" onclick="deleteManager(${manager.id})">删除</button>
            </td>
        `;

        tbody.appendChild(row);
    });
}

// 筛选数据
function filterData() {
    const permission = document.getElementById('permissionFilter').value;
    const search = document.getElementById('searchInput').value.toLowerCase();

    let filteredData = currentData;

    // 按权限筛选
    if (permission !== 'all') {
        filteredData = filteredData.filter(manager => manager.permission === permission);
    }

    // 按搜索关键词筛选
    if (search) {
        filteredData = filteredData.filter(manager =>
            manager.name.toLowerCase().includes(search) ||
            manager.username.toLowerCase().includes(search)
        );
    }

    renderTable(filteredData);
}

// 显示添加模态框
function showAddModal() {
    editingId = null;
    currentCustomers = [];
    document.getElementById('modalTitle').textContent = '添加商务负责人';
    document.getElementById('managerForm').reset();
    updateCustomersTags();
    document.getElementById('editModal').style.display = 'block';
}

// 编辑商务负责人
async function editManager(id) {
    try {
        const response = await fetch(`/api/business-managers/${id}`);
        const result = await response.json();

        if (result.success) {
            const manager = result.data;
            editingId = id;
            currentCustomers = [...manager.customers];

            document.getElementById('modalTitle').textContent = '编辑商务负责人';
            document.getElementById('editName').value = manager.name;
            document.getElementById('editUsername').value = manager.username;
            document.getElementById('editPassword').value = manager.password;
            document.getElementById('editPermission').value = manager.permission;

            updateCustomersTags();
            document.getElementById('editModal').style.display = 'block';
        }
    } catch (error) {
        console.error('加载负责人信息失败:', error);
        alert('加载负责人信息失败: ' + error.message);
    }
}

// 删除商务负责人
async function deleteManager(id) {
    const manager = currentData.find(m => m.id === id);
    if (!manager) return;
    
    const hasCustomers = manager.customers.length > 0;
    let confirmMessage = `确定要删除商务负责人"${manager.name}"吗？`;
    
    if (hasCustomers) {
        confirmMessage += `\n\n注意：该负责人当前负责 ${manager.customers.length} 个客户：\n${manager.customers.join(', ')}\n\n删除后这些客户将变为未分配状态。`;
    }
    
    if (!confirm(confirmMessage)) {
        return;
    }
    
    try {
        const response = await fetch(`/api/business-managers/${id}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('删除成功');
            loadData();
        } else {
            throw new Error(result.message || '删除失败');
        }
    } catch (error) {
        console.error('删除失败:', error);
        alert('删除失败: ' + error.message);
    }
}

// 处理表单提交
async function handleFormSubmit(event) {
    event.preventDefault();

    const formData = {
        name: document.getElementById('editName').value,
        username: document.getElementById('editUsername').value,
        password: document.getElementById('editPassword').value,
        permission: document.getElementById('editPermission').value,
        customers: currentCustomers
    };
    
    try {
        const url = editingId ? `/api/business-managers/${editingId}` : '/api/business-managers';
        const method = editingId ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert(editingId ? '更新成功' : '添加成功');
            closeModal();
            loadData();
        } else {
            throw new Error(result.message || '保存失败');
        }
    } catch (error) {
        console.error('保存失败:', error);
        alert('保存失败: ' + error.message);
    }
}

// 添加客户
function addCustomer() {
    const input = document.getElementById('newCustomerInput');
    const customerName = input.value.trim();
    
    if (!customerName) {
        alert('请输入客户名称');
        return;
    }
    
    if (currentCustomers.includes(customerName)) {
        alert('该客户已存在');
        return;
    }
    
    currentCustomers.push(customerName);
    input.value = '';
    updateCustomersTags();
}

// 移除客户
function removeCustomer(customerName) {
    const index = currentCustomers.indexOf(customerName);
    if (index > -1) {
        currentCustomers.splice(index, 1);
        updateCustomersTags();
    }
}

// 更新客户标签显示
function updateCustomersTags() {
    const container = document.getElementById('customersTags');
    container.innerHTML = '';
    
    currentCustomers.forEach(customer => {
        const tag = document.createElement('div');
        tag.className = 'customer-tag';
        tag.innerHTML = `
            ${customer}
            <span class="remove" onclick="removeCustomer('${customer}')">&times;</span>
        `;
        container.appendChild(tag);
    });
}

// 关闭模态框
function closeModal() {
    document.getElementById('editModal').style.display = 'none';
    editingId = null;
    currentCustomers = [];
}

// 点击模态框外部关闭
document.getElementById('editModal').addEventListener('click', function(event) {
    if (event.target === this) {
        closeModal();
    }
});

// 回车键添加客户
document.getElementById('newCustomerInput').addEventListener('keypress', function(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        addCustomer();
    }
});
