<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 销售漏斗管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 400px;
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h1 {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .login-header p {
            color: #666;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .login-button {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            text-align: center;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            text-align: center;
            display: none;
        }

        .demo-accounts {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1.5rem;
            font-size: 0.9rem;
        }

        .demo-accounts h3 {
            color: #667eea;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .demo-accounts p {
            margin-bottom: 0.25rem;
            color: #666;
        }

        .demo-accounts .account {
            background: white;
            padding: 0.5rem;
            border-radius: 5px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: background 0.3s;
        }

        .demo-accounts .account:hover {
            background: #f8f9fa;
        }

        .demo-accounts .account strong {
            color: #667eea;
        }

        .loading {
            display: none;
            text-align: center;
            color: #667eea;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }

            .login-header h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🔐 系统登录</h1>
            <p>销售漏斗管理系统</p>
        </div>

        <div id="errorMessage" class="error-message"></div>
        <div id="successMessage" class="success-message"></div>
        <div id="loading" class="loading">正在登录...</div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required placeholder="请输入用户名">
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required placeholder="请输入密码">
            </div>

            <button type="submit" class="login-button" id="loginButton">
                登录系统
            </button>
        </form>

        <!-- <div class="demo-accounts">
            <h3>演示账号</h3>
            <div class="account" onclick="fillAccount('admin', 'admin123')">
                <strong>管理员账号:</strong> admin / admin123
            </div>
            <div class="account" onclick="fillAccount('wang_wei', '123456')">
                <strong>商务经理:</strong> wang_wei / 123456 (王伟)
            </div>
            <div class="account" onclick="fillAccount('cui_zhican', '123456')">
                <strong>商务经理:</strong> cui_zhican / 123456 (崔治灿)
            </div>
            <div class="account" onclick="fillAccount('zhang_qianfeng', '123456')">
                <strong>商务经理:</strong> zhang_qianfeng / 123456 (张前锋)
            </div>
            <p style="margin-top: 0.5rem; font-size: 0.8rem; color: #999;">
                点击上方账号可快速填入登录信息
            </p>
        </div> -->
    </div>

    <script>
        // 页面加载时检查是否已登录
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('authToken');
            if (token) {
                // 验证token是否有效
                fetch('/api/auth/verify', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        // 已登录，跳转到主页
                        window.location.href = '/';
                    } else {
                        // token无效，清除
                        localStorage.removeItem('authToken');
                        localStorage.removeItem('userInfo');
                    }
                })
                .catch(error => {
                    console.error('验证token失败:', error);
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('userInfo');
                });
            }

            // 绑定表单提交事件
            document.getElementById('loginForm').addEventListener('submit', handleLogin);

            // 添加键盘快捷键支持
            document.addEventListener('keydown', function(event) {
                // Ctrl/Cmd + Enter 快速登录
                if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                    event.preventDefault();
                    const form = document.getElementById('loginForm');
                    const submitEvent = new Event('submit');
                    form.dispatchEvent(submitEvent);
                }

                // ESC 清空表单
                if (event.key === 'Escape') {
                    document.getElementById('loginForm').reset();
                    hideMessages();
                }
            });

            // 自动聚焦到用户名输入框
            document.getElementById('username').focus();
        });

        // 处理登录
        async function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginButton = document.getElementById('loginButton');
            const loading = document.getElementById('loading');
            
            if (!username || !password) {
                showError('请输入用户名和密码');
                return;
            }

            // 显示加载状态
            loginButton.disabled = true;
            loading.style.display = 'block';
            hideMessages();

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();

                if (result.success) {
                    // 保存token和用户信息
                    localStorage.setItem('authToken', result.token);
                    localStorage.setItem('userInfo', JSON.stringify(result.user));
                    showSuccess('登录成功，正在跳转...');
                    
                    // 延迟跳转，让用户看到成功消息
                    setTimeout(() => {
                        window.location.href = '/dashboard.html';
                    }, 1000);
                } else {
                    showError(result.message || '登录失败');
                }
            } catch (error) {
                console.error('登录请求失败:', error);
                showError('网络错误，请稍后重试');
            } finally {
                loginButton.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 快速填入账号信息
        function fillAccount(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }

        // 显示错误消息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('successMessage').style.display = 'none';
        }

        // 显示成功消息
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
        }

        // 隐藏所有消息
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }
    </script>
</body>
</html>
