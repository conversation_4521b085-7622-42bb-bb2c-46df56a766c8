<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回款数据 - 销售漏斗管理系统</title>
    <link rel="stylesheet" href="css/modal-responsive.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 2rem;
        }

        .header-title {
            flex: 1;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
        }

        .header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .control-group label {
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
        }

        select, input {
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .date-range-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .date-range-container input[type="date"] {
            flex: 1;
            min-width: 140px;
        }

        .date-separator {
            color: #666;
            font-weight: 500;
            white-space: nowrap;
        }

        .date-quick-select {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
            flex-wrap: wrap;
        }

        .quick-select-btn {
            padding: 0.4rem 0.8rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            color: #666;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .quick-select-btn:hover {
            background: #f8f9fa;
            border-color: #667eea;
            color: #667eea;
        }

        .quick-select-btn:active {
            background: #667eea;
            color: white;
        }

        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-card h3 {
            color: #667eea;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-card .value {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .stat-card .unit {
            color: #666;
            font-size: 0.9rem;
        }

        .table-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            overflow-x: auto;
            overflow-y: visible;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            max-width: 100%;
            /* 添加滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: #667eea #f1f1f1;
        }

        /* Webkit浏览器滚动条样式 */
        .table-container::-webkit-scrollbar {
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #5a6fd8;
        }

        table {
            width: max-content;
            min-width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        th, td {
            padding: 0.75rem 0.5rem;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
            white-space: nowrap;
            min-width: 80px;
        }

        th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
            white-space: nowrap;
            text-align: center;
            border-right: 1px solid #dee2e6;
        }

        /* 为不同列设置合适的最小宽度 */
        th:nth-child(1), td:nth-child(1) { min-width: 60px; text-align: center; } /* 序号 */
        th:nth-child(2), td:nth-child(2) { min-width: 70px; text-align: center; } /* 开票年 */
        th:nth-child(3), td:nth-child(3) { min-width: 70px; text-align: center; } /* 开票月 */
        th:nth-child(4), td:nth-child(4) { min-width: 100px; text-align: center; } /* 开票日期 */
        th:nth-child(5), td:nth-child(5) { min-width: 70px; text-align: center; } /* 签约年 */
        th:nth-child(6), td:nth-child(6) { min-width: 70px; text-align: center; } /* 签约月 */
        th:nth-child(7), td:nth-child(7) { min-width: 120px; } /* 合同号 */
        th:nth-child(8), td:nth-child(8) { min-width: 120px; } /* 客户 */
        th:nth-child(9), td:nth-child(9) { min-width: 150px; } /* 项目 */
        th:nth-child(10), td:nth-child(10) { min-width: 120px; } /* 开票主体 */
        th:nth-child(11), td:nth-child(11) { min-width: 90px; text-align: right; } /* 合同收入 */
        th:nth-child(12), td:nth-child(12) { min-width: 90px; text-align: right; } /* 合同尾款 */
        th:nth-child(13), td:nth-child(13) { min-width: 90px; text-align: right; } /* 开票收入 */
        th:nth-child(14), td:nth-child(14) { min-width: 70px; text-align: center; } /* 收款年 */
        th:nth-child(15), td:nth-child(15) { min-width: 80px; text-align: center; } /* 收款月份 */
        th:nth-child(16), td:nth-child(16) { min-width: 70px; text-align: center; } /* 收款日 */
        th:nth-child(17), td:nth-child(17) { min-width: 100px; text-align: right; } /* 收款金额 */
        th:nth-child(18), td:nth-child(18) { min-width: 100px; } /* 商务经理 */

        tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .region-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .manager-tag {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        /* 操作按钮样式 */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
        }

        .btn-edit, .btn-delete {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-edit {
            background: #28a745;
            color: white;
        }

        .btn-edit:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background: #c82333;
            transform: translateY(-1px);
        }

        .btn-disabled {
            background: #6c757d !important;
            cursor: not-allowed !important;
            opacity: 0.6;
        }

        .btn-disabled:hover {
            transform: none !important;
        }

        .amount {
            font-weight: 600;
            color: #28a745;
        }

        .date {
            color: #666;
            font-size: 0.9rem;
        }

        .project-name {
            font-weight: 500;
            color: #333;
        }

        .customer-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .customer-link:hover {
            text-decoration: underline;
        }

        .payment-note {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #666;
            font-style: italic;
        }

        .source-tag {
            font-size: 0.8rem;
            margin-left: 0.5rem;
            opacity: 0.7;
            cursor: help;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #666;
            font-size: 1.1rem;
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: #999;
            font-size: 1.1rem;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-top: 2rem;
            padding: 1rem;
        }

        .pagination button {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .pagination .page-info {
            color: #666;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .control-group {
                width: 100%;
            }

            .date-range-container {
                flex-direction: column;
                gap: 0.75rem;
            }

            .date-range-container input[type="date"] {
                min-width: auto;
                width: 100%;
            }

            .date-separator {
                align-self: center;
            }

            .date-quick-select {
                justify-content: center;
                margin-top: 0.75rem;
            }

            .quick-select-btn {
                flex: 1;
                min-width: 60px;
                font-size: 0.8rem;
                padding: 0.5rem 0.4rem;
            }

            .stats-cards {
                grid-template-columns: repeat(2, 1fr);
            }

            .table-container {
                /* 在移动端确保横向滚动 */
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            table {
                font-size: 0.8rem;
                /* 确保表格在移动端有足够宽度 */
                min-width: 1200px;
            }

            th, td {
                padding: 0.5rem 0.25rem;
                font-size: 0.75rem;
            }

            /* 移动端列宽调整 */
            th:nth-child(1), td:nth-child(1) { min-width: 50px; } /* 序号 */
            th:nth-child(2), td:nth-child(2) { min-width: 60px; } /* 开票年 */
            th:nth-child(3), td:nth-child(3) { min-width: 60px; } /* 开票月 */
            th:nth-child(4), td:nth-child(4) { min-width: 90px; } /* 开票日期 */
            th:nth-child(5), td:nth-child(5) { min-width: 60px; } /* 签约年 */
            th:nth-child(6), td:nth-child(6) { min-width: 60px; } /* 签约月 */
            th:nth-child(7), td:nth-child(7) { min-width: 100px; } /* 合同号 */
            th:nth-child(8), td:nth-child(8) { min-width: 100px; } /* 客户 */
            th:nth-child(9), td:nth-child(9) { min-width: 120px; } /* 项目 */
            th:nth-child(10), td:nth-child(10) { min-width: 100px; } /* 开票主体 */
            th:nth-child(11), td:nth-child(11) { min-width: 80px; } /* 合同收入 */
            th:nth-child(12), td:nth-child(12) { min-width: 80px; } /* 合同尾款 */
            th:nth-child(13), td:nth-child(13) { min-width: 80px; } /* 开票收入 */
            th:nth-child(14), td:nth-child(14) { min-width: 60px; } /* 收款年 */
            th:nth-child(15), td:nth-child(15) { min-width: 70px; } /* 收款月份 */
            th:nth-child(16), td:nth-child(16) { min-width: 60px; } /* 收款日 */
            th:nth-child(17), td:nth-child(17) { min-width: 90px; } /* 收款金额 */
            th:nth-child(18), td:nth-child(18) { min-width: 90px; } /* 商务经理 */
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background 0.3s;
        }

        .close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #e1e5e9;
        }

        .btn-cancel {
            background: #6c757d;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-cancel:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        .btn-submit {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-submit:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .required {
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .modal-content {
                margin: 2% auto;
                width: 95%;
            }

            .modal-body {
                padding: 1rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="header-title">
                    <h1>💰 回款数据</h1>
                    <p>查看和管理所有项目的回款记录</p>
                    <div style="margin-top: 1rem; font-size: 0.9rem; color: #666;">
                        <span style="margin-right: 1rem;">💰 独立回款记录</span>
                        <span>📋 客户详细信息中的回款数据</span>
                    </div>
                </div>
                <div class="user-info"></div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>区域筛选:</label>
                <select id="regionFilter">
                    <option value="all">全部区域</option>
                </select>
            </div>
            <div class="control-group">
                <label>商务负责人:</label>
                <select id="businessManagerFilter">
                    <option value="all">全部负责人</option>
                </select>
            </div>
            <div class="control-group">
                <label>客户搜索:</label>
                <input type="text" id="customerSearch" placeholder="搜索客户名称">
            </div>
            <div class="control-group">
                <label>项目搜索:</label>
                <input type="text" id="projectSearch" placeholder="搜索项目名称">
            </div>
            <div class="control-group">
                <label>回款时间区间:</label>
                <div class="date-range-container">
                    <input type="date" id="paymentDateStart" placeholder="开始日期">
                    <span class="date-separator">至</span>
                    <input type="date" id="paymentDateEnd" placeholder="结束日期">
                </div>
                <!-- <div class="date-quick-select">
                    <button type="button" class="quick-select-btn" onclick="setDateRange('thisMonth')">本月</button>
                    <button type="button" class="quick-select-btn" onclick="setDateRange('thisQuarter')">本季度</button>
                    <button type="button" class="quick-select-btn" onclick="setDateRange('thisYear')">本年</button>
                    <button type="button" class="quick-select-btn" onclick="setDateRange('lastMonth')">上月</button>
                </div> -->
            </div>
            <button onclick="resetFilters()">
                🔄 重置筛选
            </button>
            <button onclick="exportData()">
                📊 导出数据
            </button>
            <button onclick="showAddModal()" style="background: linear-gradient(135deg, #28a745, #20c997);">
                ➕ 新增回款记录
            </button>
        </div>

        <div class="stats-cards">
            <div class="stat-card">
                <h3>总回款记录</h3>
                <div class="value" id="totalRecords">-</div>
                <div class="unit">条记录</div>
            </div>
            <div class="stat-card">
                <h3>总回款金额</h3>
                <div class="value" id="totalAmount">-</div>
                <div class="unit">万元</div>
            </div>
            <div class="stat-card">
                <h3>本月回款</h3>
                <div class="value" id="monthlyAmount">-</div>
                <div class="unit">万元</div>
            </div>
            <div class="stat-card">
                <h3>平均回款</h3>
                <div class="value" id="averageAmount">-</div>
                <div class="unit">万元/笔</div>
            </div>
        </div>

        <div style="margin-bottom: 1rem; padding: 0.75rem; background: rgba(102, 126, 234, 0.1); border-radius: 8px; color: #495057; font-size: 0.9rem; display: flex; align-items: center; gap: 0.5rem;">
            <span>💡</span>
            <span>表格支持横向滚动查看所有字段，表头不会换行以保持清晰显示</span>
        </div>

        <div class="table-container">
            <div id="loading" class="loading">正在加载数据...</div>
            <table id="paymentTable" style="display: none;">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>开票年</th>
                        <th>开票月</th>
                        <th>开票日期</th>
                        <th>签约年</th>
                        <th>签约月</th>
                        <th>合同号</th>
                        <th>客户</th>
                        <th>项目</th>
                        <th>开票主体</th>
                        <th>合同收入</th>
                        <th>合同尾款</th>
                        <th>开票收入</th>
                        <th>收款年</th>
                        <th>收款月份</th>
                        <th>收款日</th>
                        <th>收款金额</th>
                        <th>商务经理</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="paymentTableBody">
                </tbody>
            </table>
            <div id="noData" class="no-data" style="display: none;">
                暂无回款数据
            </div>
        </div>

        <div class="pagination">
            <button id="prevPage" onclick="changePage(-1)">上一页</button>
            <span class="page-info" id="pageInfo">第 1 页，共 1 页</span>
            <button id="nextPage" onclick="changePage(1)">下一页</button>
        </div>
    </div>

    <!-- 新增回款记录模态框 -->
    <div id="addPaymentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>新增回款记录</h2>
                <button class="close" onclick="closeAddModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addPaymentForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerName">客户 <span class="required">*</span></label>
                            <select id="customerName" required>
                                <option value="">请选择客户</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="projectName">项目 <span class="required">*</span></label>
                            <input type="text" id="projectName" required placeholder="请输入项目名称">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="contractNumber">合同号</label>
                            <input type="text" id="contractNumber" placeholder="请输入合同号">
                        </div>
                        <div class="form-group">
                            <label for="businessManager">商务经理</label>
                            <select id="businessManager">
                                <option value="">请选择商务经理</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="invoiceDate">开票日期</label>
                            <input type="date" id="invoiceDate">
                        </div>
                        <div class="form-group">
                            <label for="contractDate">签约日期</label>
                            <input type="date" id="contractDate">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="paymentDate">收款日期 <span class="required">*</span></label>
                            <input type="date" id="paymentDate" required>
                        </div>
                        <div class="form-group">
                            <label for="paymentAmount">收款金额 (万元) <span class="required">*</span></label>
                            <input type="number" id="paymentAmount" step="0.01" min="0" required placeholder="请输入收款金额">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="invoiceEntity">开票主体</label>
                            <input type="text" id="invoiceEntity" placeholder="请输入开票主体">
                        </div>
                        <div class="form-group">
                            <label for="contractRevenue">合同收入 (万元)</label>
                            <input type="number" id="contractRevenue" step="0.01" min="0" placeholder="请输入合同收入">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="contractFinalPayment">合同尾款 (万元)</label>
                            <input type="number" id="contractFinalPayment" step="0.01" min="0" placeholder="请输入合同尾款">
                        </div>
                        <div class="form-group">
                            <label for="invoiceRevenue">开票收入 (万元)</label>
                            <input type="number" id="invoiceRevenue" step="0.01" min="0" placeholder="请输入开票收入">
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn-cancel" onclick="closeAddModal()">取消</button>
                        <button type="submit" class="btn-submit">保存回款记录</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑回款记录模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑回款记录</h3>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editForm" onsubmit="updatePaymentRecord(event)">
                    <input type="hidden" id="editRecordId">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editCustomerName">客户名称 *</label>
                            <input type="text" id="editCustomerName" required placeholder="请输入客户名称">
                        </div>
                        <div class="form-group">
                            <label for="editProjectName">项目名称 *</label>
                            <input type="text" id="editProjectName" required placeholder="请输入项目名称">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editContractNumber">合同号</label>
                            <input type="text" id="editContractNumber" placeholder="请输入合同号">
                        </div>
                        <div class="form-group">
                            <label for="editBusinessManager">商务经理</label>
                            <input type="text" id="editBusinessManager" placeholder="请输入商务经理">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editInvoiceDate">开票日期</label>
                            <input type="date" id="editInvoiceDate">
                        </div>
                        <div class="form-group">
                            <label for="editContractDate">合同日期</label>
                            <input type="date" id="editContractDate">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editPaymentAmount">收款金额 (万元) *</label>
                            <input type="number" id="editPaymentAmount" step="0.01" min="0" required placeholder="请输入收款金额">
                        </div>
                        <div class="form-group">
                            <label for="editPaymentDate">收款日期 *</label>
                            <input type="date" id="editPaymentDate" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editInvoiceEntity">开票主体</label>
                            <input type="text" id="editInvoiceEntity" placeholder="请输入开票主体">
                        </div>
                        <div class="form-group">
                            <label for="editContractRevenue">合同收入 (万元)</label>
                            <input type="number" id="editContractRevenue" step="0.01" min="0" placeholder="请输入合同收入">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editContractFinalPayment">合同尾款 (万元)</label>
                            <input type="number" id="editContractFinalPayment" step="0.01" min="0" placeholder="请输入合同尾款">
                        </div>
                        <div class="form-group">
                            <label for="editInvoiceRevenue">开票收入 (万元)</label>
                            <input type="number" id="editInvoiceRevenue" step="0.01" min="0" placeholder="请输入开票收入">
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn-cancel" onclick="closeEditModal()">取消</button>
                        <button type="submit" class="btn-submit">更新回款记录</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="navbar.js"></script>
    <script src="js/auth.js"></script>
    <script src="payment-records.js"></script>
</body>
</html>
