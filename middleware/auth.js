const jwt = require('jsonwebtoken');
const mongoManager = require('../utils/mongoManager');

// JWT密钥（在生产环境中应该使用环境变量）
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// 生成JWT token
function generateToken(user) {
    return jwt.sign(
        {
            id: user.id,
            username: user.username,
            name: user.name,
            permission: user.permission,
            customers: user.customers || []
        },
        JWT_SECRET,
        { expiresIn: '24h' }
    );
}

// 验证JWT token
function verifyToken(token) {
    try {
        return jwt.verify(token, JWT_SECRET);
    } catch (error) {
        return null;
    }
}

// 认证中间件
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
        return res.status(401).json({
            success: false,
            message: '访问被拒绝，需要登录'
        });
    }

    const user = verifyToken(token);
    if (!user) {
        return res.status(403).json({
            success: false,
            message: 'Token无效或已过期'
        });
    }

    req.user = user;
    next();
}

// 管理员权限检查中间件
function requireAdmin(req, res, next) {
    if (req.user.permission !== 'admin') {
        return res.status(403).json({
            success: false,
            message: '需要管理员权限'
        });
    }
    next();
}

// 检查用户是否有权限访问特定客户数据
function checkCustomerAccess(req, res, next) {
    // 从不同的地方获取客户名称
    const customerName = req.params.customerName || req.params.customer || req.body.customerName;

    // 如果是URL编码的，需要解码
    const decodedCustomerName = customerName ? decodeURIComponent(customerName) : null;

    // 管理员有所有权限
    if (req.user.permission === 'admin') {
        return next();
    }

    // 商务经理只能访问自己负责的客户
    if (req.user.permission === 'manager') {
        // 从JWT token中获取用户的客户列表
        const userCustomers = req.user.customers || [];

        // 如果没有客户名称，允许通过（可能是列表请求，会在后续过滤）
        if (!decodedCustomerName) {
            return next();
        }

        // 检查客户是否在负责人的客户列表中
        if (!userCustomers.includes(decodedCustomerName)) {
            return res.status(403).json({
                success: false,
                message: `无权限访问客户"${decodedCustomerName}"的信息`
            });
        }
    }

    next();
}

// 用户登录验证
async function authenticateUser(username, password) {
    try {
        // 检查是否是root管理员
        if (username === 'admin' && password === 'admin123') {
            return {
                id: 0,
                username: 'admin',
                name: '系统管理员',
                permission: 'admin'
            };
        }

        // 从MongoDB检查商务经理账号
        const manager = await mongoManager.authenticateBusinessManager(username, password);

        if (manager) {
            return {
                id: manager.id,
                username: manager.username,
                name: manager.name,
                permission: manager.permission,
                customers: manager.customers
            };
        }

        return null;
    } catch (error) {
        console.error('用户认证失败:', error);
        return null;
    }
}

// 获取用户有权限访问的客户列表
async function getUserAccessibleCustomers(user) {
    try {
        if (user.permission === 'admin') {
            // 管理员可以访问所有客户
            const salesData = await mongoManager.loadSalesData();
            return salesData.map(record => record.customerName);
        } else if (user.permission === 'manager') {
            // 商务经理只能访问自己负责的客户
            const manager = await mongoManager.findBusinessManagerByUsername(user.username);

            if (manager && manager.customers && Array.isArray(manager.customers)) {
                return manager.customers;
            } else {
                console.error('商务经理客户列表无效:', user.username, manager?.customers);
                return [];
            }
        }

        return [];
    } catch (error) {
        console.error('获取用户可访问客户列表失败:', error);
        return [];
    }
}

// 过滤数据，只返回用户有权限访问的数据
async function filterDataByPermission(data, user, customerField = 'customerName') {
    if (user.permission === 'admin') {
        return data;
    }

    const accessibleCustomers = await getUserAccessibleCustomers(user);

    // 确保 accessibleCustomers 是数组
    if (!Array.isArray(accessibleCustomers)) {
        console.error('权限过滤失败 - 可访问客户列表不是数组:', user.username, accessibleCustomers);
        return [];
    }

    return data.filter(item => accessibleCustomers.includes(item[customerField]));
}

module.exports = {
    generateToken,
    verifyToken,
    authenticateToken,
    requireAdmin,
    checkCustomerAccess,
    authenticateUser,
    getUserAccessibleCustomers,
    filterDataByPermission
};
