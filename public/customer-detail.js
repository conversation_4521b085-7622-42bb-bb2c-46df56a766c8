// 全局变量
let currentCustomer = null;
let currentProjects = [];
let editingProjectId = null;

// 获取当前用户有权限访问的第一个客户
async function getFirstAccessibleCustomer() {
    try {
        const token = localStorage.getItem('authToken');
        if (!token) {
            throw new Error('用户未登录');
        }

        const response = await fetch('/api/sales', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const result = await response.json();
        if (result.success && result.data && result.data.length > 0) {
            // 返回第一个客户的名称
            return result.data[0].customerName;
        }

        return null;
    } catch (error) {
        console.error('获取客户列表失败:', error);
        throw error;
    }
}

// 加载客户列表到下拉框
async function loadCustomerList() {
    try {
        const token = localStorage.getItem('authToken');
        if (!token) {
            throw new Error('用户未登录');
        }

        const response = await fetch('/api/sales', {
            headers: {
                'Authorization': `Bear<PERSON> ${token}`
            }
        });

        const result = await response.json();
        if (result.success && result.data) {
            const customerSelect = document.getElementById('customerSelect');
            customerSelect.innerHTML = '';

            // 添加客户选项
            result.data.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.customerName;
                option.textContent = customer.customerName;
                customerSelect.appendChild(option);
            });

            // 设置当前选中的客户
            const urlParams = new URLSearchParams(window.location.search);
            const currentCustomer = urlParams.get('customer');
            if (currentCustomer) {
                customerSelect.value = decodeURIComponent(currentCustomer);
            }
        }
    } catch (error) {
        console.error('加载客户列表失败:', error);
        const customerSelect = document.getElementById('customerSelect');
        customerSelect.innerHTML = '<option value="">加载失败</option>';
    }
}

// 切换客户
function switchCustomer() {
    const customerSelect = document.getElementById('customerSelect');
    const selectedCustomer = customerSelect.value;

    if (selectedCustomer) {
        // 跳转到选中的客户详情页面
        window.location.href = `/customer-detail.html?customer=${encodeURIComponent(selectedCustomer)}`;
    }
}

// 列配置相关变量
let columnConfig = {
    columns: [],
    order: [],
    visible: {}
};

// 默认列配置
const defaultColumns = [
    { key: 'select', label: '选择', fixed: true, visible: true, sortable: false },
    { key: 'id', label: '项目ID', fixed: false, visible: true, sortable: true },
    { key: 'projectName', label: '项目名称', fixed: true, visible: true, sortable: true },
    { key: 'productType', label: '产品类型', fixed: false, visible: true, sortable: true },
    { key: 'expectedRevenue', label: '预计收入(万元)', fixed: false, visible: true, sortable: true },
    { key: 'projectStage', label: '项目阶段', fixed: false, visible: true, sortable: true },
    { key: 'percentage', label: '完成度', fixed: false, visible: true, sortable: true },
    { key: 'expectedSales', label: '预计销售额(万元)', fixed: false, visible: true, sortable: true },
    { key: 'actualPayment', label: '实际回款(万元)', fixed: false, visible: true, sortable: true },
    { key: 'actualSales', label: '实际销售额(万元)', fixed: false, visible: true, sortable: true },
    { key: 'expectedSignMonth', label: '预计签约月份', fixed: false, visible: true, sortable: true },
    { key: 'quarter', label: '季度', fixed: false, visible: true, sortable: true },
    { key: 'actualNegotiationTime', label: '实际谈判时间', fixed: false, visible: true, sortable: true },
    { key: 'actualSignTime', label: '实际签约时间', fixed: false, visible: true, sortable: true },
    { key: 'invoiceDate', label: '开票日期', fixed: false, visible: true, sortable: true },
    { key: 'acceptanceTime', label: '验收时间', fixed: false, visible: true, sortable: true },
    { key: 'paymentTime', label: '回款时间', fixed: false, visible: true, sortable: true },
    { key: 'paymentAmount', label: '回款金额(万元)', fixed: false, visible: true, sortable: true },
    { key: 'realtimePaymentData', label: '实时收款数据', fixed: false, visible: true, sortable: true },
    { key: 'paymentRemarks', label: '回款备注', fixed: false, visible: true, sortable: true },
    { key: 'partners', label: '合作伙伴', fixed: false, visible: true, sortable: true },
    { key: 'competitors', label: '竞争对手', fixed: false, visible: true, sortable: true },
    { key: 'projectDepartment', label: '项目发起部门', fixed: false, visible: true, sortable: true },
    { key: 'departmentHead', label: '部门负责人', fixed: false, visible: true, sortable: true },
    { key: 'departmentLeader', label: '部门领导', fixed: false, visible: true, sortable: true },
    { key: 'companyLeader', label: '公司分管领导', fixed: false, visible: true, sortable: true },
    { key: 'actions', label: '操作', fixed: true, visible: true, sortable: false }
];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    // 直接检查localStorage中的认证信息
    const token = localStorage.getItem('authToken');
    const userInfoStr = localStorage.getItem('userInfo');

    if (!token || !userInfoStr) {
        console.log('用户未认证，跳转到登录页面');
        window.location.href = '/login.html';
        return;
    }

    let userInfo;
    try {
        userInfo = JSON.parse(userInfoStr);
        console.log('用户信息解析成功:', userInfo);
    } catch (error) {
        console.error('解析用户信息失败:', error);
        window.location.href = '/login.html';
        return;
    }

    // 从URL参数获取客户名称
    const urlParams = new URLSearchParams(window.location.search);
    const rawCustomerName = urlParams.get('customer');
    let customerName = rawCustomerName ? decodeURIComponent(rawCustomerName) : null;

    // 如果没有指定客户名称，自动选择第一个有权限的客户
    if (!customerName) {
        try {
            customerName = await getFirstAccessibleCustomer();
            if (!customerName) {
                alert('没有可访问的客户数据');
                window.location.href = '/sales-dashboard.html';
                return;
            }
            // 更新URL，但不刷新页面
            const newUrl = new URL(window.location);
            newUrl.searchParams.set('customer', encodeURIComponent(customerName));
            window.history.replaceState({}, '', newUrl);
        } catch (error) {
            console.error('获取客户列表失败:', error);
            alert('获取客户列表失败');
            window.location.href = '/sales-dashboard.html';
            return;
        }
    }

    // 检查用户是否有权限访问该客户
    let hasAccess = false;

    // 详细的权限检查逻辑
    if (!userInfo || !userInfo.permission) {
        console.error('用户信息无效:', userInfo);
        alert('用户信息无效，请重新登录');
        window.location.href = '/login.html';
        return;
    }

    if (userInfo.permission === 'admin') {
        hasAccess = true;
        console.log('管理员权限，允许访问');
    } else if (userInfo.permission === 'manager') {
        if (!userInfo.customers || !Array.isArray(userInfo.customers)) {
            console.error('商务经理缺少客户列表:', userInfo);
            alert('用户权限配置错误，请联系管理员');
            window.location.href = '/sales-dashboard.html';
            return;
        }

        hasAccess = userInfo.customers.includes(customerName);
        console.log('商务经理权限检查:', {
            customerName: customerName,
            userCustomers: userInfo.customers,
            hasAccess: hasAccess
        });
    } else {
        console.error('未知的用户权限:', userInfo.permission);
        alert('用户权限无效，请联系管理员');
        window.location.href = '/login.html';
        return;
    }

    if (!hasAccess) {
        console.error('权限检查失败:', {
            customerName: customerName,
            userPermission: userInfo.permission,
            userCustomers: userInfo.customers,
            hasAccess: hasAccess
        });
        alert(`您没有权限访问客户"${customerName}"的信息`);
        window.location.href = '/sales-dashboard.html';
        return;
    }

    console.log('权限检查通过，开始加载客户数据');

    // 初始化列配置
    initializeColumnConfig();

    loadCustomerData(customerName);

    // 绑定事件
    document.getElementById('stageFilter').addEventListener('change', loadProjects);
    document.getElementById('productTypeFilter').addEventListener('change', loadProjects);
    document.getElementById('sortBy').addEventListener('change', loadProjects);
    document.getElementById('sortOrder').addEventListener('change', loadProjects);
    document.getElementById('projectForm').addEventListener('submit', handleFormSubmit);
});



// 加载客户基本信息
async function loadCustomerData(customerName) {
    try {
        const token = localStorage.getItem('authToken');
        if (!token) {
            throw new Error('用户未登录');
        }

        const response = await fetch(`/api/customers/${encodeURIComponent(customerName)}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            currentCustomer = result.data;
            updateCustomerInfo();
            loadProjects();
            // 加载客户列表到下拉框
            loadCustomerList();
        } else {
            throw new Error(result.message || '加载客户数据失败');
        }
    } catch (error) {
        console.error('加载客户数据失败:', error);

        // 如果是登录过期错误
        if (error.message.includes('登录已过期') || error.message.includes('用户未登录')) {
            alert('登录已过期，请重新登录');
            authManager.redirectToLogin();
            return;
        }

        // 如果是权限错误
        if (error.message.includes('权限')) {
            alert('您没有权限访问该客户信息');
            window.location.href = '/sales-dashboard.html';
            return;
        }

        alert('加载客户数据失败: ' + error.message);
    }
}

// 更新客户基本信息显示
function updateCustomerInfo() {
    if (!currentCustomer) return;
    
    document.getElementById('customerName').textContent = currentCustomer.customerName;
    document.getElementById('customerRegion').textContent = `区域: ${currentCustomer.region}`;
    document.getElementById('totalProjects').textContent = `项目数: ${currentCustomer.summary.totalProjects}`;
    
    // 更新统计数据
    const summary = currentCustomer.summary;
    document.getElementById('totalExpectedRevenue').textContent = summary.totalExpectedRevenue.toFixed(2);
    // document.getElementById('totalActualPayment').textContent = summary.totalActualPayment.toFixed(2);
    document.getElementById('totalActualSales').textContent = summary.totalActualSales.toFixed(2);
    document.getElementById('salesRate').textContent = summary.salesRate.toFixed(2) + '%';
    
    // 更新页面标题
    document.title = `${currentCustomer.customerName} - 客户详细信息`;
}

// 加载项目数据
async function loadProjects() {
    if (!currentCustomer) return;
    
    const loading = document.getElementById('loading');
    const table = document.getElementById('projectsTable');
    
    loading.style.display = 'block';
    table.style.display = 'none';
    
    try {
        const stage = document.getElementById('stageFilter').value;
        const productType = document.getElementById('productTypeFilter').value;
        const sortBy = document.getElementById('sortBy').value;
        const sortOrder = document.getElementById('sortOrder').value;
        
        const params = new URLSearchParams({
            sortBy,
            order: sortOrder
        });
        
        if (stage) params.append('stage', stage);
        if (productType) params.append('productType', productType);
        
        const token = localStorage.getItem('authToken');
        if (!token) {
            throw new Error('用户未登录');
        }

        const response = await fetch(`/api/customers/${encodeURIComponent(currentCustomer.customerName)}/projects?${params}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        
        if (result.success) {
            currentProjects = result.data;
            updateProductTypeFilter();
            renderProjectsTable(result.data);
            
            // 更新汇总数据（可能因为过滤而变化）
            if (result.summary) {
                currentCustomer.summary = result.summary;
                updateCustomerInfo();
            }
            
            loading.style.display = 'none';
            table.style.display = 'table';
        } else {
            throw new Error(result.message || '加载项目数据失败');
        }
    } catch (error) {
        console.error('加载项目数据失败:', error);
        loading.textContent = '加载项目数据失败: ' + error.message;
    }
}

// 更新产品类型过滤器
function updateProductTypeFilter() {
    const productTypeFilter = document.getElementById('productTypeFilter');
    const currentValue = productTypeFilter.value;
    
    // 清空现有选项（保留"全部类型"）
    productTypeFilter.innerHTML = '<option value="">全部类型</option>';
    
    // 获取所有产品类型
    const productTypes = [...new Set(currentProjects.map(p => p.productType).filter(type => type))];
    
    productTypes.forEach(type => {
        const option = document.createElement('option');
        option.value = type;
        option.textContent = type;
        productTypeFilter.appendChild(option);
    });
    
    // 恢复之前的选择
    productTypeFilter.value = currentValue;
}

// 渲染项目表格
function renderProjectsTable(projects) {
    // 首先应用列配置到表头
    applyColumnConfig();

    // 使用配置渲染表格内容
    renderProjectsTableWithConfig(projects);
}

// 编辑项目
async function editProject(projectId) {
    const project = currentProjects.find(p => p.id === projectId);
    if (!project) {
        alert('项目不存在');
        return;
    }
    
    editingProjectId = projectId;
    
    // 填充表单
    document.getElementById('modalTitle').textContent = '编辑项目';
    document.getElementById('editProjectName').value = project.projectName;
    document.getElementById('editProductType').value = project.productType;
    document.getElementById('editExpectedRevenue').value = project.expectedRevenue;
    document.getElementById('editProjectStage').value = project.projectStage;
    document.getElementById('editPercentage').value = project.percentage;
    document.getElementById('editExpectedSales').value = project.expectedSales;
    document.getElementById('editActualPayment').value = project.actualPayment;
    document.getElementById('editActualSales').value = project.actualSales;
    document.getElementById('editExpectedSignMonth').value = project.expectedSignMonth;
    document.getElementById('editQuarter').value = project.quarter;

    // 新增字段：时间相关
    document.getElementById('editActualNegotiationTime').value = project.actualNegotiationTime || '';
    document.getElementById('editActualSignTime').value = project.actualSignTime || '';
    document.getElementById('editInvoiceDate').value = project.invoiceDate || '';
    document.getElementById('editAcceptanceTime').value = project.acceptanceTime || '';
    document.getElementById('editPaymentTime').value = project.paymentTime || '';

    // 新增字段：回款相关
    document.getElementById('editPaymentAmount').value = project.paymentAmount || '';
    document.getElementById('editPaymentRemarks').value = project.paymentRemarks || '';
    document.getElementById('editRealtimePaymentData').value = project.realtimePaymentData || '';

    // 新增字段：合作与竞争
    document.getElementById('editPartners').value = project.partners || '';
    document.getElementById('editCompetitors').value = project.competitors || '';

    // 原有字段
    document.getElementById('editProjectDepartment').value = project.projectDepartment;
    document.getElementById('editDepartmentHead').value = project.departmentHead;
    document.getElementById('editDepartmentLeader').value = project.departmentLeader;
    document.getElementById('editCompanyLeader').value = project.companyLeader;
    
    document.getElementById('editModal').classList.add('show');

    // 绑定预计销售额自动计算事件
    bindExpectedSalesCalculation();
}

// 绑定预计销售额自动计算
function bindExpectedSalesCalculation() {
    const expectedRevenueInput = document.getElementById('editExpectedRevenue');
    const percentageInput = document.getElementById('editPercentage');
    const expectedSalesInput = document.getElementById('editExpectedSales');

    function calculateExpectedSales() {
        const expectedRevenue = parseFloat(expectedRevenueInput.value) || 0;
        const percentage = parseFloat(percentageInput.value) || 0;
        const expectedSales = Math.round(expectedRevenue * percentage * 100) / 100;
        expectedSalesInput.value = expectedSales.toFixed(2);
    }

    expectedRevenueInput.addEventListener('input', calculateExpectedSales);
    percentageInput.addEventListener('input', calculateExpectedSales);
}

// 删除项目
async function deleteProject(projectId) {
    const project = currentProjects.find(p => p.id === projectId);
    if (!project) {
        alert('项目不存在');
        return;
    }

    if (!confirm(`确定要删除项目"${project.projectName}"吗？此操作不可撤销。`)) {
        return;
    }

    try {
        const token = localStorage.getItem('authToken');
        if (!token) {
            throw new Error('用户未登录');
        }

        const response = await fetch(`/api/customers/${encodeURIComponent(currentCustomer.customerName)}/projects/${projectId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            alert('项目删除成功');

            // 更新汇总数据
            if (result.summary) {
                currentCustomer.summary = result.summary;
                updateCustomerInfo();
            }

            loadProjects();
        } else {
            throw new Error(result.message || '删除失败');
        }
    } catch (error) {
        console.error('删除失败:', error);
        alert('删除失败: ' + error.message);
    }
}

// 显示添加项目模态框
function showAddModal() {
    editingProjectId = null;
    document.getElementById('modalTitle').textContent = '添加项目';
    document.getElementById('projectForm').reset();
    document.getElementById('editModal').classList.add('show');

    // 绑定预计销售额自动计算事件
    bindExpectedSalesCalculation();
}

// 处理表单提交
async function handleFormSubmit(event) {
    event.preventDefault();
    
    const formData = {
        projectName: document.getElementById('editProjectName').value,
        productType: document.getElementById('editProductType').value,
        expectedRevenue: parseFloat(document.getElementById('editExpectedRevenue').value),
        projectStage: document.getElementById('editProjectStage').value,
        percentage: parseFloat(document.getElementById('editPercentage').value) || 0,
        expectedSales: parseFloat(document.getElementById('editExpectedSales').value) || 0,
        actualPayment: parseFloat(document.getElementById('editActualPayment').value) || 0,
        actualSales: parseFloat(document.getElementById('editActualSales').value) || 0,
        expectedSignMonth: document.getElementById('editExpectedSignMonth').value,
        quarter: document.getElementById('editQuarter').value,

        // 新增字段：时间相关
        actualNegotiationTime: document.getElementById('editActualNegotiationTime').value,
        actualSignTime: document.getElementById('editActualSignTime').value,
        invoiceDate: document.getElementById('editInvoiceDate').value,
        acceptanceTime: document.getElementById('editAcceptanceTime').value,
        paymentTime: document.getElementById('editPaymentTime').value,

        // 新增字段：回款相关
        paymentAmount: parseFloat(document.getElementById('editPaymentAmount').value) || 0,
        paymentRemarks: document.getElementById('editPaymentRemarks').value,
        realtimePaymentData: document.getElementById('editRealtimePaymentData').value,

        // 新增字段：合作与竞争
        partners: document.getElementById('editPartners').value,
        competitors: document.getElementById('editCompetitors').value,

        // 原有字段
        projectDepartment: document.getElementById('editProjectDepartment').value,
        departmentHead: document.getElementById('editDepartmentHead').value,
        departmentLeader: document.getElementById('editDepartmentLeader').value,
        companyLeader: document.getElementById('editCompanyLeader').value
    };
    
    try {
        const url = editingProjectId 
            ? `/api/customers/${encodeURIComponent(currentCustomer.customerName)}/projects/${editingProjectId}`
            : `/api/customers/${encodeURIComponent(currentCustomer.customerName)}/projects`;
        const method = editingProjectId ? 'PUT' : 'POST';
        
        const token = localStorage.getItem('authToken');
        if (!token) {
            throw new Error('用户未登录');
        }

        const response = await fetch(url, {
            method,
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert(editingProjectId ? '项目更新成功' : '项目添加成功');
            closeModal();
            
            // 更新汇总数据
            if (result.summary) {
                currentCustomer.summary = result.summary;
                updateCustomerInfo();
            }
            
            loadProjects();
        } else {
            throw new Error(result.message || '保存失败');
        }
    } catch (error) {
        console.error('保存失败:', error);
        alert('保存失败: ' + error.message);
    }
}

// 关闭模态框
function closeModal() {
    document.getElementById('editModal').classList.remove('show');
    editingProjectId = null;
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const projectCheckboxes = document.querySelectorAll('.project-checkbox');

    projectCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateDeleteButton();
}

// 更新删除按钮状态
function updateDeleteButton() {
    const selectedCheckboxes = document.querySelectorAll('.project-checkbox:checked');
    const deleteButton = document.getElementById('deleteSelectedBtn');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const allCheckboxes = document.querySelectorAll('.project-checkbox');

    // 更新删除按钮显示
    if (selectedCheckboxes.length > 0) {
        deleteButton.style.display = 'inline-block';
        deleteButton.textContent = `删除选中(${selectedCheckboxes.length})`;
    } else {
        deleteButton.style.display = 'none';
    }

    // 更新全选复选框状态
    if (selectedCheckboxes.length === allCheckboxes.length && allCheckboxes.length > 0) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else if (selectedCheckboxes.length > 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    }
}

// 批量删除选中的项目
async function deleteSelectedProjects() {
    const selectedCheckboxes = document.querySelectorAll('.project-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));

    if (selectedIds.length === 0) {
        alert('请先选择要删除的项目');
        return;
    }

    const projectNames = selectedIds.map(id => {
        const project = currentProjects.find(p => p.id === id);
        return project ? project.projectName : `项目${id}`;
    }).join('、');

    if (!confirm(`确定要删除以下 ${selectedIds.length} 个项目吗？\n\n${projectNames}\n\n此操作不可撤销。`)) {
        return;
    }

    try {
        // 逐个删除项目
        for (const projectId of selectedIds) {
            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('用户未登录');
            }

            const response = await fetch(`/api/customers/${encodeURIComponent(currentCustomer.customerName)}/projects/${projectId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(`删除项目${projectId}失败: ${result.message}`);
            }

            // 更新客户汇总数据（使用最后一次删除的结果）
            if (result.summary) {
                currentCustomer.summary = result.summary;
            }
        }

        alert(`成功删除 ${selectedIds.length} 个项目`);
        updateCustomerInfo();
        loadProjects();

    } catch (error) {
        console.error('批量删除失败:', error);
        alert('批量删除失败: ' + error.message);
        // 重新加载数据以确保一致性
        loadProjects();
    }
}

// 点击模态框外部关闭
document.getElementById('editModal').addEventListener('click', function(event) {
    if (event.target === this) {
        closeModal();
    }
});

// ==================== 列配置功能 ====================

// 初始化列配置
function initializeColumnConfig() {
    // 从localStorage加载配置，如果没有则使用默认配置
    const savedConfig = localStorage.getItem('customerDetailColumnConfig');
    if (savedConfig) {
        try {
            columnConfig = JSON.parse(savedConfig);
            // 确保配置完整性，添加新列
            ensureColumnConfigIntegrity();
        } catch (error) {
            console.error('解析列配置失败，使用默认配置:', error);
            resetColumnConfigToDefault();
        }
    } else {
        resetColumnConfigToDefault();
    }

    // 渲染列配置界面
    renderColumnConfig();
}

// 确保列配置完整性
function ensureColumnConfigIntegrity() {
    // 检查是否有新增的列
    const currentKeys = columnConfig.columns.map(col => col.key);
    const defaultKeys = defaultColumns.map(col => col.key);

    // 添加新列
    defaultKeys.forEach(key => {
        if (!currentKeys.includes(key)) {
            const defaultCol = defaultColumns.find(col => col.key === key);
            columnConfig.columns.push({ ...defaultCol });
            columnConfig.order.push(key);
            columnConfig.visible[key] = defaultCol.visible;
        }
    });

    // 移除不存在的列
    columnConfig.columns = columnConfig.columns.filter(col => defaultKeys.includes(col.key));
    columnConfig.order = columnConfig.order.filter(key => defaultKeys.includes(key));

    // 清理visible配置
    Object.keys(columnConfig.visible).forEach(key => {
        if (!defaultKeys.includes(key)) {
            delete columnConfig.visible[key];
        }
    });
}

// 重置为默认配置
function resetColumnConfigToDefault() {
    columnConfig = {
        columns: defaultColumns.map(col => ({ ...col })),
        order: defaultColumns.map(col => col.key),
        visible: {}
    };

    defaultColumns.forEach(col => {
        columnConfig.visible[col.key] = col.visible;
    });
}

// 渲染列配置界面
function renderColumnConfig() {
    const grid = document.getElementById('columnConfigGrid');
    grid.innerHTML = '';

    // 按照当前顺序渲染列配置项
    columnConfig.order.forEach(key => {
        const column = columnConfig.columns.find(col => col.key === key);
        if (!column) return;

        const item = document.createElement('div');
        item.className = `column-item ${column.fixed ? 'fixed' : ''}`;
        item.draggable = !column.fixed;
        item.dataset.key = key;

        item.innerHTML = `
            <input type="checkbox" class="column-checkbox"
                   ${columnConfig.visible[key] ? 'checked' : ''}
                   ${column.fixed ? 'disabled' : ''}
                   onchange="toggleColumnVisibility('${key}')">
            <span class="column-label">
                ${column.label} ${column.fixed ? '📌' : ''}
            </span>
            <span class="drag-handle" ${column.fixed ? 'style="opacity: 0.3;"' : ''}>⋮⋮</span>
        `;

        // 添加拖拽事件（仅非固定列）
        if (!column.fixed) {
            item.addEventListener('dragstart', handleDragStart);
            item.addEventListener('dragover', handleDragOver);
            item.addEventListener('drop', handleDrop);
            item.addEventListener('dragend', handleDragEnd);
        }

        grid.appendChild(item);
    });
}

// 拖拽相关变量
let draggedElement = null;

// 拖拽开始
function handleDragStart(e) {
    draggedElement = e.target;
    e.target.classList.add('dragging');
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', e.target.outerHTML);
}

// 拖拽经过
function handleDragOver(e) {
    if (e.preventDefault) {
        e.preventDefault();
    }
    e.dataTransfer.dropEffect = 'move';
    return false;
}

// 拖拽放下
function handleDrop(e) {
    if (e.stopPropagation) {
        e.stopPropagation();
    }

    if (draggedElement !== e.target && !e.target.classList.contains('fixed')) {
        const draggedKey = draggedElement.dataset.key;
        const targetKey = e.target.closest('.column-item').dataset.key;

        // 更新顺序
        const draggedIndex = columnConfig.order.indexOf(draggedKey);
        const targetIndex = columnConfig.order.indexOf(targetKey);

        // 移除拖拽的元素
        columnConfig.order.splice(draggedIndex, 1);
        // 插入到目标位置
        columnConfig.order.splice(targetIndex, 0, draggedKey);

        // 重新渲染
        renderColumnConfig();
    }

    return false;
}

// 拖拽结束
function handleDragEnd(e) {
    e.target.classList.remove('dragging');
    draggedElement = null;
}

// 切换列可见性
function toggleColumnVisibility(key) {
    columnConfig.visible[key] = !columnConfig.visible[key];
    // 立即应用到表格
    applyColumnConfig();
}

// 展开/收起列配置
function toggleColumnConfig() {
    const content = document.getElementById('columnConfigContent');
    const toggle = document.getElementById('columnConfigToggle');

    if (content.classList.contains('show')) {
        content.classList.remove('show');
        toggle.classList.remove('expanded');
    } else {
        content.classList.add('show');
        toggle.classList.add('expanded');
    }
}

// 全选列
function selectAllColumns() {
    columnConfig.columns.forEach(col => {
        if (!col.fixed) {
            columnConfig.visible[col.key] = true;
        }
    });
    renderColumnConfig();
    applyColumnConfig();
}

// 全不选列
function deselectAllColumns() {
    columnConfig.columns.forEach(col => {
        if (!col.fixed) {
            columnConfig.visible[col.key] = false;
        }
    });
    renderColumnConfig();
    applyColumnConfig();
}

// 重置列配置
function resetColumnConfig() {
    if (confirm('确定要重置为默认列配置吗？这将清除您的自定义设置。')) {
        resetColumnConfigToDefault();
        renderColumnConfig();
        applyColumnConfig();
    }
}

// 保存列配置
function saveColumnConfig() {
    try {
        localStorage.setItem('customerDetailColumnConfig', JSON.stringify(columnConfig));
        alert('列配置已保存');
    } catch (error) {
        console.error('保存列配置失败:', error);
        alert('保存列配置失败: ' + error.message);
    }
}

// 应用列配置到表格
function applyColumnConfig() {
    const table = document.getElementById('projectsTable');
    if (!table) return;

    const thead = table.querySelector('thead tr');
    if (!thead) return;

    // 重新生成表头
    generateTableHeader(thead);

    // 重新渲染表格内容
    if (currentProjects && currentProjects.length > 0) {
        renderProjectsTableWithConfig(currentProjects);
    }
}

// 生成表头
function generateTableHeader(thead) {
    thead.innerHTML = '';

    columnConfig.order.forEach(key => {
        if (!columnConfig.visible[key]) return;

        const column = columnConfig.columns.find(col => col.key === key);
        if (!column) return;

        const th = document.createElement('th');

        // 设置固定列样式
        if (column.key === 'projectName') {
            th.className = 'fixed-left';
            th.title = '此列固定显示';
        } else if (column.key === 'actions') {
            th.className = 'fixed-right';
            th.title = '此列固定显示';
        }

        // 设置内容
        if (column.key === 'select') {
            th.innerHTML = '<input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">';
        } else if (column.key === 'projectName') {
            th.innerHTML = `${column.label} 📌`;
        } else if (column.key === 'actions') {
            th.innerHTML = `${column.label} 📌`;
        } else {
            th.textContent = column.label;
        }

        thead.appendChild(th);
    });
}

// 使用列配置渲染项目表格
function renderProjectsTableWithConfig(projects) {
    const tbody = document.getElementById('projectsTableBody');
    tbody.innerHTML = '';

    projects.forEach(project => {
        const row = document.createElement('tr');

        columnConfig.order.forEach(key => {
            if (!columnConfig.visible[key]) return;

            const td = document.createElement('td');

            // 设置固定列样式
            if (key === 'projectName') {
                td.className = 'fixed-left';
                td.title = project.projectName;
            } else if (key === 'actions') {
                td.className = 'action-buttons fixed-right';
            }

            // 设置内容
            switch (key) {
                case 'select':
                    td.innerHTML = `<input type="checkbox" class="project-checkbox" value="${project.id}" onchange="updateDeleteButton()">`;
                    break;
                case 'id':
                    td.textContent = project.id;
                    break;
                case 'projectName':
                    td.textContent = project.projectName;
                    break;
                case 'productType':
                    td.innerHTML = `<span class="product-tag">${project.productType || '-'}</span>`;
                    break;
                case 'expectedRevenue':
                    td.className = 'number';
                    td.textContent = project.expectedRevenue.toFixed(2);
                    break;
                case 'projectStage':
                    td.innerHTML = `<span class="stage-tag stage-${project.projectStage}">${project.projectStage}</span>`;
                    break;
                case 'percentage':
                    const progressPercentage = (project.percentage * 100).toFixed(1);
                    td.innerHTML = `
                        <div>${progressPercentage}%</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progressPercentage}%"></div>
                        </div>
                    `;
                    break;
                case 'expectedSales':
                    td.className = 'number';
                    td.textContent = project.expectedSales.toFixed(2);
                    break;
                case 'actualPayment':
                    td.className = 'number';
                    td.textContent = project.actualPayment.toFixed(2);
                    break;
                case 'actualSales':
                    td.className = 'number';
                    td.textContent = project.actualSales.toFixed(2);
                    break;
                case 'expectedSignMonth':
                    td.textContent = project.expectedSignMonth || '-';
                    break;
                case 'quarter':
                    td.textContent = project.quarter || '-';
                    break;
                case 'actualNegotiationTime':
                    td.textContent = project.actualNegotiationTime || '-';
                    break;
                case 'actualSignTime':
                    td.textContent = project.actualSignTime || '-';
                    break;
                case 'invoiceDate':
                    td.textContent = project.invoiceDate || '-';
                    break;
                case 'acceptanceTime':
                    td.textContent = project.acceptanceTime || '-';
                    break;
                case 'paymentTime':
                    td.textContent = project.paymentTime || '-';
                    break;
                case 'paymentAmount':
                    td.className = 'number';
                    td.textContent = project.paymentAmount ? project.paymentAmount.toFixed(2) : '-';
                    break;
                case 'realtimePaymentData':
                    td.title = project.realtimePaymentData || '-';
                    td.textContent = project.realtimePaymentData || '-';
                    break;
                case 'paymentRemarks':
                    td.title = project.paymentRemarks || '-';
                    td.textContent = project.paymentRemarks || '-';
                    break;
                case 'partners':
                    td.title = project.partners || '-';
                    td.textContent = project.partners || '-';
                    break;
                case 'competitors':
                    td.title = project.competitors || '-';
                    td.textContent = project.competitors || '-';
                    break;
                case 'projectDepartment':
                    td.textContent = project.projectDepartment || '-';
                    break;
                case 'departmentHead':
                    td.textContent = project.departmentHead || '-';
                    break;
                case 'departmentLeader':
                    td.textContent = project.departmentLeader || '-';
                    break;
                case 'companyLeader':
                    td.textContent = project.companyLeader || '-';
                    break;
                case 'actions':
                    td.innerHTML = `
                        <button class="btn-edit" onclick="editProject(${project.id})">编辑</button>
                        <button class="btn-delete" onclick="deleteProject(${project.id})">删除</button>
                    `;
                    break;
            }

            row.appendChild(td);
        });

        tbody.appendChild(row);
    });
}
