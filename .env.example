# 环境配置示例文件
# 复制此文件为 .env (开发环境) 或 .env.production (生产环境)

# 环境类型 (development/production)
NODE_ENV=development

# MongoDB 配置
MONGODB_HOST=127.0.0.1
MONGODB_PORT=27017
MONGODB_DATABASE=up
MONGODB_USERNAME=
MONGODB_PASSWORD=

# 服务器配置
PORT=3000

# 日志级别 (debug/info/warn/error)
LOG_LEVEL=info

# 生产环境示例配置:
# NODE_ENV=production
# MONGODB_HOST=**************
# MONGODB_PORT=27017
# MONGODB_DATABASE=up
# MONGODB_USERNAME=mongodb
# MONGODB_PASSWORD=your_password_here
# PORT=3000
# LOG_LEVEL=info
