// 数据库配置管理
require('dotenv').config({
    path: process.env.NODE_ENV === 'production' ? '.env.production' : '.env'
});

const config = {
    // 环境配置
    environment: process.env.NODE_ENV || 'development',
    
    // 服务器配置
    port: process.env.PORT || 3000,
    
    // MongoDB 配置
    mongodb: {
        host: process.env.MONGODB_HOST || '127.0.0.1',
        port: process.env.MONGODB_PORT || 27017,
        database: process.env.MONGODB_DATABASE || 'up',
        username: process.env.MONGODB_USERNAME || '',
        password: process.env.MONGODB_PASSWORD || '',
        
        // 构建连接字符串
        getConnectionString() {
            const { host, port, database, username, password } = this;
            
            if (username && password) {
                // 有认证的连接字符串
                return `mongodb://${username}:${password}@${host}:${port}/${database}?authSource=admin`;
            } else {
                // 无认证的连接字符串（本地开发）
                return `mongodb://${host}:${port}/${database}`;
            }
        },
        
        // 获取连接选项
        getConnectionOptions() {
            const options = {
                serverSelectionTimeoutMS: 5000, // 5秒超时
                connectTimeoutMS: 10000, // 10秒连接超时
                socketTimeoutMS: 45000, // 45秒socket超时
            };
            
            // 生产环境额外配置
            if (this.isProduction()) {
                options.maxPoolSize = 10; // 最大连接池大小
                options.minPoolSize = 2;  // 最小连接池大小
                options.maxIdleTimeMS = 30000; // 最大空闲时间
                options.retryWrites = true; // 重试写入
                options.w = 'majority'; // 写入确认
            }
            
            return options;
        },
        
        // 判断是否为生产环境
        isProduction() {
            return process.env.NODE_ENV === 'production';
        },
        
        // 判断是否为开发环境
        isDevelopment() {
            return process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;
        }
    },
    
    // 日志配置
    logging: {
        level: process.env.LOG_LEVEL || 'info'
    }
};

// 验证必要的配置
function validateConfig() {
    const { mongodb } = config;
    
    if (!mongodb.host) {
        throw new Error('MONGODB_HOST is required');
    }
    
    if (!mongodb.database) {
        throw new Error('MONGODB_DATABASE is required');
    }
    
    // 生产环境必须有认证信息
    if (mongodb.isProduction() && (!mongodb.username || !mongodb.password)) {
        throw new Error('MONGODB_USERNAME and MONGODB_PASSWORD are required in production');
    }
    
    console.log('✅ 配置验证通过');
}

// 显示当前配置信息
function displayConfig() {
    const { mongodb } = config;
    
    console.log('🔧 当前环境配置:');
    console.log(`   环境: ${config.environment}`);
    console.log(`   端口: ${config.port}`);
    console.log(`   MongoDB主机: ${mongodb.host}:${mongodb.port}`);
    console.log(`   数据库: ${mongodb.database}`);
    console.log(`   认证: ${mongodb.username ? '是' : '否'}`);
    console.log(`   连接字符串: ${mongodb.getConnectionString().replace(/:([^:@]+)@/, ':***@')}`);
}

// 初始化配置
try {
    validateConfig();
    displayConfig();
} catch (error) {
    console.error('❌ 配置错误:', error.message);
    process.exit(1);
}

module.exports = config;
