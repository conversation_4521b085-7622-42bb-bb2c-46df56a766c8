const mongoManager = require('../utils/mongoManager');

async function testDeleteLogic() {
    try {
        console.log('🧪 测试删除逻辑（生产环境）...');
        
        // 设置生产环境
        process.env.NODE_ENV = 'production';
        
        // 连接生产环境MongoDB
        await mongoManager.connect();
        
        // 加载当前数据
        const salesData = await mongoManager.loadSalesData();
        const customerData = await mongoManager.loadCustomerData();
        
        console.log(`📊 当前销售记录: ${salesData.length} 条`);
        console.log(`👥 当前客户数据: ${Object.keys(customerData).length} 个`);
        
        // 检查数据一致性
        const validCustomerNames = new Set(salesData.map(record => record.customerName));
        const orphanedCustomers = Object.keys(customerData).filter(name => !validCustomerNames.has(name));
        
        if (orphanedCustomers.length > 0) {
            console.log(`❌ 发现 ${orphanedCustomers.length} 个孤立的客户数据:`);
            orphanedCustomers.forEach(name => console.log(`   - ${name}`));
        } else {
            console.log('✅ 数据一致性检查通过，没有孤立的客户数据');
        }
        
        // 检查是否还有证券通相关数据
        const zhengquantongSales = salesData.filter(record => 
            record.customerName && record.customerName.includes('证券通')
        );
        const zhengquantongCustomers = Object.keys(customerData).filter(name => 
            name.includes('证券通')
        );
        
        if (zhengquantongSales.length > 0 || zhengquantongCustomers.length > 0) {
            console.log('❌ 仍然存在证券通相关数据:');
            if (zhengquantongSales.length > 0) {
                console.log(`   销售记录: ${zhengquantongSales.length} 条`);
            }
            if (zhengquantongCustomers.length > 0) {
                console.log(`   客户数据: ${zhengquantongCustomers.length} 个`);
            }
        } else {
            console.log('✅ 证券通数据已完全清理');
        }
        
        // 显示当前所有客户名称（用于验证）
        console.log('\n📋 当前所有客户名称:');
        const allCustomers = [...validCustomerNames].sort();
        allCustomers.forEach((name, index) => {
            console.log(`   ${index + 1}. ${name}`);
        });
        
        console.log('\n🎉 删除逻辑测试完成！');
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 测试失败:', error);
        console.error('错误详情:', error.stack);
        process.exit(1);
    }
}

testDeleteLogic();
