# 环境配置说明

## 概述

本项目支持开发环境和生产环境的分离配置，通过环境变量管理不同环境的数据库连接和其他配置。

## 环境配置

### 开发环境
- **MongoDB地址**: 127.0.0.1:27017
- **数据库**: up
- **认证**: 无需认证
- **用途**: 本地开发和测试

### 生产环境
- **MongoDB地址**: **************:27017
- **数据库**: up
- **用户名**: mongodb
- **密码**: czc274033
- **用途**: 生产部署

## 配置文件

### .env (开发环境)
```env
NODE_ENV=development
MONGODB_HOST=127.0.0.1
MONGODB_PORT=27017
MONGODB_DATABASE=up
MONGODB_USERNAME=
MONGODB_PASSWORD=
PORT=3000
LOG_LEVEL=debug
```

### .env.production (生产环境)
```env
NODE_ENV=production
MONGODB_HOST=**************
MONGODB_PORT=27017
MONGODB_DATABASE=up
MONGODB_USERNAME=mongodb
MONGODB_PASSWORD=czc274033
PORT=3000
LOG_LEVEL=info
```

## 启动命令

### 开发环境
```bash
# 使用 npm scripts
npm start              # 开发环境启动
npm run dev            # 开发环境 + nodemon

# 直接使用 node
NODE_ENV=development node app.js
```

### 生产环境
```bash
# 使用 npm scripts
npm run start:prod     # 生产环境启动
npm run dev:prod       # 生产环境 + nodemon

# 直接使用 node
NODE_ENV=production node app.js
```

## 安全注意事项

1. **敏感信息保护**
   - `.env.production` 包含生产环境密码，不应提交到版本控制
   - 已在 `.gitignore` 中排除所有 `.env*` 文件

2. **密码管理**
   - 生产环境密码应定期更换
   - 使用强密码策略
   - 限制数据库用户权限

3. **网络安全**
   - 确保生产数据库服务器防火墙配置正确
   - 只允许必要的IP地址访问
   - 使用SSL/TLS加密连接（如需要）

## 配置验证

运行配置测试脚本：
```bash
# 测试当前环境配置
node scripts/testEnvironmentConfig.js

# 测试生产环境配置
NODE_ENV=production node scripts/testEnvironmentConfig.js
```

## 故障排除

### 连接失败
1. 检查网络连接
2. 验证主机地址和端口
3. 确认用户名和密码
4. 检查防火墙设置

### 认证失败
1. 验证用户名和密码
2. 确认用户权限
3. 检查数据库用户是否存在

### 环境变量问题
1. 确认 `.env` 文件存在
2. 检查环境变量格式
3. 验证 NODE_ENV 设置

## 部署建议

### 开发环境
- 使用本地MongoDB实例
- 启用详细日志记录
- 允许MongoDB连接失败时使用JSON文件存储

### 生产环境
- 使用远程MongoDB服务器
- 启用连接池和重试机制
- MongoDB连接失败时停止服务器启动
- 使用适当的日志级别

## 文件结构

```
├── .env                    # 开发环境配置
├── .env.production         # 生产环境配置 (不提交到git)
├── .env.example           # 配置模板
├── config/
│   └── database.js        # 数据库配置管理
├── utils/
│   └── mongoManager.js    # MongoDB连接管理
└── scripts/
    └── testEnvironmentConfig.js  # 配置测试脚本
```
