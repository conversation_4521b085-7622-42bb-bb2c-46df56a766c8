// 商务经理数据迁移脚本 - 从JSON文件迁移到MongoDB
const fs = require('fs');
const path = require('path');
const mongoManager = require('../utils/mongoManager');

async function migrateBusinessManagersData() {
    console.log('🔄 开始迁移商务经理数据到MongoDB...');
    console.log('=' .repeat(60));

    try {
        // 连接到MongoDB
        await mongoManager.connect();
        console.log('✅ MongoDB连接成功');

        // 读取JSON文件中的商务经理数据
        const jsonFilePath = path.join(__dirname, '..', 'data', 'business_managers.json');
        
        if (!fs.existsSync(jsonFilePath)) {
            console.log('⚠️  JSON文件不存在，创建默认商务经理数据');
            
            // 创建默认的商务经理数据
            const defaultManagers = [
                {
                    id: 1,
                    name: "张三",
                    username: "<PERSON><PERSON><PERSON>",
                    password: "123456",
                    permission: "manager",
                    customers: ["客户A", "客户B"]
                },
                {
                    id: 2,
                    name: "李四",
                    username: "lisi",
                    password: "123456",
                    permission: "manager",
                    customers: ["客户C", "客户D"]
                },
                {
                    id: 3,
                    name: "王五",
                    username: "wangwu",
                    password: "123456",
                    permission: "manager",
                    customers: ["客户E"]
                }
            ];

            // 保存到MongoDB
            const success = await mongoManager.saveBusinessManagersData(defaultManagers);
            if (success) {
                console.log(`✅ 默认商务经理数据已保存到MongoDB: ${defaultManagers.length} 条记录`);
                
                // 创建JSON文件备份
                fs.writeFileSync(jsonFilePath, JSON.stringify(defaultManagers, null, 2));
                console.log('📄 已创建JSON文件备份');
            } else {
                throw new Error('保存默认数据失败');
            }
        } else {
            // 读取现有JSON文件
            const jsonData = fs.readFileSync(jsonFilePath, 'utf8');
            const businessManagers = JSON.parse(jsonData);
            
            console.log(`📄 从JSON文件读取到 ${businessManagers.length} 条商务经理记录`);

            // 检查MongoDB中是否已有数据
            const existingData = await mongoManager.loadBusinessManagersData();
            
            if (existingData.length > 0) {
                console.log(`⚠️  MongoDB中已存在 ${existingData.length} 条商务经理记录`);
                console.log('');
                console.log('现有MongoDB数据:');
                existingData.forEach((manager, index) => {
                    console.log(`   ${index + 1}. ${manager.name} (${manager.username}) - ${manager.customers?.length || 0} 个客户`);
                });
                
                console.log('');
                console.log('JSON文件数据:');
                businessManagers.forEach((manager, index) => {
                    console.log(`   ${index + 1}. ${manager.name} (${manager.username}) - ${manager.customers?.length || 0} 个客户`);
                });
                
                console.log('');
                console.log('🔄 将用JSON文件数据覆盖MongoDB数据...');
            }

            // 保存到MongoDB（会覆盖现有数据）
            const success = await mongoManager.saveBusinessManagersData(businessManagers);
            
            if (success) {
                console.log(`✅ 商务经理数据已成功迁移到MongoDB: ${businessManagers.length} 条记录`);
                
                // 创建备份文件
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const backupPath = path.join(__dirname, '..', 'data', `business_managers_backup_${timestamp}.json`);
                fs.copyFileSync(jsonFilePath, backupPath);
                console.log(`📦 已创建JSON文件备份: ${path.basename(backupPath)}`);
            } else {
                throw new Error('迁移失败');
            }
        }

        // 验证迁移结果
        console.log('');
        console.log('🔍 验证迁移结果...');
        const verifyData = await mongoManager.loadBusinessManagersData();
        
        console.log(`✅ MongoDB中现有 ${verifyData.length} 条商务经理记录:`);
        verifyData.forEach((manager, index) => {
            console.log(`   ${index + 1}. ${manager.name} (${manager.username})`);
            console.log(`      权限: ${manager.permission}`);
            console.log(`      负责客户: ${manager.customers?.join(', ') || '无'}`);
            console.log('');
        });

        // 测试登录功能
        console.log('🧪 测试登录功能...');
        
        // 测试管理员登录
        const adminAuth = await mongoManager.authenticateBusinessManager('admin', 'admin123');
        console.log(`管理员登录测试: ${adminAuth ? '❌ 不应该在商务经理表中找到' : '✅ 正确'}`);
        
        // 测试商务经理登录
        if (verifyData.length > 0) {
            const testManager = verifyData[0];
            const managerAuth = await mongoManager.authenticateBusinessManager(testManager.username, testManager.password);
            console.log(`商务经理登录测试 (${testManager.username}): ${managerAuth ? '✅ 成功' : '❌ 失败'}`);
        }

        console.log('');
        console.log('🎉 商务经理数据迁移完成！');
        console.log('');
        console.log('📝 后续步骤:');
        console.log('   1. 重启应用服务器');
        console.log('   2. 测试登录功能');
        console.log('   3. 确认所有商务经理可以正常登录');
        console.log('   4. 验证权限控制是否正常工作');

    } catch (error) {
        console.error('❌ 迁移失败:', error.message);
        console.error('');
        console.error('💡 故障排除建议:');
        console.error('   1. 检查MongoDB服务是否运行');
        console.error('   2. 验证数据库连接配置');
        console.error('   3. 确认有足够的数据库权限');
        console.error('   4. 检查JSON文件格式是否正确');
        
        process.exit(1);
    } finally {
        // 关闭MongoDB连接
        await mongoManager.disconnect();
    }
}

// 运行迁移
if (require.main === module) {
    migrateBusinessManagersData();
}

module.exports = { migrateBusinessManagersData };
