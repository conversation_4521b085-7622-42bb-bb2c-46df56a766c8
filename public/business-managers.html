<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商务负责人管理 - 销售漏斗管理系统</title>
    <link rel="stylesheet" href="css/modal-responsive.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .control-group label {
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
        }

        select, input {
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-add {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .btn-edit {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .btn-delete {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .table-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-admin {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-manager {
            background: #d4edda;
            color: #155724;
        }

        .department-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .customers-list {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .customers-count {
            background: #e3f2fd;
            color: #1976d2;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #666;
            font-size: 1.1rem;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal h3 {
            color: #667eea;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
        }

        .btn-cancel {
            background: #6c757d;
        }

        .customers-management {
            margin-top: 1rem;
        }

        .customers-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .customer-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .customer-tag .remove {
            cursor: pointer;
            font-weight: bold;
            color: #d32f2f;
        }

        .add-customer {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .add-customer input {
            flex: 1;
        }

        .add-customer button {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 商务负责人管理</h1>
            <p>管理销售团队成员信息和客户分配关系</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>权限筛选:</label>
                <select id="permissionFilter">
                    <option value="all">全部权限</option>
                    <option value="admin">管理员</option>
                    <option value="manager">经理</option>
                </select>
            </div>
            <div class="control-group">
                <label>搜索:</label>
                <input type="text" id="searchInput" placeholder="搜索姓名或账户名">
            </div>
            <button class="btn-add" onclick="showAddModal()">
                ➕ 添加负责人
            </button>
            <button onclick="loadData()">
                🔄 刷新数据
            </button>
        </div>

        <div class="table-container">
            <div id="loading" class="loading">正在加载数据...</div>
            <table id="managersTable" style="display: none;">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>姓名</th>
                        <th>账户名</th>
                        <th>权限</th>
                        <th>负责客户</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="managersTableBody">
                </tbody>
            </table>
        </div>
    </div>

    <!-- 添加/编辑模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <h3 id="modalTitle">添加商务负责人</h3>
            <form id="managerForm">
                <div class="form-group">
                    <label>姓名 *:</label>
                    <input type="text" id="editName" required placeholder="如：张经理">
                </div>
                <div class="form-group">
                    <label>账户名 *:</label>
                    <input type="text" id="editUsername" required placeholder="如：zhang_manager">
                </div>
                <div class="form-group">
                    <label>密码 *:</label>
                    <input type="password" id="editPassword" required placeholder="请输入密码">
                </div>
                <div class="form-group">
                    <label>权限 *:</label>
                    <select id="editPermission" required>
                        <option value="">请选择权限</option>
                        <option value="admin">管理员</option>
                        <option value="manager">经理</option>
                    </select>
                </div>
                <div class="form-group customers-management">
                    <label>负责客户:</label>
                    <div class="customers-tags" id="customersTags">
                        <!-- 客户标签将在这里动态生成 -->
                    </div>
                    <div class="add-customer">
                        <input type="text" id="newCustomerInput" placeholder="输入客户名称">
                        <button type="button" onclick="addCustomer()">添加</button>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-cancel" onclick="closeModal()">取消</button>
                    <button type="submit">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script src="navbar.js"></script>
    <script src="business-managers.js"></script>
</body>
</html>
