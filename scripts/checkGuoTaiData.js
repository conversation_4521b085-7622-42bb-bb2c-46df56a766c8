const mongoManager = require('../utils/mongoManager');

async function checkGuoTaiData() {
    try {
        console.log('🔍 检查国泰海通的项目数据...');
        
        // 设置生产环境
        process.env.NODE_ENV = 'production';
        
        // 连接生产环境MongoDB
        await mongoManager.connect();
        
        // 加载数据
        const customerData = await mongoManager.loadCustomerData();
        
        // 查找国泰海通的数据
        const guoTaiCustomer = customerData['国泰海通'];
        
        if (!guoTaiCustomer) {
            console.log('❌ 没有找到国泰海通的客户数据');
            process.exit(1);
        }
        
        console.log(`📋 国泰海通客户信息:`);
        console.log(`   项目数量: ${guoTaiCustomer.projects ? guoTaiCustomer.projects.length : 0}`);
        
        if (guoTaiCustomer.projects && guoTaiCustomer.projects.length > 0) {
            console.log('\n📊 项目详细信息:');
            
            guoTaiCustomer.projects.forEach((project, index) => {
                console.log(`\n   项目 ${index + 1}: ${project.projectName || '未命名'}`);
                console.log(`     - 项目阶段: ${project.projectStage || '未设置'}`);
                console.log(`     - 预期收入: ${project.expectedRevenue || '未设置'} 万元`);
                console.log(`     - 回款金额: ${project.paymentAmount || '未设置'} 万元`);
                console.log(`     - 回款时间: ${project.paymentTime || '未设置'}`);
                console.log(`     - 签约时间: ${project.actualSignTime || '未设置'}`);
                console.log(`     - 开票时间: ${project.invoiceTime || '未设置'}`);
                console.log(`     - 商谈招标结束时间: ${project.negotiationBiddingEndTime || '未设置'}`);
                
                // 检查是否符合已开票待回款条件（当前逻辑）
                const invoiceDate = project.invoiceTime ? new Date(project.invoiceTime) : null;
                const paymentDate = project.paymentTime ? new Date(project.paymentTime) : null;
                
                const isInvoicedPending = (invoiceDate && !paymentDate && !project.paymentAmount) || 
                                         (project.projectStage === '回收货款' && !project.paymentAmount);
                const shouldBeInvoicedPending = isInvoicedPending && project.expectedRevenue;
                
                console.log(`     - 当前逻辑是否符合已开票待回款: ${shouldBeInvoicedPending ? '✅ 是' : '❌ 否'}`);
                
                // 检查修正后的逻辑（必须有开票时间）
                const correctedIsInvoicedPending = invoiceDate && !paymentDate && !project.paymentAmount;
                const correctedShouldBeInvoicedPending = correctedIsInvoicedPending && project.expectedRevenue;
                
                console.log(`     - 修正逻辑是否符合已开票待回款: ${correctedShouldBeInvoicedPending ? '✅ 是' : '❌ 否'}`);
                
                if (shouldBeInvoicedPending !== correctedShouldBeInvoicedPending) {
                    console.log(`     ⚠️ 逻辑差异！当前逻辑和修正逻辑结果不同`);
                    console.log(`       当前逻辑原因:`);
                    console.log(`         - 条件1 (有开票时间且无回款): ${invoiceDate && !paymentDate && !project.paymentAmount ? '✅' : '❌'}`);
                    console.log(`         - 条件2 (项目阶段为回收货款且无回款): ${project.projectStage === '回收货款' && !project.paymentAmount ? '✅' : '❌'}`);
                    console.log(`       修正逻辑原因:`);
                    console.log(`         - 必须有开票时间: ${invoiceDate ? '✅' : '❌'}`);
                    console.log(`         - 无回款时间: ${!paymentDate ? '✅' : '❌'}`);
                    console.log(`         - 无回款金额: ${!project.paymentAmount ? '✅' : '❌'}`);
                }
                
                // 特别标注海通PC三期验收款
                if (project.projectName && project.projectName.includes('海通PC三期验收款')) {
                    console.log(`     🎯 这是用户提到的项目！`);
                    console.log(`     📝 用户要求：如果未设置开票日期，则不属于已开票未回款`);
                    console.log(`     📊 开票时间状态: ${project.invoiceTime ? `已设置 (${project.invoiceTime})` : '未设置'}`);
                    console.log(`     💡 按用户要求，此项目${project.invoiceTime ? '应该' : '不应该'}被统计为已开票待回款`);
                }
            });
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 检查失败:', error);
        console.error('错误详情:', error.stack);
        process.exit(1);
    }
}

checkGuoTaiData();
