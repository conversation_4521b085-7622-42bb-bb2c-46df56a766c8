const mongoManager = require('../utils/mongoManager');

async function deleteHaitongData() {
    try {
        console.log('🗑️ 开始删除海通证券数据...');
        
        // 连接MongoDB
        await mongoManager.connect();
        
        // 加载销售数据
        let salesData = await mongoManager.loadSalesData();
        console.log(`📊 删除前MongoDB中共有 ${salesData.length} 条销售数据`);
        
        // 查找海通证券记录
        const haitongIndex = salesData.findIndex(record => record.customerName === '海通证券');
        
        if (haitongIndex !== -1) {
            const haitongRecord = salesData[haitongIndex];
            console.log(`🎯 找到海通证券记录: ID=${haitongRecord.id}, 区域=${haitongRecord.region}`);
            
            // 删除销售记录
            salesData.splice(haitongIndex, 1);
            console.log('✅ 已从销售数据中删除海通证券');
            
            // 保存更新后的销售数据到MongoDB
            await mongoManager.saveSalesData(salesData);
            console.log('✅ 销售数据已保存到MongoDB');
            
            // 加载客户详细数据
            let customerData = await mongoManager.loadCustomerData();
            
            // 删除客户详细数据
            if (customerData['海通证券']) {
                delete customerData['海通证券'];
                console.log('✅ 已从客户详细数据中删除海通证券');
                
                // 保存更新后的客户数据到MongoDB
                await mongoManager.saveCustomerData(customerData);
                console.log('✅ 客户数据已保存到MongoDB');
            }
            
            console.log(`📊 删除后MongoDB中共有 ${salesData.length} 条销售数据`);
            console.log('🎉 海通证券数据删除完成！');
            
        } else {
            console.log('ℹ️ 没有找到海通证券记录');
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 删除失败:', error);
        process.exit(1);
    }
}

deleteHaitongData();
