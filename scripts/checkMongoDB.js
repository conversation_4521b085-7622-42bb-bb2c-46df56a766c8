const mongoManager = require('../utils/mongoManager');

async function checkMongoDB() {
    try {
        console.log('🔍 检查MongoDB连接状态...');
        console.log('=' .repeat(50));
        
        // 连接到MongoDB
        const db = await mongoManager.connect();
        
        // 检查数据库状态
        const admin = db.admin();
        const status = await admin.serverStatus();
        
        console.log('✅ MongoDB连接成功！');
        console.log(`📊 MongoDB版本: ${status.version}`);
        console.log(`🔗 连接地址: 127.0.0.1:27017`);
        console.log(`💾 数据库: up`);
        console.log(`⏰ 服务器时间: ${new Date(status.localTime).toLocaleString('zh-CN')}`);
        console.log(`🔄 运行时间: ${Math.floor(status.uptime / 3600)}小时 ${Math.floor((status.uptime % 3600) / 60)}分钟`);
        
        // 检查集合状态
        console.log('\n📋 集合状态:');
        const collections = [
            'sales_funnel_data',
            'customer_details_data', 
            'organization_data',
            'business_managers_data',
            'payment_records_data',
            'qa_data',
            'execution_plans'
        ];
        
        for (const collectionName of collections) {
            try {
                const collection = db.collection(collectionName);
                const count = await collection.countDocuments();
                console.log(`  📁 ${collectionName}: ${count} 条记录`);
            } catch (error) {
                console.log(`  ❌ ${collectionName}: 检查失败 - ${error.message}`);
            }
        }
        
        console.log('\n✅ MongoDB状态检查完成！');
        
    } catch (error) {
        console.error('❌ MongoDB连接失败:', error.message);
        console.log('\n💡 解决建议:');
        console.log('1. 确保MongoDB服务正在运行');
        console.log('2. 检查连接地址和端口是否正确 (127.0.0.1:27017)');
        console.log('3. 确保数据库"up"存在');
        console.log('4. 检查网络连接和防火墙设置');
    } finally {
        // 关闭MongoDB连接
        await mongoManager.disconnect();
        process.exit(0);
    }
}

// 运行检查
checkMongoDB();
