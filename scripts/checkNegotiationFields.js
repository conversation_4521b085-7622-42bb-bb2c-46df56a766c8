const mongoManager = require('../utils/mongoManager');

async function checkNegotiationFields() {
    try {
        console.log('🔍 检查项目数据中的商务谈判/中标时间相关字段...');
        
        // 设置生产环境
        process.env.NODE_ENV = 'production';
        
        // 连接生产环境MongoDB
        await mongoManager.connect();
        
        // 加载数据
        const customerData = await mongoManager.loadCustomerData();
        
        console.log(`👥 客户数据: ${Object.keys(customerData).length} 个`);
        
        // 统计所有可能的商务谈判相关字段
        let totalProjects = 0;
        let negotiationFieldStats = {};
        let projectsWithNegotiationData = [];
        
        // 可能的商务谈判字段名称
        const possibleNegotiationFields = [
            'negotiationBiddingEndTime', 'actualNegotiationTime', 'negotiationTime', 
            'biddingTime', 'actualBiddingTime', 'negotiationEndTime',
            '实际商务谈判时间', '中标时间', '商务谈判时间', '招标时间'
        ];
        
        Object.keys(customerData).forEach(customerName => {
            const customer = customerData[customerName];
            if (customer.projects && customer.projects.length > 0) {
                customer.projects.forEach(project => {
                    totalProjects++;
                    
                    // 检查所有字段
                    const projectFields = Object.keys(project);
                    let hasNegotiationField = false;
                    let negotiationFields = [];
                    
                    // 检查每个可能的商务谈判字段
                    possibleNegotiationFields.forEach(field => {
                        if (project[field] !== undefined && project[field] !== null && project[field] !== '') {
                            hasNegotiationField = true;
                            negotiationFields.push({
                                field: field,
                                value: project[field]
                            });
                            
                            if (!negotiationFieldStats[field]) {
                                negotiationFieldStats[field] = 0;
                            }
                            negotiationFieldStats[field]++;
                        }
                    });
                    
                    // 检查所有包含"negotiation"、"bidding"、"谈判"、"中标"的字段
                    projectFields.forEach(field => {
                        const fieldLower = field.toLowerCase();
                        if ((fieldLower.includes('negotiation') || fieldLower.includes('bidding') || 
                             field.includes('谈判') || field.includes('中标') || field.includes('招标')) 
                            && project[field] !== undefined && project[field] !== null && project[field] !== '') {
                            
                            if (!possibleNegotiationFields.includes(field)) {
                                hasNegotiationField = true;
                                negotiationFields.push({
                                    field: field,
                                    value: project[field]
                                });
                                
                                if (!negotiationFieldStats[field]) {
                                    negotiationFieldStats[field] = 0;
                                }
                                negotiationFieldStats[field]++;
                            }
                        }
                    });
                    
                    if (hasNegotiationField) {
                        projectsWithNegotiationData.push({
                            customer: customerName,
                            project: project.projectName || '未命名',
                            negotiationFields: negotiationFields,
                            projectStage: project.projectStage,
                            expectedRevenue: project.expectedRevenue
                        });
                    }
                    
                    // 显示前几个项目的相关字段（用于调试）
                    if (totalProjects <= 5) {
                        console.log(`\n📋 项目 ${totalProjects}: ${customerName} - ${project.projectName || '未命名'}`);
                        console.log(`   项目阶段: ${project.projectStage || '未设置'}`);
                        
                        // 显示所有时间相关字段
                        projectFields.forEach(field => {
                            const fieldLower = field.toLowerCase();
                            if (fieldLower.includes('time') || fieldLower.includes('date') || 
                                field.includes('时间') || field.includes('日期')) {
                                const value = project[field];
                                if (value !== undefined && value !== null && value !== '') {
                                    console.log(`     ${field}: ${value}`);
                                }
                            }
                        });
                    }
                });
            }
        });
        
        console.log(`\n📊 统计结果:`);
        console.log(`   总项目数: ${totalProjects}`);
        console.log(`   有商务谈判相关字段的项目: ${projectsWithNegotiationData.length}`);
        
        if (Object.keys(negotiationFieldStats).length > 0) {
            console.log(`\n📋 商务谈判字段统计:`);
            Object.keys(negotiationFieldStats).forEach(field => {
                console.log(`   ${field}: ${negotiationFieldStats[field]} 个项目`);
            });
        } else {
            console.log(`\n❌ 没有找到任何商务谈判相关字段`);
        }
        
        if (projectsWithNegotiationData.length > 0) {
            console.log(`\n📋 有商务谈判数据的项目:`);
            
            projectsWithNegotiationData.forEach((proj, index) => {
                console.log(`\n   ${index + 1}. ${proj.customer} - ${proj.project}`);
                console.log(`      项目阶段: ${proj.projectStage || '未设置'}`);
                console.log(`      预期收入: ${proj.expectedRevenue || '未设置'} 万元`);
                proj.negotiationFields.forEach(field => {
                    console.log(`      ${field.field}: ${field.value}`);
                });
                
                // 检查是否在近一周内
                const now = new Date();
                const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                
                proj.negotiationFields.forEach(field => {
                    try {
                        const fieldDate = new Date(field.value);
                        if (fieldDate >= oneWeekAgo) {
                            console.log(`      ✅ ${field.field} 在近一周内！`);
                        }
                    } catch (e) {
                        // 不是有效日期格式
                    }
                });
            });
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 检查失败:', error);
        console.error('错误详情:', error.stack);
        process.exit(1);
    }
}

checkNegotiationFields();
