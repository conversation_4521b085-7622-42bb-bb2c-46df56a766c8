// 导航栏组件
function createNavbar(currentPage = '') {
    const navbar = document.createElement('div');
    navbar.className = 'top-navbar';
    /**
     *  <a href="index.html" class="nav-link ${currentPage === 'home' ? 'active' : ''}">
                    <span class="nav-icon">🏠</span>
                    <span class="nav-text">首页</span>
                </a>
     */
    navbar.innerHTML = `
        <div class="navbar-container">
            <div class="navbar-brand">
                <span class="brand-icon">📊</span>
                <span class="brand-text">销售漏斗管理系统</span>
            </div>
            <nav class="navbar-nav">
                <a href="sales-dashboard.html" class="nav-link ${currentPage === 'sales' ? 'active' : ''}">
                    <span class="nav-icon">📊</span>
                    <span class="nav-text">销售仪表板</span>
                </a>
                <a href="customer-detail.html" class="nav-link ${currentPage === 'customer' ? 'active' : ''}">
                    <span class="nav-icon">👥</span>
                    <span class="nav-text">客户详情</span>
                </a>
                <a href="organization.html" class="nav-link ${currentPage === 'organization' ? 'active' : ''}">
                    <span class="nav-icon">🏢</span>
                    <span class="nav-text">组织架构</span>
                </a>
                <a href="business-managers.html" class="nav-link ${currentPage === 'business-managers' ? 'active' : ''}">
                    <span class="nav-icon">👨‍💼</span>
                    <span class="nav-text">商务负责人</span>
                </a>
                <a href="payment-records.html" class="nav-link ${currentPage === 'payment-records' ? 'active' : ''}">
                    <span class="nav-icon">💰</span>
                    <span class="nav-text">回款数据</span>
                </a>
                <a href="qa.html" class="nav-link ${currentPage === 'qa' ? 'active' : ''}">
                    <span class="nav-icon">📚</span>
                    <span class="nav-text">Q&A知识库</span>
                </a>
            </nav>
           
        </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .top-navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 2000;
            margin-bottom: 2rem;
        }

        .navbar-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 2rem;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: white;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .brand-icon {
            font-size: 1.5rem;
        }

        .navbar-nav {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.2);
        }

        .nav-icon {
            font-size: 1rem;
        }

        .navbar-toggle {
            display: none;
            flex-direction: column;
            cursor: pointer;
            gap: 3px;
        }

        .navbar-toggle span {
            width: 25px;
            height: 3px;
            background: white;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .navbar-extras {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .customer-quick-select {
            padding: 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.9rem;
            min-width: 180px;
        }

        .customer-quick-select option {
            background: #667eea;
            color: white;
        }

        .customer-quick-select:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.2);
        }

        .navbar-user-info {
            display: flex;
            align-items: center;
        }

        .navbar-user-info .user-profile {
            padding: 0;
        }

        .navbar-user-info .user-details {
            align-items: flex-end;
        }

        .navbar-user-info .user-name {
            color: white;
            font-size: 0.9rem;
        }

        .navbar-user-info .user-role {
            background: rgba(255, 255, 255, 0.2);
            font-size: 0.75rem;
            padding: 0.2rem 0.6rem;
        }

        .navbar-user-info .user-customers {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.7rem;
        }

        .navbar-user-info .logout-btn {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
            margin-left: 0.75rem;
        }

        @media (max-width: 768px) {
            .navbar-container {
                padding: 1rem;
            }

            .navbar-nav {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                flex-direction: column;
                padding: 1rem;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            .navbar-nav.show {
                display: flex;
            }

            .navbar-toggle {
                display: flex;
            }

            .nav-link {
                padding: 0.75rem 1rem;
                border-radius: 5px;
            }

            .brand-text {
                display: none;
            }

            .navbar-extras {
                order: -1;
                margin-right: auto;
            }

            .customer-quick-select {
                min-width: 120px;
                font-size: 0.8rem;
            }
        }

        @media (max-width: 480px) {
            .navbar-container {
                padding: 0.75rem;
            }

            .navbar-brand {
                font-size: 1rem;
            }

            .brand-icon {
                font-size: 1.2rem;
            }
        }
    `;

    document.head.appendChild(style);
    return navbar;
}

// 切换移动端导航
function toggleMobileNav() {
    const nav = document.querySelector('.navbar-nav');
    nav.classList.toggle('show');
}

// 初始化导航栏
function initNavbar(currentPage = '') {
    const navbar = createNavbar(currentPage);
    document.body.insertBefore(navbar, document.body.firstChild);

    // 如果是客户详情页面，加载客户列表用于快速切换
    if (currentPage === 'customer') {
        loadCustomersForQuickSelect();
    }

    // 初始化导航栏用户信息
    initNavbarUserInfo();
}

// 初始化导航栏用户信息
function initNavbarUserInfo() {
    // 等待认证管理器加载完成
    setTimeout(() => {
        if (typeof authManager !== 'undefined' && authManager.isAuthenticated()) {
            const navbarUserInfo = document.querySelector('.navbar-user-info');
            if (navbarUserInfo) {
                navbarUserInfo.classList.add('user-info');
                authManager.displayUserInfo();
            }
        }
    }, 100);
}

// 加载客户列表用于快速切换
async function loadCustomersForQuickSelect() {
    try {
        const response = await fetch('/api/customers');
        const result = await response.json();

        if (result.success) {
            const select = document.getElementById('customerQuickSelect');
            if (select) {
                select.innerHTML = '<option value="">快速切换客户...</option>';

                result.data.forEach(customer => {
                    const option = document.createElement('option');
                    option.value = customer.customerName;
                    option.textContent = `${customer.customerName} (${customer.region})`;
                    select.appendChild(option);
                });

                // 显示选择器
                select.style.display = 'block';

                // 设置当前客户为选中状态
                const urlParams = new URLSearchParams(window.location.search);
                const currentCustomer = urlParams.get('customer');
                if (currentCustomer) {
                    select.value = currentCustomer;
                }
            }
        }
    } catch (error) {
        console.error('加载客户列表失败:', error);
    }
}

// 快速切换客户
function quickSwitchCustomer() {
    const select = document.getElementById('customerQuickSelect');
    const selectedCustomer = select.value;

    if (selectedCustomer) {
        window.location.href = `customer-detail.html?customer=${encodeURIComponent(selectedCustomer)}`;
    }
}

// 自动检测当前页面并初始化导航栏
document.addEventListener('DOMContentLoaded', function() {
    const path = window.location.pathname;
    let currentPage = '';

    if (path.includes('sales-dashboard')) {
        currentPage = 'sales';
    } else if (path.includes('customer-detail')) {
        currentPage = 'customer';
    } else if (path.includes('organization')) {
        currentPage = 'organization';
    } else if (path.includes('business-managers')) {
        currentPage = 'business-managers';
    } else if (path.includes('payment-records')) {
        currentPage = 'payment-records';
    } else if (path.includes('qa')) {
        currentPage = 'qa';
    } else if (path.includes('index') || path === '/') {
        currentPage = 'home';
    }

    initNavbar(currentPage);
});
