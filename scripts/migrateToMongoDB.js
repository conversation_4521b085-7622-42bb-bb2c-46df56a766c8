const dataManager = require('../utils/dataManager');
const mongoManager = require('../utils/mongoManager');

async function migrateData() {
    try {
        console.log('🔄 开始数据迁移：JSON文件 → MongoDB');
        console.log('=' .repeat(50));
        
        // 连接到MongoDB
        await mongoManager.connect();
        
        // 执行数据迁移
        const success = await mongoManager.migrateFromJSON(dataManager);
        
        if (success) {
            console.log('=' .repeat(50));
            console.log('✅ 数据迁移完成！');
            console.log('📊 所有数据已成功从JSON文件迁移到MongoDB');
            console.log('🔗 MongoDB连接信息: 127.0.0.1:27017/up');
            console.log('📝 建议：备份原始JSON文件以防万一');
        } else {
            console.log('❌ 数据迁移失败！');
        }
        
    } catch (error) {
        console.error('❌ 迁移过程中发生错误:', error.message);
        console.error('详细错误:', error);
    } finally {
        // 关闭MongoDB连接
        await mongoManager.disconnect();
        process.exit(0);
    }
}

// 运行迁移
migrateData();
