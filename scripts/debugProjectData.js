const mongoManager = require('../utils/mongoManager');

async function debugProjectData() {
    try {
        console.log('🔍 调试项目数据结构...');
        
        // 设置生产环境
        process.env.NODE_ENV = 'production';
        
        // 连接生产环境MongoDB
        await mongoManager.connect();
        
        // 加载数据
        const customerData = await mongoManager.loadCustomerData();
        
        console.log(`👥 客户数据: ${Object.keys(customerData).length} 个`);
        
        // 查找有项目数据的客户
        let totalProjects = 0;
        let customersWithProjects = 0;
        
        Object.keys(customerData).forEach(customerName => {
            const customer = customerData[customerName];
            if (customer.projects && customer.projects.length > 0) {
                customersWithProjects++;
                totalProjects += customer.projects.length;
                
                // 显示前几个客户的项目数据结构
                if (customersWithProjects <= 3) {
                    console.log(`\n📋 客户: ${customerName} (${customer.projects.length} 个项目)`);
                    
                    customer.projects.slice(0, 2).forEach((project, index) => {
                        console.log(`   项目 ${index + 1}: ${project.projectName || '未命名'}`);
                        console.log(`     - 项目阶段: ${project.projectStage || '未设置'}`);
                        console.log(`     - 预期收入: ${project.expectedRevenue || '未设置'}`);
                        console.log(`     - 回款金额: ${project.paymentAmount || '未设置'}`);
                        console.log(`     - 回款时间: ${project.paymentTime || '未设置'}`);
                        console.log(`     - 签约时间: ${project.actualSignTime || '未设置'}`);
                        console.log(`     - 开票时间: ${project.invoiceTime || '未设置'}`);
                        console.log(`     - 商谈招标结束时间: ${project.negotiationBiddingEndTime || '未设置'}`);
                        console.log(`     - 创建时间: ${project.createdAt || '未设置'}`);
                        console.log(`     - 更新时间: ${project.updatedAt || '未设置'}`);
                    });
                }
            }
        });
        
        console.log(`\n📊 总计: ${customersWithProjects} 个客户有项目数据，共 ${totalProjects} 个项目`);
        
        // 检查时间字段的格式
        console.log('\n🕐 检查时间字段格式...');
        let timeFieldSamples = {
            paymentTime: [],
            actualSignTime: [],
            invoiceTime: [],
            negotiationBiddingEndTime: []
        };
        
        Object.values(customerData).forEach(customer => {
            if (customer.projects) {
                customer.projects.forEach(project => {
                    if (project.paymentTime && timeFieldSamples.paymentTime.length < 3) {
                        timeFieldSamples.paymentTime.push(project.paymentTime);
                    }
                    if (project.actualSignTime && timeFieldSamples.actualSignTime.length < 3) {
                        timeFieldSamples.actualSignTime.push(project.actualSignTime);
                    }
                    if (project.invoiceTime && timeFieldSamples.invoiceTime.length < 3) {
                        timeFieldSamples.invoiceTime.push(project.invoiceTime);
                    }
                    if (project.negotiationBiddingEndTime && timeFieldSamples.negotiationBiddingEndTime.length < 3) {
                        timeFieldSamples.negotiationBiddingEndTime.push(project.negotiationBiddingEndTime);
                    }
                });
            }
        });
        
        Object.keys(timeFieldSamples).forEach(field => {
            console.log(`   ${field}: ${timeFieldSamples[field].length > 0 ? timeFieldSamples[field].join(', ') : '无数据'}`);
        });
        
        // 检查最近的时间数据
        const now = new Date();
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        
        console.log('\n📅 时间范围检查:');
        console.log(`   当前时间: ${now.toISOString()}`);
        console.log(`   一周前: ${oneWeekAgo.toISOString()}`);
        console.log(`   一月前: ${oneMonthAgo.toISOString()}`);
        
        // 统计各时间段的数据
        let recentData = {
            recentPayments: 0,
            recentContracts: 0,
            recentInvoices: 0,
            recentNegotiations: 0
        };
        
        Object.values(customerData).forEach(customer => {
            if (customer.projects) {
                customer.projects.forEach(project => {
                    if (project.paymentTime) {
                        const paymentDate = new Date(project.paymentTime);
                        if (paymentDate >= oneMonthAgo) recentData.recentPayments++;
                    }
                    if (project.actualSignTime) {
                        const contractDate = new Date(project.actualSignTime);
                        if (contractDate >= oneMonthAgo) recentData.recentContracts++;
                    }
                    if (project.invoiceTime) {
                        const invoiceDate = new Date(project.invoiceTime);
                        if (invoiceDate >= oneMonthAgo) recentData.recentInvoices++;
                    }
                    if (project.negotiationBiddingEndTime) {
                        const negotiationDate = new Date(project.negotiationBiddingEndTime);
                        if (negotiationDate >= oneMonthAgo) recentData.recentNegotiations++;
                    }
                });
            }
        });
        
        console.log('\n📈 最近一个月的数据统计:');
        console.log(`   回款记录: ${recentData.recentPayments} 条`);
        console.log(`   签约记录: ${recentData.recentContracts} 条`);
        console.log(`   开票记录: ${recentData.recentInvoices} 条`);
        console.log(`   商谈结束记录: ${recentData.recentNegotiations} 条`);
        
        process.exit(0);
    } catch (error) {
        console.error('❌ 调试失败:', error);
        console.error('错误详情:', error.stack);
        process.exit(1);
    }
}

debugProjectData();
