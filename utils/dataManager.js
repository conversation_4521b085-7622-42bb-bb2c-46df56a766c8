const fs = require('fs');
const path = require('path');

class DataManager {
    constructor() {
        this.salesDataPath = path.join(__dirname, '..', 'data', 'sales_data.json');
        this.customerDataPath = path.join(__dirname, '..', 'data', 'customer_data.json');
        this.organizationDataPath = path.join(__dirname, '..', 'data', 'organization_data.json');
        this.businessManagersPath = path.join(__dirname, '..', 'data', 'business_managers.json');
        this.paymentRecordsPath = path.join(__dirname, '..', 'data', 'payment_records.json');
        this.executionPlansPath = path.join(__dirname, '..', 'data', 'execution_plans.json');
        this.dataDir = path.join(__dirname, '..', 'data');

        // 确保数据目录存在
        this.ensureDataDirectory();

        // 初始化数据
        this.initializeData();
    }
    
    // 确保数据目录存在
    ensureDataDirectory() {
        if (!fs.existsSync(this.dataDir)) {
            fs.mkdirSync(this.dataDir, { recursive: true });
        }
    }
    
    // 初始化数据（从scripts目录复制初始数据）
    initializeData() {
        const originalSalesPath = path.join(__dirname, '..', 'scripts', 'processed_sales_data.json');
        const originalCustomerPath = path.join(__dirname, '..', 'scripts', 'all_customer_details.json');
        
        // 如果数据文件不存在，从原始文件复制
        if (!fs.existsSync(this.salesDataPath) && fs.existsSync(originalSalesPath)) {
            const salesData = fs.readFileSync(originalSalesPath, 'utf8');
            fs.writeFileSync(this.salesDataPath, salesData);
            console.log('初始化销售数据文件');
        }
        
        if (!fs.existsSync(this.customerDataPath) && fs.existsSync(originalCustomerPath)) {
            const customerData = fs.readFileSync(originalCustomerPath, 'utf8');
            fs.writeFileSync(this.customerDataPath, customerData);
            console.log('初始化客户数据文件');
        }
    }
    
    // 读取销售数据
    loadSalesData() {
        try {
            if (fs.existsSync(this.salesDataPath)) {
                const data = fs.readFileSync(this.salesDataPath, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error('读取销售数据失败:', error.message);
            return [];
        }
    }
    
    // 保存销售数据
    saveSalesData(data) {
        try {
            fs.writeFileSync(this.salesDataPath, JSON.stringify(data, null, 2));
            console.log('销售数据已保存');
            return true;
        } catch (error) {
            console.error('保存销售数据失败:', error.message);
            return false;
        }
    }
    
    // 读取客户数据
    loadCustomerData() {
        try {
            if (fs.existsSync(this.customerDataPath)) {
                const data = fs.readFileSync(this.customerDataPath, 'utf8');
                return JSON.parse(data);
            }
            return {};
        } catch (error) {
            console.error('读取客户数据失败:', error.message);
            return {};
        }
    }
    
    // 保存客户数据
    saveCustomerData(data) {
        try {
            fs.writeFileSync(this.customerDataPath, JSON.stringify(data, null, 2));
            console.log('客户数据已保存');
            return true;
        } catch (error) {
            console.error('保存客户数据失败:', error.message);
            return false;
        }
    }

    // 读取组织架构数据
    loadOrganizationData() {
        try {
            if (fs.existsSync(this.organizationDataPath)) {
                const data = fs.readFileSync(this.organizationDataPath, 'utf8');
                return JSON.parse(data);
            }
            return {};
        } catch (error) {
            console.error('读取组织架构数据失败:', error.message);
            return {};
        }
    }

    // 保存组织架构数据
    saveOrganizationData(data) {
        try {
            fs.writeFileSync(this.organizationDataPath, JSON.stringify(data, null, 2));
            console.log('组织架构数据已保存');
            return true;
        } catch (error) {
            console.error('保存组织架构数据失败:', error.message);
            return false;
        }
    }

    // 读取商务负责人数据
    loadBusinessManagersData() {
        try {
            if (fs.existsSync(this.businessManagersPath)) {
                const data = fs.readFileSync(this.businessManagersPath, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error('读取商务负责人数据失败:', error.message);
            return [];
        }
    }

    // 保存商务负责人数据
    saveBusinessManagersData(data) {
        try {
            fs.writeFileSync(this.businessManagersPath, JSON.stringify(data, null, 2));
            console.log('商务负责人数据已保存');
            return true;
        } catch (error) {
            console.error('保存商务负责人数据失败:', error.message);
            return false;
        }
    }

    // 读取回款记录数据
    loadPaymentRecordsData() {
        try {
            if (fs.existsSync(this.paymentRecordsPath)) {
                const data = fs.readFileSync(this.paymentRecordsPath, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error('读取回款记录数据失败:', error.message);
            return [];
        }
    }

    // 保存回款记录数据
    savePaymentRecordsData(data) {
        try {
            fs.writeFileSync(this.paymentRecordsPath, JSON.stringify(data, null, 2));
            console.log('回款记录数据已保存');
            return true;
        } catch (error) {
            console.error('保存回款记录数据失败:', error.message);
            return false;
        }
    }

    // 读取执行计划数据
    loadExecutionPlansData() {
        try {
            if (fs.existsSync(this.executionPlansPath)) {
                const data = fs.readFileSync(this.executionPlansPath, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error('读取执行计划数据失败:', error.message);
            return [];
        }
    }

    // 保存执行计划数据
    saveExecutionPlansData(data) {
        try {
            fs.writeFileSync(this.executionPlansPath, JSON.stringify(data, null, 2));
            console.log('执行计划数据已保存');
            return true;
        } catch (error) {
            console.error('保存执行计划数据失败:', error.message);
            return false;
        }
    }

    // 读取Q&A数据
    loadQAData() {
        try {
            const qaDataPath = path.join(this.dataDir, 'qa_data.json');
            if (fs.existsSync(qaDataPath)) {
                const data = fs.readFileSync(qaDataPath, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error('读取Q&A数据失败:', error.message);
            return [];
        }
    }

    // 保存Q&A数据
    saveQAData(data) {
        try {
            const qaDataPath = path.join(this.dataDir, 'qa_data.json');
            fs.writeFileSync(qaDataPath, JSON.stringify(data, null, 2));
            console.log('Q&A数据已保存');
            return true;
        } catch (error) {
            console.error('保存Q&A数据失败:', error.message);
            return false;
        }
    }

    // 读取Q&A数据
    loadQAData() {
        try {
            const qaDataPath = path.join(this.dataDir, 'qa_data.json');
            if (fs.existsSync(qaDataPath)) {
                const data = fs.readFileSync(qaDataPath, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error('读取Q&A数据失败:', error.message);
            return [];
        }
    }

    // 保存Q&A数据
    saveQAData(data) {
        try {
            const qaDataPath = path.join(this.dataDir, 'qa_data.json');
            fs.writeFileSync(qaDataPath, JSON.stringify(data, null, 2));
            console.log('Q&A数据已保存');
            return true;
        } catch (error) {
            console.error('保存Q&A数据失败:', error.message);
            return false;
        }
    }


    // 备份数据
    backupData() {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupDir = path.join(this.dataDir, 'backups');
            
            if (!fs.existsSync(backupDir)) {
                fs.mkdirSync(backupDir, { recursive: true });
            }
            
            // 备份销售数据
            if (fs.existsSync(this.salesDataPath)) {
                const backupSalesPath = path.join(backupDir, `sales_data_${timestamp}.json`);
                fs.copyFileSync(this.salesDataPath, backupSalesPath);
            }
            
            // 备份客户数据
            if (fs.existsSync(this.customerDataPath)) {
                const backupCustomerPath = path.join(backupDir, `customer_data_${timestamp}.json`);
                fs.copyFileSync(this.customerDataPath, backupCustomerPath);
            }

            // 备份组织架构数据
            if (fs.existsSync(this.organizationDataPath)) {
                const backupOrgPath = path.join(backupDir, `organization_data_${timestamp}.json`);
                fs.copyFileSync(this.organizationDataPath, backupOrgPath);
            }

            // 备份商务负责人数据
            if (fs.existsSync(this.businessManagersPath)) {
                const backupManagersPath = path.join(backupDir, `business_managers_${timestamp}.json`);
                fs.copyFileSync(this.businessManagersPath, backupManagersPath);
            }

            // 备份回款记录数据
            if (fs.existsSync(this.paymentRecordsPath)) {
                const backupPaymentPath = path.join(backupDir, `payment_records_${timestamp}.json`);
                fs.copyFileSync(this.paymentRecordsPath, backupPaymentPath);
            }

            console.log(`数据备份完成: ${timestamp}`);
            return true;
        } catch (error) {
            console.error('数据备份失败:', error.message);
            return false;
        }
    }
    
    // 重置数据到初始状态
    resetData() {
        try {
            // 先备份当前数据
            this.backupData();
            
            // 重新初始化数据
            if (fs.existsSync(this.salesDataPath)) {
                fs.unlinkSync(this.salesDataPath);
            }
            if (fs.existsSync(this.customerDataPath)) {
                fs.unlinkSync(this.customerDataPath);
            }
            if (fs.existsSync(this.organizationDataPath)) {
                fs.unlinkSync(this.organizationDataPath);
            }
            if (fs.existsSync(this.businessManagersPath)) {
                fs.unlinkSync(this.businessManagersPath);
            }
            if (fs.existsSync(this.paymentRecordsPath)) {
                fs.unlinkSync(this.paymentRecordsPath);
            }

            this.initializeData();
            console.log('数据已重置到初始状态');
            return true;
        } catch (error) {
            console.error('重置数据失败:', error.message);
            return false;
        }
    }
}

module.exports = new DataManager();
